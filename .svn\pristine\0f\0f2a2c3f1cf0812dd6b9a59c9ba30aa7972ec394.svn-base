<template>
<div>
    <CommonDialog class="personal_setting"
        :title="$t('personal_setting_title')"
        :show.sync="visible"
        :close-on-click-modal="false"
        width="800px"
        :modal="false"
        @closed="handleClose"
        :footShow="false"
    >
        <el-tabs v-model="activeName" @tab-click="handleClick" :key="activeName">
            <el-tab-pane :label="$t('modify_basic_info_text')" name="first" v-if="!forceEnhancePassword">
                <div class="basic_info">
                    <div class="item">
                        <p class="title">{{$t('register_account')}}：</p>
                        <div class="item_content">
                            <el-input v-model="account" class="login_name item_content_left" maxlength="16" :disabled="true"></el-input>
                            <el-button @click="resetLoginName" class="reset_login_name item_content_right" type="primary">{{$t('edit_txt')}}</el-button>
                        </div>

                    </div>
                    <div class="item">
                        <p class="title">{{$t('nickname')}}：</p>
                        <div class="item_content">
                            <el-input v-model="nickname" maxlength="50" class="item_content_left"></el-input>
                            <div class="item_content_right"></div>
                        </div>
                    </div>
                    <div class="item" v-if="!isInternalNetworkEnv">
                        <p class="title">{{$t('register_mobile')}}：</p>
                        <div class="item_content">
                            <el-input v-model="mobileShow" class="mobile_phone item_content_left" :disabled="true"></el-input>
                            <el-button @click="modifyBinding(1)" class="reset_mobile item_content_right" type="primary">{{$t('edit_txt')}}</el-button>
                        </div>
                    </div>
                    <div class="item" v-if="!isInternalNetworkEnv">
                        <p class="title">{{$t('register_email')}}：</p>
                        <div class="item_content">
                            <el-input v-model="emailShow" class="mobile_phone item_content_left" :disabled="true"></el-input>
                            <el-button @click="modifyBinding(2)" class="reset_mobile item_content_right" type="primary">{{$t('edit_txt')}}</el-button>
                        </div>
                    </div>
                    <div class="item">
                        <p class="title">{{$t('personal_profile')}}：</p>
                        <div class="item_content">
                            <el-input v-model="user.introduction" class="mobile_phone item_content_left" :disabled="true" :placeholder="$t('personal_profile_tips')"></el-input>
                            <el-button @click="modifyPersonProfile" class="reset_mobile item_content_right" type="primary">{{$t('edit_txt')}}</el-button>
                        </div>
                    </div>
                    <div class="item">
                        <p class="title">{{$t('organization_name')}}：</p>
                        <div class="item_content">
                            <el-input v-model="user.organizationName" class="mobile_phone item_content_left" :disabled="true"></el-input>
                            <el-button @click="modifyOrganization" class="reset_mobile item_content_right" type="primary">{{$t('edit_txt')}}</el-button>
                        </div>
                    </div>
                    <!-- 职业身份设置已屏蔽 -->
                    <!-- <div class="item" v-permission="{regionPermissionKey:'professionalIdentityForce'}">
                        <p class="title">{{ $t('professional_identity_title') }}：</p>
                        <div class="item_content">
                            <el-select v-model="professionalIdentity" :placeholder="$t('input_select_tips')" class="item_content_left">
                                <el-option
                                    v-for="item in professionalIdentityOptions"
                                    :key="item.value"
                                    :label="item.label"
                                    :value="item.value"
                                >
                                </el-option>
                            </el-select>
                            <div class="item_content_right"></div>
                        </div>
                    </div> -->
                    <!-- <div class="item">
                        <p class="title">{{ $t('scan_room_hospital') }}：</p>
                        <el-select v-model="hospital_id" filterable allow-create :placeholder="$t('select_hospital')" @change="hospitalChange">
                            <el-option v-for="hospital in dynamicGlobalParams.hospitals" :label="hospital.hospital_name" :value="hospital.id" :key="hospital.id"></el-option>
                        </el-select>
                    </div> -->
                    <div class="item">
                        <p class="title">{{$t('register_sex')}}：</p>
                        <div class="item_content">
                            <el-select v-model="sex" class="item_content_left">
                                <el-option :label="$t('male')" :value="1"></el-option>
                                <el-option :label="$t('female')" :value="0"></el-option>
                            </el-select>
                            <div class="item_content_right"></div>
                        </div>
                    </div>
                </div>
                <div class="submit">
                    <el-button @click="modify_current_basic_info" type="primary" size="medium">{{$t('submit_btn')}}</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane :label="$t('modify_password_text')" name="second">
                <div class="password">
                    <div class="item" v-show="0 != user.is_password_privatized">
                        <p class="title">{{$t('modify_old_password_label')}}：</p>
                        <el-input type="password" v-model="old_password" maxlength="16" :placeholder="$t('modify_old_password_input')"></el-input>
                    </div>
                    <div class="item">
                        <p class="title">{{$t('modify_new_password_label')}}：</p>
                        <el-input type="password" v-model="new_password" maxlength="16" :placeholder="$t('modify_new_password_input')"></el-input>
                    </div>
                    <div class="item">
                        <p class="title">{{$t('register_confirm_password')}}：</p>
                        <el-input type="password" v-model="new_confirm_password" maxlength="16" :placeholder="$t('modify_confirm_password_input')"></el-input>
                    </div>
                </div>
                <div class="submit">
                    <el-button @click="modify_current_password" type="primary" size="medium">{{$t('submit_btn')}}</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane :label="$t('modify_photo_text')" name="third" v-if="!forceEnhancePassword">
                <div class="change_avatar">
                    <el-button @click="choosePhoto" class="" type="primary" size="medium">{{$t('choose_file')}}</el-button>
                    <input type="file" accept="image/*" ref="fileRef" class="fileRef" @change="changeFile">
                    <vue-cropper v-show="showCropper"
                    ref="cropper"
                    :img="uploadedImageURL"
                    :autoCrop="true"
                    :autoCropWidth="200"
                    :autoCropHeight="200"
                    :fixedBox="true"
                    ></vue-cropper>
                </div>
                <div class="submit">
                    <el-button v-show="showCropper" @click="submitAvatar" type="primary" size="medium">{{$t('confirm_txt')}}</el-button>
                </div>

            </el-tab-pane>
            <el-tab-pane :label="$t('other_setting_text')" name="fourth" v-if="!forceEnhancePassword">
                <div class="other_settings">
                    <div class="setting_item">
                        <span>{{$t('desensitization_reception')}}：</span>
                        <el-switch v-model="isDesensitization" @change="toggleDesensitization"></el-switch>
                    </div>
                    <el-button @click="cancelAccount" class="" type="primary" size="medium">{{$t('cancel_account')}}</el-button>
                </div>
            </el-tab-pane>
            <el-tab-pane :label="$t('referral_code')" name="fifth" v-if="!forceEnhancePassword&&isShowReferral">
                <div class="password">
                    <div class="item">
                        <p class="title">{{$t('referral_code')}}：</p>
                        <el-input type="text" v-model="referral_code" maxlength="6" :placeholder="$t('referral_code')"></el-input>
                    </div>

                </div>
                <div class="submit">
                    <el-button v-loading="binding" @click="bindReferralCode" type="primary" size="medium">{{$t('submit_btn')}}</el-button>
                </div>
            </el-tab-pane>
        </el-tabs>
        <two-factor-authentication
            :isShowVerify.sync="isShowVerify"
            :cellphone="cancelMobilePhone"
            :email="cancelEmail"
            :token="token"
            :isLoading="isLoading"
            verifyCodeType="destroyUser"
            :verifyCallback="logoffAction"
            ref="twoFactorAuthentication"
            ></two-factor-authentication>
    <!-- </el-dialog> -->
    </CommonDialog>
    <PersonalProfileSetting v-model="showPersonalProfileSetting" @personalProfileSettingSubmit="personalProfileSettingSubmit"></PersonalProfileSetting>
</div>

</template>
<script>
import base from '../lib/base'
import service from '../service/service'
import {VueCropper} from 'vue-cropper'
import {parseImageListToLocal} from '../lib/common_base'
import twoFactorAuthentication from '../components/twoFactorAuthentication.vue'
import PersonalProfileSetting from '../components/personalProfileSetting.vue'
import {
    accountRegExp1,
    nicknameRegExp,
    passwordStrengthRegExp,
} from '@/common/regExpMapping.js'
import CommonDialog from '../MRComponents/commonDialog.vue'
// 职业身份相关导入已屏蔽
// import { PROFESSIONAL_IDENTITY } from "../lib/constants.js"
export default {
    mixins: [base],
    name: 'PersonalSetting',
    model: {
        prop: 'value',
        event: 'input'
    },
    permission: true,
    props: {
        value: {
            type: Boolean,
            default: false
        }
    },
    components: {
        twoFactorAuthentication,
        CommonDialog,
        VueCropper,
        PersonalProfileSetting
    },
    data(){
        return {
            activeName:'first',
            preActive:'first',
            account:'',
            nickname:'',
            mobile_phone:'',
            email:'',
            sex:1,
            password:'',
            old_password:'',
            new_password:'',
            new_confirm_password:'',
            // file_input:'',
            uploadedImageURL:'',
            showCropper:false,
            uploading:false,
            hospital_id:undefined,
            international_code: '',
            force_enhance_password:false,
            isShowVerify:false,
            cancelMobilePhone:'',
            cancelEmail:'',
            token:'',
            isDesensitization:false,
            isLoading:false,
            referral_code:'',
            binding:false,
            showPersonalProfileSetting:false,
            // 职业身份相关数据已屏蔽
            // professionalIdentity: null,
            // professionalIdentityOptions: []
        }
    },
    computed:{
        visible: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        },
        forceEnhancePassword () {
            return this.force_enhance_password;
        },
        mobileShow() {
            if (this.mobile_phone==='') {
                return ''
            }
            return '+'+this.international_code+' '+this.secretifyMobile(this.mobile_phone)
        },
        emailShow(){
            return this.secretifyEmail(this.email);
        },
        serverInfo() {
            return this.systemConfig.serverInfo
        },
        isNeedIdentify() {
            return this.serverInfo.enable_sms_identification;
        },
        isShowReferral(){
            return this.$checkPermission({regionPermissionKey:'referralCode'})&&!!this.user.probationary_expiry;
        },
        isInternalNetworkEnv(){
            return this.systemConfig.serverInfo.network_environment
        },
    },
    mounted(){
        this.$nextTick(()=>{
            var that = this;
            this.isDesensitization=this.user.preferences.is_desensitization==1?true:false;
            this.account=this.user.username;
            this.nickname=this.user.nickname;
            this.email=this.user.email;
            if(this.hospitalIsExist(this.user.hospital_id)){
                this.hospital_id=this.user.hospital_id
            }

            this.mobile_phone=this.user.mobile_phone;
            this.international_code=this.user.international_code;
            this.sex=this.user.sex;
            // this.file_input=this.$refs.fileRef;

            // 职业身份初始化已屏蔽
            // this.professionalIdentityOptions = [
            //     { value: PROFESSIONAL_IDENTITY.PHYSICIAN, label: this.$t('professional_identity.physician') },
            //     { value: PROFESSIONAL_IDENTITY.SONOGRAPHER, label: this.$t('professional_identity.sonographer') },
            //     { value: PROFESSIONAL_IDENTITY.ADMINISTRATOR, label: this.$t('professional_identity.administrator') },
            //     { value: PROFESSIONAL_IDENTITY.RESEARCHER, label: this.$t('professional_identity.researcher') },
            //     { value: PROFESSIONAL_IDENTITY.TECHNICAL_SUPPORT, label: this.$t('professional_identity.technical_support') },
            //     { value: PROFESSIONAL_IDENTITY.OTHER, label: this.$t('professional_identity.other') }
            // ];

            // this.professionalIdentity = this.user.professional_identity || null;

            this.$root.eventBus.$off('reset_mobile').$on('reset_mobile',function (data) {
                if (data.accountType=='mobile') {
                    that.mobile_phone = data.mobile_phone;
                    that.international_code = data.international_code;
                    that.$store.commit('user/updateUser',{
                        international_code:data.international_code,
                        mobile_phone:that.secretifyMobile(data.mobile_phone)
                    });
                    that.$message.success(that.$t('reset_mobile_success'))
                }else if (data.accountType=='email') {
                    that.email = data.email;
                    that.$store.commit('user/updateUser',{
                        email:that.secretifyEmail(data.email)
                    });
                    that.$message.success(that.$t('reset_email_success'))
                }
            });

            this.$root.eventBus.$off('reset_login_name').$on('reset_login_name',function (data) {
                that.account = data.login_name;
                that.$store.commit('user/updateUser',{
                    username:data.login_name,
                    name:data.login_name
                });
                that.$message.success(that.$t('reset_login_name_success'))
            });


            // 移除路由相关的逻辑，因为现在是弹窗组件

        })
    },
    methods:{
        async modify_current_basic_info(){
            // var accountRegexp=/^[a-zA-Z0-9][_a-zA-Z0-9]{3,15}$/;
            // var accountRegexp2=/^[0-9]+$/;
            // var regexp = /^1[0-9]{10}$/;
            // var nicknameRegexp = /[~!|@#$%^&*?,./\\()_+"'{}{}·“”‘！@#￥%……&*（）—=`【】？《》<>，。、 ]/gi;
            if (!accountRegExp1.test(this.account) ){
                this.$message.error(this.$t('account_format_tip'));
            }else if (this.nickname == ''){
                this.$message.error(this.$t('nickname_should_not_be_empty'));
            // }else if (nicknameRegExp.test(this.nickname)){
            //     this.$message.error(this.$t('nickname_cannot_contain_special_character'));
            // }else if (0 < this.mobile_phone.length && !regexp.test(this.mobile_phone)){
            //     this.$message.error(this.$t('mobile_number_is_invalid_input_again'));
            }else{
                var that = this;
                var input_data = {};
                var is_edit = false;
                // if (that.user.username != that.account) {
                //     input_data.username = that.account;
                //     is_edit = true;
                // }
                if (that.user.nickname != that.nickname) {
                    input_data.nickname = that.nickname;
                    is_edit = true;
                }
                // if (that.user.mobile_phone != that.mobile_phone) {
                //     input_data.mobile_phone = that.mobile_phone;
                //     is_edit = true;
                // }
                // if (that.user.international_code != that.international_code) {
                //     input_data.international_code = that.international_code;
                //     is_edit = true;
                // }

                // 先判断hospital_id是Number类型（选择已有医院）还是string类型（添加自定义医院）
                if(typeof(that.hospital_id)=="number"){
                    if (that.user.hospital_id != that.hospital_id){
                        input_data.hospital = that.hospital_id;
                        is_edit = true;
                    }
                }else if(typeof(that.hospital_id)=="string"){
                    //发送请求获取hospital_id
                    var data = {
                        name:that.hospital_id
                    };
                    const hospital_id = await that.createHospitalByName(data);
                    if(hospital_id){
                        input_data.hospital = hospital_id;
                        is_edit = true;

                        //添加成功后要插入globalParams.hospitals里
                        var newHospital = {
                            hospital_name: that.hospital_id,
                            id: hospital_id,
                            city_id: 0,
                            country_id: 0,
                            latitude: 0,
                            longitude: 0,
                            postcode: 0,
                            province_id: 0,
                            region: ""
                        }
                        that.$store.commit("dynamicGlobalParams/addHospital",newHospital)
                    }
                }


                if (that.user.sex != that.sex) {
                    input_data.sex = that.sex;
                    is_edit = true;
                }

                // 职业身份变化检查已屏蔽
                // let professionalIdentityChanged = false;
                // if (that.professionalIdentity !== that.user.professional_identity) {
                //     professionalIdentityChanged = true;
                // }

                if (!is_edit) {
                    that.back();
                }else{
                    window.main_screen.commitUserBasicInfoModify(input_data, function(is_succ, information){
                        if(is_succ){
                            if (input_data.hospital) {
                                input_data.hospital_id=input_data.hospital;
                                for(let hospital of that.dynamicGlobalParams.hospitals){
                                    if (input_data.hospital_id==hospital.id) {
                                        input_data.hospital_name=hospital.hospital_name;
                                        break;
                                    }
                                }
                            }
                            input_data.id=that.user.uid;
                            that.changeDefaultImg(input_data);
                            that.$store.commit('user/updateUser',input_data)
                            if (input_data.username) {
                                window.localStorage.setItem('account',input_data.username)
                            }
                            that.$message.success(that.$t('modify_basic_info_success'));
                            that.$store.commit('chatList/updateFriendToChatList',that.user)
                            that.$store.commit('conversationList/updateFriendToConversationList',that.user)
                            if (input_data.nickname) {
                                //修改名称触发判断修改默认头像
                                that.$root.eventBus.$emit('ifCreateUserAvatar',1)
                            }

                            // 职业身份保存已屏蔽
                            // if (professionalIdentityChanged) {
                            //     that.saveProfessionalIdentity();
                            // }

                            that.closeSettingIfNeed();
                        }else{
                            if (information == "database_err") {
                                that.$message.error(that.$t('modify_password_fail_database_err'));
                            } else if (information == "name_repeated") {
                                that.$message.error(that.$t('personal_information_fail_name_repeated'));
                            } else if (information == "name_pure_numbers") {
                                that.$message.error(that.$t('personal_information_fail_name_pure_numbers'));
                            } else if (information == "email_repeated") {
                                that.$message.error(that.$t('personal_information_fail_email_repeated'));
                            } else if (information == "nickname_repeated") {
                                that.$message.error(that.$t('personal_information_fail_nickname_repeated'));
                            } else if (information == "mobile_phone_repeated") {
                                that.$message.error(that.$t('personal_information_fail_mobile_phone_repeated'));
                            }
                        }
                    })
                }
            }
        },
        hospitalChange(){
            if(typeof(this.hospital_id)=="string"){
                this.hospital_id = this.hospital_id.trim();//去除首尾空字符
                if(this.hospital_id.length==0){
                    this.$message.error(this.$t('hospital_name_length_limit_0'))
                    this.hospital_id = this.user.hospital_id
                }else if(this.hospital_id.length>32){
                    this.$message.error(this.$t('hospital_name_length_limit_32'))
                    this.hospital_id = this.user.hospital_id
                }
            }
        },
        createHospitalByName(data){
            return new Promise((resolve,reject)=>{
                window.main_screen.createHospitalByName(data,(res)=>{
                    if(!res.error_code){
                        resolve(res.data.id)
                    }else{
                        this.$message.error(res.error_msg);
                        reject(false)
                    }
                })
            })
        },
        modify_current_password(){
            // var passwordRegexp=/^[a-zA-Z0-9~!@#$%^&*()_+`\[\]\\\|\;\':",.\/<>\?\*]{6,16}$/
            // var passwordRegexpEnhanced=/^(?=.*[a-z])(?=.*[A-Z])(?=.*[0-9])[a-zA-Z0-9~!@#$%^&*()_+`\[\]\\\|\;\':",.\/<>\?\*]{8,16}$/
            if ((0 != this.user.is_password_privatized) && (this.old_password.length < 6 || this.old_password.length > 16)){
                this.$message.error(this.$t('password_length_not_correct'));
            }else if (!passwordStrengthRegExp.test(this.new_password)){
                this.$message.error(this.$t('enhanced_password_format_tip'));
            }else if (this.new_password != this.new_confirm_password){
                this.$message.error(this.$t('confirm_password_and_new_password_not_match'));
            }else{
                var tip = '';
                var that = this;
                var input_data = {
                    old_pwd:this.old_password,
                    new_pwd:this.new_password,
                    token:this.user.new_token,
                };
                window.main_screen.commitPasswordModify(input_data, function(result){
                    if(result.is_succ){
                        that.$message.success(that.$t('modify_password_success'));
                        that.new_password='';
                        that.new_confirm_password='';
                        that.old_password='';
                        that.closeSettingIfNeed();
                    }else{
                        if (result.information == "account_err") {
                            tip = that.$t('modify_password_fail_account_incorrect');
                        } else if (result.information == "old_pwd_err") {
                            tip = that.$t('modify_password_fail_password_incorrect');
                        } else if (result.information == "pwd_same") {
                            tip = that.$t('modify_password_fail_password_same');
                        } else if (result.information == "database_err") {
                            tip = that.$t('modify_password_fail_database_err');
                        } else if (result.information == "password_format_error") {
                            tip = that.$t('password_format_tip');
                        } else if (result.information == "enhanced_password_format_error") {
                            tip = that.$t('enhanced_password_format_tip');
                        }
                        that.$message.error(tip);
                    }
                })
            }
        },
        choosePhoto(){
            this.$refs.fileRef.click();
        },
        changeFile(){
            this.showCropper=true;
            var URL = window.URL || window.webkitURL;
            var files = this.$refs.fileRef.files;
            if (files && files.length) {
                let file = files[0];
                if (/^image\/\w+$/.test(file.type)) {
                    let tempUrl=URL.createObjectURL(file)
                    this.checkImgExist(tempUrl).then(img => {
                        this.uploadedImageURL = tempUrl;
                        // 图片完整，加载成功，不做任何操作
                    }).catch(err => {
                        // 图片损坏，加载失败，撤回链接释放内存
                        URL.revokeObjectURL(this.uploadedImageURL)
                        this.$message(this.$t('image_corruption_text'))
                    })
                } else {
                    this.$message.error('Please choose an image file.');
                }
            }
        },
        // 检查图片完整性
        checkImgExist(src) {
            return new Promise((resolve, reject) => {
                const img = document.createElement('img')
                img.onload = () => {
                    resolve(img)
                }
                img.onerror = err => {
                    reject(err)
                }
                img.src = src
            })
        },
        secretifyMobile(mobile) {
            let mobileLength = mobile.length
            if(mobileLength > 0 && typeof mobile == 'string') {
                let secretLen = Math.floor(mobileLength / 2) - 1
                let secretStr = ''
                let restLen = Math.ceil(mobileLength / 2) + 1
                let aheadLen = Math.floor(restLen / 2)
                let afterLen = Math.ceil(restLen / 2)
                for(let i=0; i<secretLen; i++) {
                    secretStr += '*'
                }
                let replaceReg = new RegExp(`(\\d{${aheadLen}})(\\d{${secretLen}})(\\d{${afterLen}})`)
                return mobile.replace(replaceReg, `$1${secretStr}$3`)
            }else{
                return ''
            }
        },
        secretifyEmail(email){
            // 最多显示@前3个字符剩下的用***代替
            if (!email) {
                return ''
            }
            const [preStr, afterStr] = email.split("@");
            const showLength = Math.min(preStr.length, 3);
            return `${preStr.substr(0, showLength)}***@${afterStr}`;
        },
        submitAvatar(){
            var that=this;
            console.log(this.$refs.cropper.getCropData)
            this.$refs.cropper.getCropData((data)=>{
                that.uploading=true;
                var controller = window.main_screen.controller;
                var uid=this.user.uid;
                let img=new Image()
                img.src=data;
                img.onload=()=>{
                    const canvas=document.createElement('canvas')
                    canvas.width=200;
                    canvas.height=200;
                    const context=canvas.getContext('2d')
                    context.fillStyle="rgba(255,255,255,0)"
                    context.beginPath()
                    context.arc(100,100,100,0,Math.PI*2,false);
                    context.clip()
                    context.closePath()
                    context.drawImage(img,0,0,200,200)
                    let imageData=canvas.toDataURL('image/png');
                    controller.emit("set_user_portrait_img",{
                        uid:uid,
                        imageType:'image/png',
                        fileName:uid + '_' + new Date().getTime() + '.png',
                        file:imageData
                    },function(is_succ,path){
                        if("success" == is_succ){
                            let user={
                                id:that.user.uid,
                                avatar:path
                            }
                            parseImageListToLocal([user],'avatar')
                            that.$store.commit('user/updateUser',{
                                avatar:user.avatar,
                                avatar_local:user.avatar_local
                            })
                            that.$store.commit('conversationList/updateFriendToAttendeeList',{
                                avatar:path,
                                avatar_local:user.avatar_local,
                                id:uid,
                                state:that.user.state,
                                nickname:that.user.nickname
                            })
                            that.$message.success(that.$t('modify_photo_success'));
                            that.showCropper=false;
                            that.closeSettingIfNeed();
                        }else{
                            that.$message.error('set_user_portrait_img error')
                        }
                    });
                }
            })
        },

        hospitalIsExist(hospital_id){
            let exist=false
            for(let hospital of this.dynamicGlobalParams.hospitals){
                if (hospital.id==hospital_id) {
                    exist=true;
                    break
                }
            }
            return exist
        },
        handleClick(tab){
            if (tab.name!=this.preActive) {
                //切换了Tab
                if (this.preActive=='first') {
                    this.change=false;
                    if(this.user.username != this.account || this.nickname!=this.user.nickname||this.sex!=this.user.sex){
                        this.change=true;
                    }
                    // 职业身份变化检查已屏蔽
                    // if (this.professionalIdentity !== this.user.professional_identity) {
                    //     this.change = true;
                    // }
                    // if(this.hospital_id!=this.user.hospital_id){
                    //     this.change=true;
                    // }
                    if(typeof(this.hospital_id)=="number"){
                        if (this.user.hospital_id != this.hospital_id){
                            this.change = true;
                        }
                    }else if(typeof(this.hospital_id)=="string"){
                        this.change = true;
                    }

                    if (this.change) {
                        this.$confirm(this.$t('is_save_change'),{
                            confirmButtonText:this.$t('confirm_button_text'),
                            cancelButtonText:this.$t('cancel_button_text'),
                            type:'warning'})
                            .then(()=>{
                                this.modify_current_basic_info();
                            })
                            .catch(()=>{
                                this.account=this.user.username
                                this.nickname=this.user.nickname
                                this.hospital_id=this.user.hospital_id
                                this.sex=this.user.sex;
                                // this.professionalIdentity = this.user.professional_identity || null;
                            })
                    }
                }else if(this.preActive=='second'){
                    if (this.old_password||this.new_password||this.new_confirm_password) {
                        this.change=true;
                        this.$confirm(this.$t('is_save_change'),{
                            confirmButtonText:this.$t('confirm_button_text'),
                            cancelButtonText:this.$t('cancel_button_text'),
                            type:'warning'})
                            .then(()=>{
                                this.modify_current_password();
                            })
                            .catch(()=>{
                                this.old_password='';
                                this.new_password='';
                                this.new_confirm_password='';
                            })
                    }
                }else if(this.preActive=='third'){
                    if (this.showCropper) {
                        this.change=true;
                        this.$confirm(this.$t('is_save_change'),{
                            confirmButtonText:this.$t('confirm_button_text'),
                            cancelButtonText:this.$t('cancel_button_text'),
                            type:'warning'})
                            .then(()=>{
                                this.submitAvatar();
                            })
                            .catch(()=>{
                                this.showCropper=false
                            })
                    }
                }
            }
            this.preActive=tab.name;
        },
        closeSettingIfNeed(){
            if (!this.change) {
                this.back();
            }else{
                this.change=false
            }
        },
        modifyBinding(type){
            // TODO: 实现修改绑定功能的弹窗
            this.$message.info('修改绑定功能暂未实现');
        },
        resetLoginName(){
            // TODO: 实现重置登录名功能的弹窗
            this.$message.info('重置登录名功能暂未实现');
        },
        cancelAccount(){
            this.$confirm(this.$t('log_off_waring'),{
                confirmButtonText:this.$t('confirm_button_text'),
                cancelButtonText:this.$t('cancel_button_text'),
                type:'warning'})
                .then(()=>{
                    const bindedPhoneOrEmail=this.user.mobile_phone||this.user.email;
                    if (!this.isInternalNetworkEnv&&bindedPhoneOrEmail) {
                        if (this.user.mobile_phone) {
                            this.cancelMobilePhone=`+${this.user.international_code} ${this.user.mobile_phone}`;
                        }
                        this.cancelEmail=this.user.email;
                        this.token=this.user.new_token;
                        this.isShowVerify=true;
                        this.$nextTick(()=>{
                            this.$refs.twoFactorAuthentication.init();
                        })
                    }else{
                        this.logoffAction();
                    }

                })
                .catch(()=>{
                })
        },
        logoffAction(verifyCode,type){
            let params={
                token:this.user.new_token
            }
            if (verifyCode) {
                params.accountType=type;
                params.code=verifyCode;
            }
            this.isLoading=true;
            service.logoff(params).then(res => {
                this.isLoading=false;
                if (res.data.error_code===0) {
                    //notify_user_destroy
                }
            })
        },
        toggleDesensitization(){
            let isDesensitization=this.isDesensitization?1:0;
            const data={
                is_desensitization:isDesensitization
            }
            this.$root.socket.emit("set_user_other_info", data,(is_succ,info)=>{
                if (is_succ) {
                    let preferences=this.user.preferences;
                    preferences.is_desensitization=isDesensitization;
                    this.$store.commit('user/updateUser',{
                        preferences
                    })
                }else{
                    this.$message.error(this.$t('update_failed_text'))
                    this.isDesensitization=!this.isDesensitization;
                }
            });
        },
        bindReferralCode(){
            if (this.referral_code.length>0) {
                const token=this.user.new_token;
                this.binding = true;
                service.bindReferralCode({
                    token:token,
                    referralCode:this.referral_code
                }).then((res)=> {
                    this.binding=false;
                    if (res.data.error_code===0) {
                        this.$store.commit('user/updateUser',{
                            probationary_expiry:'',
                        });
                        this.$message.success(this.$t('referral_success_tip'))
                        this.back();
                    }
                })
            }
        },
        modifyOrganization(){
            // TODO: 实现修改组织功能的弹窗
            this.$message.info('修改组织功能暂未实现');
        },
        modifyPersonProfile(){
            // this.$router.push(this.$route.fullPath+'/personal_profile_setting');
            this.showPersonalProfileSetting = true
        },
        personalProfileSettingSubmit(){
            this.showPersonalProfileSetting = false
        },
        // 重写 back 方法，关闭弹窗而不是路由导航
        back() {
            this.visible = false;
        },
        handleClose(){
            // 关闭弹窗，通过设置 visible 为 false
            this.visible = false;
        },
        // 职业身份保存方法已屏蔽
        // saveProfessionalIdentity() {
        //     if (!this.professionalIdentity) {
        //         this.$message.warning(this.$t('please_choose_professional_identity'));
        //         return;
        //     }

        //     service.setProfessionalIdentity({
        //         professional_identity: this.professionalIdentity
        //     }).then((res) => {
        //         console.log("saveProfessionalIdentity:", res);
        //         if (res.data && res.data.error_code === 0) {
        //             // 更新用户信息
        //             this.$store.commit('user/updateUser', {
        //                 professional_identity: this.professionalIdentity
        //             });
        //         } else {
        //             this.$message.error(this.$t('operate_err'));
        //             // this.$message.error(res.data?.msg || '设置失败，请重试');
        //         }
        //     }).catch((error) => {
        //         console.error('setProfessionalIdentity failed:', error);
        //         // this.$message.error('设置失败，请重试');
        //     });
        // }
    }
}
</script>
<style lang="scss">
.personal_setting{
    // .el-dialog{
    //     // height:620px !important;
    //     .el-dialog__body{
    //         // height: 100 !important;
    //         overflow: auto;
    //     }
    //     .el-tabs__content{
    //         position:static;
    //     }
    // }
    .basic_info,.password,.change_avatar,.fancy,.other{
        .item{
            .title{
                font-size: 18px;
                margin: 8px 0;
            }
            .item_content{
                display: flex;
                justify-content: space-between;
                .item_content_left{
                    padding-right: 15px;
                    flex: 1;
                }
                .item_content_right{
                    width: 80px;
                }
            }
            .content{
                font-size: 18px;
                margin: 12px 0;
                .modify{
                    margin-left:14px;
                }
            }
        }
        .fileRef{
            display:none;
        }
        .vue-cropper{
            height:300px;
            margin-top:6px;
        }
        .mobile_phone,.login_name{
            // width:387px
        }
        .reset_mobile,.reset_login_name{
            // width:80px;
            // height:40px
        }
    }
    .submit{
        margin:30px 0;
        display: flex;
        justify-content: flex-end;
        .el-button{
            width: 100%;
        }

    }
    .other_settings{
        .setting_item{
            line-height: 40px;
            font-size: 16px;
        }

    }
}
</style>
