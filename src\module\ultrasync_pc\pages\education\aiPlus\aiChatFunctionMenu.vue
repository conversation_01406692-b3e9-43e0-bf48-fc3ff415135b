<template>
    <div class="ai-chat-menu-container" :class="{ 'show-border-right': isSidebarVisible }">
        <!-- 新增：悬浮小菜单 -->
        <div class="floating-mini-menu" v-if="!isSidebarVisible">
            <el-tooltip :content="$t('expand_menu')" placement="right" ref="tooltipExpand">
                <div class="mini-menu-item" @click="showSidebar">
                    <i class="el-icon-arrow-right"></i>
                </div>
            </el-tooltip>
            <el-tooltip :content="$t('new_chat')" placement="right" ref="tooltipNewChat">
                <div class="mini-menu-item" @click="emitCreateNewChat">
                    <i class="icon iconfont iconaddChat"></i>
                </div>
            </el-tooltip>
            <el-tooltip :content="$t('historical_records')" placement="right" ref="tooltipHistory">
                <div class="mini-menu-item" @click="showSidebar">
                    <i class="el-icon-time"></i>
                </div>
            </el-tooltip>
        </div>

        <!-- 主菜单容器 -->
        <div class="ai-chat-sidebar"  v-if="isSidebarVisible">
            <!-- 新增 header 容器包裹按钮 -->
            <div class="sidebar-header">
                <div class="hide-sidebar-button" @click="hideSidebar">
                    <i class="el-icon-arrow-left"></i>
                    <span>{{ $t('collapse_menu') }}</span>
                </div>
            </div>
            <div class="new-chat-button" @click="emitCreateNewChat">
                <i class="el-icon-plus"></i>
                <span>{{ $t('new_chat') }}</span>
            </div>
            <div class="history-section">
                <div class="history-title">{{ $t('historical_records') }}</div>
                <div class="history-list">
                    <div v-for="chat in chatList" :key="chat.id"
                        :class="['history-item', { active: currentChatId === chat.id }]"
                        @click="emitSwitchChat(chat.id)">
                        <i class="el-icon-chat-dot-square"></i>
                        <span class="item-title">{{ chat.title }}</span>
                        <i class="el-icon-delete delete-icon"
                            @click.stop="emitDeleteChat(chat.id)"
                            v-if="chatList.length > 1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../../lib/base";
export default {
    mixins: [base],
    name: "AiChatFunctionMenu",
    props: {
        chatList: {
            type: Array,
            default: () => [],
        },
        currentChatId: {
            type: [Number, String],
            default: 0,
        },
    },
    data() {
        return {
            isSidebarVisible: false, // 新增：控制大菜单可见性
        };
    },
    methods: {
        // 新增：显示大菜单
        showSidebar() {
            if (this.$refs.tooltipExpand) {
                this.$refs.tooltipExpand.showPopper = false;
            }
            if (this.$refs.tooltipHistory) {
                this.$refs.tooltipHistory.showPopper = false;
            }
            this.isSidebarVisible = true;
        },
        // 新增：隐藏大菜单
        hideSidebar() {
            this.isSidebarVisible = false;
        },
        // 移除不需要的方法
        emitCreateNewChat() {
            if (this.$refs.tooltipNewChat) {
                this.$refs.tooltipNewChat.showPopper = false;
            }
            this.$emit('create-new-chat');
        },
        emitSwitchChat(chatId) {
            this.$emit('switch-chat', chatId);
        },
        emitDeleteChat(chatId) {
            this.$emit('delete-chat', chatId);
        },
        emitOpenClinicalThinkingPractice() {
            this.$emit('open-practice');
        },
        // 根据索引返回不同的图标
        getHistoryIcon(index) {
            const icons = [
                'el-icon-chat-dot-square',  // 默认聊天图标
            ];
            return index < icons.length ? icons[index] : icons[0];
        }
    },
};
</script>

<style lang="scss" scoped>
.ai-chat-menu-container {
    position: relative;
    padding: 0 18px;
    display: flex;
    flex-direction: column;
    &.show-border-right {
        border-right: 1px solid #e0e0e0;
    }
}

// 新增：悬浮小菜单样式
.floating-mini-menu {
    background-color: #ffffff;
    border-radius: 20px; // 更圆润的圆角
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column;
    padding: 8px;
    gap: 10px; // 调整间距
    margin-top: 30px;

    .mini-menu-item {
        width: 36px; // 稍大一点
        height: 36px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%; // 圆形按钮
        cursor: pointer;
        color: #606266; // 默认图标颜色
        transition: background-color 0.2s ease;

        i {
            font-size: 18px; // 稍大图标
        }

        &:hover {
            background-color: #f5f7fa;
            color: #409EFF; // 悬停时图标变蓝
        }
    }
}

.ai-chat-sidebar {
    width: 250px;
    background-color: #ffffff;
    display: flex;
    flex-direction: column;
    padding: 20px 10px;
    position: relative; // 保留 relative 以便将来扩展
    overflow: hidden;
    // 滚动条样式
    &::-webkit-scrollbar {
        width: 4px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }
}

// 新增：侧边栏头部样式
.sidebar-header {
    display: flex;
    justify-content: flex-end; // 将内容推到右侧
    align-items: center;
    margin-bottom: 10px; // 与下方按钮的间距
    margin-top: 10px;
}

// 修改：关闭按钮样式
.hide-sidebar-button {
    display: flex;
    align-items: center;
    cursor: pointer;
    padding: 5px; // 调整内边距
    color: #909399; // 修改默认颜色为较浅灰色

    i {
        margin-right: 4px;
    }

    span {
        font-size: 12px; // 修改字体大小
    }

    &:hover {
        color: #409EFF; // 保留悬停颜色
    }
}

.new-chat-button {
    height: 40px;
    background-color: #EBF5FF;
    border-radius: 8px;
    display: flex;
    align-items: center;
    padding: 0 16px;
    margin-bottom: 20px;
    cursor: pointer;
    flex-shrink: 0;

    i {
        font-size: 14px;
        color: #409EFF;
        margin-right: 8px;
    }

    span {
        font-size: 14px;
        color: #333;
        font-weight: 500;
    }

    .shortcut {
        margin-left: auto;
        font-size: 12px;
        color: #909399;
        font-weight: normal;
    }

    &:hover {
        background-color: #DCF0FF;
    }
}

.history-section {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

.history-title {
    font-size: 12px;
    color: #909399;
    margin-bottom: 8px;
    padding-left: 8px;
    flex-shrink: 0;
}

.history-list {
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    flex: 1;
    // 滚动条样式
    &::-webkit-scrollbar {
        width: 4px;
    }

    &::-webkit-scrollbar-track {
        background: transparent;
    }

    &::-webkit-scrollbar-thumb {
        background-color: rgba(0, 0, 0, 0.1);
        border-radius: 4px;
    }
}

.history-item {
    height: 40px;
    display: flex;
    align-items: center;
    padding: 0 12px;
    border-radius: 4px;
    margin-bottom: 4px;
    cursor: pointer;
    position: relative;
    flex-shrink: 0;
    i {
        font-size: 16px;
        color: #909399;
        margin-right: 8px;
        width: 16px;
        text-align: center;
    }

    .item-title {
        font-size: 14px;
        color: #606266;
        flex: 1;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        padding-right: 20px;
    }

    .badge {
        min-width: 16px;
        height: 16px;
        background-color: #F56C6C;
        border-radius: 8px;
        color: white;
        font-size: 12px;
        text-align: center;
        line-height: 16px;
        padding: 0 4px;
    }

    .ellipsis {
        font-size: 16px;
        color: #C0C4CC;
    }

    .delete-icon {
        display: none;
        color: #909399;
        font-size: 14px;

        &:hover {
            color: #F56C6C;
        }
    }

    &:hover {
        background-color: #F5F7FA;

        .delete-icon {
            display: block;
            position: absolute;
            right: 12px;
        }

        .badge, .ellipsis {
            display: none;
        }
    }

    &.active {
        background-color: #ECF5FF;

        i, .item-title {
            color: #409EFF;
        }
    }
}
</style>
