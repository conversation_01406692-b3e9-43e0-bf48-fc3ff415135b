<template>
    <div class="group_set_wall" v-loading="loadingWall">
        <input type="hidden" name="" :value="groupset_id" />
        <div class="navbar clearfix">
            <el-button class="fl" v-if="EnableQc_statistics" @click="openBIDataShow" type="primary"
                >{{ $t('bi_data_display') }}
            </el-button>
            <el-button class="fl" v-if="showStatistic" @click="groupSetStatistics" type="primary"
                >{{ $t('group_set_statistics') }}
            </el-button>
            <el-button class="fl" @click="gotoTvWall" type="primary" v-if="isShowMonitorWall && isMyGroupSet"
                >{{ $t('tv_wall_text') }}
            </el-button>
            <el-button class="fl" type="primary" @click="openMember" v-if="isMyGroupSet">{{
                $t('edit_group_set')
            }}</el-button>
            <el-button class="fl" type="primary" @click="openAuthMng" v-if="isMyGroupSet">{{
                $t('auth_mng')
            }}</el-button>
            <el-button v-loading="loadingDelete" class="fl" @click="deleteGroupset" type="danger" v-if="isMyGroupSet"
                >{{ $t('delete_groupset_text') }}
            </el-button>
            <template v-if="isMyGroupSet">
                <i
                    @click="toggleGroupsetExam(1)"
                    v-show="currentGroupset.view_mode == 0"
                    class="icon iconfont iconlist"
                ></i>
                <i
                    @click="toggleGroupsetExam(0)"
                    v-show="currentGroupset.view_mode == 1"
                    class="icon iconfont icongroupchat-icon"
                ></i>
            </template>

            <!-- <el-popover
              placement="bottom"
              width="200"
              popper-class="groupset_operate"
              trigger="click">
                <div class="operate_list">
                    <div  @click="editGroupsetManager">{{$t('groupset_manager')}}</div>
                    <div @click="addGroupsetAttendee">{{$t('groupset_add_attendee')}}</div>
                    <div @click="deleteGroupsetAttendee">{{$t('groupset_delete_attendee')}}</div>
                </div>
            </el-popover> -->
        </div>
        <div class="grouset_wall_panel" v-if="currentGroupset.view_mode == 0">
            <div class="row" v-for="(row, rowNum) in showData" :key="rowNum">
                <div class="column" v-for="(column, index) in row" :key="'column_' + index">
                    <div class="tv_wall_item" v-if="!column.empty">
                        <div class="subject longwrap">
                            {{ column.subject }}
                        </div>
                        <div class="monitor" :ref="`monitor_${column.conversation_id}_${column.friend_id}`">
                            <template v-if="column.message">
                                <el-image
                                    v-if="
                                        column.message.msg_type == systemConfig.msg_type.Image ||
                                        column.message.msg_type == systemConfig.msg_type.OBAI ||
                                        column.message.msg_type == systemConfig.msg_type.Frame
                                    "
                                    :src="column.message.error_image || column.message.url_local"
                                    lazy
                                    @error="setErrorImage(column.message)"
                                >
                                    <img slot="placeholder" src="static/resource_pc/images/loading.gif" />
                                </el-image>

                                <template
                                    v-else-if="
                                        column.message.msg_type == systemConfig.msg_type.Cine ||
                                        column.message.msg_type == systemConfig.msg_type.Video
                                    "
                                >
                                    <el-image
                                        :src="column.message.error_image || column.message.url_local"
                                        lazy
                                        @error="setErrorImage(column.message)"
                                    >
                                        <img slot="placeholder" src="static/resource_pc/images/loading.gif" />
                                    </el-image>
                                    <i class="iconfont iconvideo_fill_light"></i>
                                </template>
                                <!-- <img v-if="column.message.msg_type==systemConfig.msg_type.Image||column.message.msg_type==systemConfig.msg_type.Frame" :src="column.message.realUrl"> -->
                                <!-- <img v-else-if="column.message.msg_type==systemConfig.msg_type.Cine||column.message.msg_type==systemConfig.msg_type.Video" :src="column.message.realUrl"> -->
                                <div
                                    v-else-if="column.message.msg_type == systemConfig.msg_type.RealTimeVideoReview"
                                    class="empty_bg"
                                >
                                    <p>{{ $t('realtime_video_review_text') }}</p>
                                    <p class="time">{{ formatTime(column.message.start_ts) }}</p>
                                    <p class="time">{{ formatTime(column.message.stop_ts) }}</p>
                                </div>
                                <div
                                    v-else-if="column.message.msg_type == systemConfig.msg_type.VIDEO_CLIP"
                                    class="empty_bg"
                                >
                                    <p>{{ $t('video_clips') }}</p>
                                    <p class="time">{{ formatTime(column.message.start_ts) }}</p>
                                </div>
                                <div
                                    v-else-if="column.message.msg_type == systemConfig.msg_type.RealTimeVideo"
                                    class="empty_bg"
                                >
                                    {{ $t('realtime_video_text') }}
                                </div>
                                <div v-else class="empty_bg">
                                    {{ $t('groupset_msg_empty') }}
                                </div>
                            </template>
                            <template v-else>
                                <div class="empty_bg">
                                    {{ $t('groupset_msg_empty') }}
                                </div>
                            </template>
                        </div>
                        <div class="footer clearfix">
                            <div class="count fl">
                                <el-popover placement="bottom" popper-class="toolbar_item" trigger="hover">
                                    <span>{{ $t('groupset_exam_count') }}</span>
                                    <i class="iconfont iconlist" slot="reference"></i>
                                </el-popover>
                                <span>{{ column.exam_count }}</span>
                                <el-popover placement="bottom" popper-class="toolbar_item" trigger="hover">
                                    <span>{{ $t('groupset_video_count') }}</span>
                                    <i class="iconfont iconvideo_fill_light" slot="reference"></i>
                                </el-popover>
                                <span>{{ column.video_count }}</span>
                                <el-popover placement="bottom" popper-class="toolbar_item" trigger="hover">
                                    <span>{{ $t('groupset_image_count') }}</span>
                                    <i class="iconfont iconpicture" slot="reference"></i>
                                </el-popover>
                                <span>{{ column.image_count }}</span>
                            </div>
                            <template v-if="column.message">
                                <el-popover
                                    v-if="
                                        (column.message.msg_type == systemConfig.msg_type.Image ||
                                            column.message.msg_type == systemConfig.msg_type.OBAI ||
                                            column.message.msg_type == systemConfig.msg_type.Frame) &&
                                        isMyGroupSet
                                    "
                                    placement="bottom"
                                    popper-class="toolbar_item"
                                    trigger="hover"
                                >
                                    <span>{{ $t('enter_gallery') }}</span>
                                    <i
                                        @click="clickGallery($event, column.message)"
                                        class="iconfont iconslider1 fr"
                                        slot="reference"
                                    ></i>
                                </el-popover>
                                <el-popover
                                    v-else-if="
                                        (column.message.msg_type == systemConfig.msg_type.Cine ||
                                            column.message.msg_type == systemConfig.msg_type.Video ||
                                            column.message.msg_type == systemConfig.msg_type.RealTimeVideoReview ||
                                            column.message.msg_type == systemConfig.msg_type.VIDEO_CLIP) &&
                                        isMyGroupSet
                                    "
                                    placement="bottom"
                                    popper-class="toolbar_item"
                                    trigger="hover"
                                >
                                    <span>{{ $t('enter_gallery') }}</span>
                                    <i
                                        class="iconfont iconslider1 fr"
                                        @click="clickGallery($event, column.message)"
                                        slot="reference"
                                    ></i>
                                </el-popover>
                                <el-popover
                                    v-else-if="
                                        column.message.msg_type == systemConfig.msg_type.RealTimeVideo && isMyGroupSet
                                    "
                                    placement="bottom"
                                    popper-class="toolbar_item"
                                    trigger="hover"
                                >
                                    <span>{{ $t('enter_live') }}</span>
                                    <i
                                        @click="clickGallery($event, column.message)"
                                        class="iconfont iconvideo fr"
                                        slot="reference"
                                    ></i>
                                </el-popover>
                            </template>
                            <el-popover
                                placement="bottom"
                                popper-class="toolbar_item"
                                trigger="hover"
                                v-if="isMyGroupSet"
                            >
                                <span>{{ $t('enter_conversation') }}</span>
                                <i
                                    @click="trunConversation(column)"
                                    class="iconfont icongroupchat-icon fr"
                                    slot="reference"
                                ></i>
                            </el-popover>
                        </div>
                    </div>
                </div>
            </div>
            <div class="groupset_footer clearfix">
                <div class="pagination fr">
                    <el-pagination
                        background
                        layout="prev,pager,next"
                        :current-page.sync="pageNum"
                        @current-change="currentChange"
                        :page-size="9"
                        :total="wallList.length"
                    >
                    </el-pagination>
                </div>
            </div>
        </div>
        <template v-if="currentGroupset.view_mode == 1">
            <groupset-exam-view></groupset-exam-view>
        </template>
        <router-view :closeCb="checkPlayRealtime" class="groupSetWallRouterView" v-if="showRouterView"></router-view>
    </div>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool.js";
import groupsetExamView from "../components/groupsetExamView";
import {
    parseSingleChat,
    getRealUrl,
    getThumb,
    getLocalImgUrl,
    getThumbnailLocalImgUrl,
    addRootToUrl,
    getMoreResourceList,
} from "../lib/common_base";
import { destroyGallery } from "../lib/common_realtimeVideo";
import { getLanguage } from '@/common/i18n';
export default {
    mixins: [base],
    name: "GroupsetWall",
    permission: true,
    components: { groupsetExamView },
    data() {
        return {
            getMoreResourceList,
            loadingDelete: false,
            loadingWall: false,
            loadingId: 0,
            currentId: 0,
            wallList: [],
            pageNum: 1,
            showData: [], //展示的二维数组
            showDataList: [], //展示数据的一维数组
            showRouterView: true,
            currentGalleryList: [],
            isManager: false,
        };
    },
    computed: {
        groupset_id() {
            let id = this.$route.params.groupset_id;
            this.initPage(id);
            return id;
        },
        currentGroupset() {
            return this.$store.state.groupset.currentGroupset || {};
        },
        isMyGroupSet() {
            return this.currentGroupset.groupSetType == "myGroupSet";
        },
        EnableQc_statistics() {
            // 是否允许查看BI统计
            return (
                this.$checkPermission({regionPermissionKey:'qcStatistics'}) &&
                this.$store.state.systemConfig.serverInfo.qc_statistics &&
                this.$store.state.systemConfig.serverInfo.qc_statistics.enable
            );
        },
        isWorkStation() {
            return this.isCef && Tool.ifAppWorkstationClientType(this.systemConfig.clientType);
        },
        isShowMonitorWall() {
            return this.$checkPermission({regionPermissionKey:'tvwall'}) && this.user.role > 1 && !this.isWorkStation;
            // return this.user.enable_monitor_wall&&this.user.role>1
        },
        showStatistic() {
            return this.$checkPermission({regionPermissionKey:'cloudStatistic'});
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.$root.eventBus
                .$off("updateGroupsetWallRealtime")
                .$on("updateGroupsetWallRealtime", this.updateGroupsetWallRealtime);
            this.$root.eventBus.$off("NotifyScreenChanged").$on("NotifyScreenChanged", this.handleNotifyScreenChanged);
            this.$root.eventBus.$off("refreshGroupsetWall").$on("refreshGroupsetWall", this.getGroupsetWall);
        });
    },
    beforeRouteUpdate(to, from, next) {
        if (to.name === "groupset_wall") {
            this.showRouterView = false;
            setTimeout(() => {
                this.showRouterView = true;
            }, 1000);
        }
        next();
    },
    beforeDestroy() {
        this.closeAllPlayer();
        this.showData = [];
    },
    methods: {
        initPage(groupset_id) {
            console.log("initPage", this.loadingId, groupset_id, this.currentId);
            if (groupset_id == this.currentId || this.loadingId != 0 || groupset_id == 0) {
                //正在加载/重复刷新当前群落不执行
                this.currentId = groupset_id;
                return;
            }
            this.currentId = groupset_id;
            let temp = this.$store.state.groupset.walls[groupset_id];
            this.toggleGroupsetExam(this.currentGroupset.view_mode, true);
            if (temp) {
                this.wallList = temp;
                this.currentChange(1);
                // return;
            }
            this.getGroupsetWall(groupset_id);
        },
        getGroupsetWall(groupset_id) {
            let that = this;
            this.loadingId = groupset_id;
            this.wallList = [];
            this.loadingWall = true;
            that.$root.socket.emit("open_conversation_wall", { groupset_id: groupset_id }, (err, result) => {
                that.loadingWall = false;
                that.loadingId = 0;
                if (err) {
                    console.log("open_conversation_wall error:", err);
                } else {
                    let data = {};
                    data.id = groupset_id;
                    data.list = result.list;
                    let index = 0;
                    for (let item of data.list) {
                        if (item.message) {
                            item.message.url = addRootToUrl(item.message.url);
                            item.message.url_local = getThumbnailLocalImgUrl(item.message);
                            // item.message.url_local=item.message.url
                            item.message.realUrl = addRootToUrl(getRealUrl(item.message));
                            if(this.systemConfig.serverInfo.network_environment === 1){
                                item.message.url = Tool.replaceInternalNetworkEnvImageHost(item.message.url)
                                item.message.url_local = Tool.replaceInternalNetworkEnvImageHost(item.message.url_local)
                                item.message.realUrl = Tool.replaceInternalNetworkEnvImageHost(item.message.realUrl)
                            }
                        }
                        index++;
                    }
                    // console.log("getGroupsetWall:", data);
                    that.$store.commit("groupset/addGroupsetWall", data);
                    if (data.id == that.groupset_id) {
                        that.wallList = data.list;
                        that.currentChange(1);
                    }
                }
            });
        },
        loadError(imgObj) {
            let target = getThumb(imgObj.msg_type);
            if (target && imgObj.url_local && imgObj.url_local.indexOf(target) > -1) {
                //加载大缩略图失败
                let originThumb = getLocalImgUrl(imgObj.url);
                let realUrl = getRealUrl(imgObj);
                let isConsultation = false;
                if (
                    imgObj.msg_type == this.systemConfig.msg_type.OBAI ||
                    imgObj.msg_type == this.systemConfig.msg_type.Frame ||
                    imgObj.msg_type == this.systemConfig.msg_type.Cine
                ) {
                    isConsultation = true;
                }
                let data = {
                    msg_type: imgObj.msg_type,
                    file_storage_type: this.systemConfig.serverInfo.attachment_storage_type,
                    url: imgObj.url_local,
                    original_url: realUrl,
                    isConsultation: isConsultation,
                };
                this.$root.socket.emit("request_get_thumbnail", data, (is_succ, json) => {
                    console.log("request_get_thumbnail", json);
                    if (is_succ) {
                        let image = new Image();
                        image.onload = () => {
                            imgObj.url_local = data.url;
                        };
                        let url = data.url
                        if(this.systemConfig.serverInfo.network_environment === 1){
                            url = Tool.replaceInternalNetworkEnvImageHost(url)
                        }
                        image.src = url;
                    }
                });
                imgObj.url_local = originThumb;
            } else {
                imgObj.url_local = "static/resource/images/slt_err.png";
            }
        },
        groupSetStatistics() {
            const requestConfig = this.systemConfig.server_type;
            let ajaxServer = requestConfig.protocol + requestConfig.host + requestConfig.port;
            let lang = getLanguage();
            if(process.env.NODE_ENV === 'production'){
                ajaxServer += '/statistic'
            }else{
                ajaxServer = window.location.origin
            }
            const url = Tool.transferLocationToCe(
                `${ajaxServer}/statistic.html#/index/live?dataFrom=groupset&id=${this.groupset_id}&token=${window.vm.$store.state.dynamicGlobalParams.token}&language=${lang}`
            );
            // window.localStorage.setItem('stat_query', JSON.stringify({dataFrom: 'groupset',id: this.groupset_id}));
            if ([1, 5].includes(window.clientType)) {
                window.open(url, "blank");
            } else {
                window.CWorkstationCommunicationMng.OpenNewWindow({ url });
            }
        },
        openMember() {
            const id = this.groupset_id;
            this.$router.push(`/main/index/chat_window/0/groupset_wall/${id}/groupset_setting`);
        },
        openAuthMng() {
            const id = this.groupset_id;
            this.$router.push(`/main/index/chat_window/0/groupset_wall/${id}/groupset_manager`);
        },
        openBIDataShow() {
            var that = this;
            const requestConfig = this.systemConfig.server_type;
            let ajaxServer = requestConfig.protocol + requestConfig.host + requestConfig.port;

            let lang = getLanguage();
            if(process.env.NODE_ENV === 'production'){
                ajaxServer += '/statistic'
            }else{
                ajaxServer = window.location.origin
            }
            const url = Tool.transferLocationToCe(
                `${ajaxServer}/statistic.html#/remote_ultrasound_data_center?dataFrom=groupset&id=${this.groupset_id}&token=${window.vm.$store.state.dynamicGlobalParams.token}&language=${lang}`
            );
            if (Tool.ifBrowserClientType(that.systemConfig.clientType)) {
                window.open(url, "blank");
            } else {
                window.CWorkstationCommunicationMng.OpenNewWindow({ url });
            }
        },
        currentChange(pageNum) {
            this.closeAllPlayer();
            this.showData = [];
            let data = this.wallList.slice((pageNum - 1) * 9, pageNum * 9);
            this.showDataList = data.concat([]);
            for (let rowIndex = 0; rowIndex < 3; rowIndex++) {
                let row = [];
                for (let columnIndex = 0; columnIndex < 3; columnIndex++) {
                    if (data.length == 0) {
                        row.push({ empty: true });
                    } else {
                        row.push(data.shift());
                    }
                }
                this.showData.push(row);
            }
            this.checkPlayRealtime();
        },
        trunConversation(item) {
            if (item.conversation_id) {
                this.openConversation(item.conversation_id, 2);
            } else {
                this.openConversation(item.friend_id, 3);
            }
        },
        clickGallery(event, file) {
            this.closeAllPlayer();
            var cid = file.group_id;
            this.$nextTick(() => {
                this.openConversation(cid, 10, null, async () => {
                    if (this.conversationList[cid] && this.conversationList[cid].galleryObj.gallery_list.length > 0) {
                        this.currentGalleryList = this.conversationList[cid].galleryObj.gallery_list;
                    } else {
                        this.currentGalleryList = await this.getMoreResourceList(0, cid);
                    }

                    this.$store.commit("gallery/setGallery", {
                        list: this.currentGalleryList,
                        openFile: file,
                        loadMore: true,
                        loadMoreCallback: async () => {
                            const moreResourceList = await this.getMoreResourceList(
                                this.currentGalleryList[this.currentGalleryList.length - 1].resource_id,
                                cid
                            );
                            this.currentGalleryList = this.deduplicateArrayByResourceId(
                                this.currentGalleryList.concat(moreResourceList)
                            );
                            let loadMore = false;

                            if (moreResourceList.length >= this.systemConfig.consultationImageShowNum) {
                                loadMore = true;
                            }
                            this.$store.commit("gallery/setGallery", {
                                list: this.currentGalleryList,
                                loadMore,
                            });
                        },
                    });
                    this.$nextTick(() => {
                        let groupset_id = this.$route.params.groupset_id;
                        this.$router.push(`/main/index/chat_window/${cid}/groupset_wall/${groupset_id}/gallery`);
                    });
                });
            });

            // setTimeout(()=>{
            //     this.$root.eventBus.$emit('changeTab',3)
            // },0)
        },
        deduplicateArrayByResourceId(array) {
            const seen = new Set();
            const deduplicatedArray = [];

            for (let i = 0; i < array.length; i++) {
                const item = array[i];
                const resourceId = item.resource_id;

                if (!seen.has(resourceId)) {
                    seen.add(resourceId);
                    deduplicatedArray.push(item);
                }
            }

            return deduplicatedArray;
        },
        updateGroupsetWallRealtime(data) {
            let walls = this.$store.state.groupset.walls;
            if (data.type == "open") {
                for (let groupset_id in walls) {
                    let index = 0;
                    for (let conversation of walls[groupset_id]) {
                        if (conversation.conversation_id == data.cid) {
                            let lastMsg = Object.assign({}, conversation.lastMsg || conversation.message);
                            this.$store.commit("groupset/updateGroupsetWall", {
                                groupset_id: groupset_id,
                                index: index,
                                updateObj: {
                                    message: {
                                        msg_type: this.systemConfig.msg_type.RealTimeVideo,
                                        group_id: data.cid,
                                        file_id: "temp",
                                    },
                                    lastMsg: lastMsg,
                                },
                            });
                            if (this.groupset_id == groupset_id) {
                                //当前页面是该群落
                                for (let j = 0; j < this.showDataList.length; j++) {
                                    if (this.showDataList[j].conversation_id == data.cid) {
                                        //实时直播的会话再当前页面
                                        this.openPlayer(j);
                                        break;
                                    }
                                }
                            }
                            break;
                        }
                        index++;
                    }
                }
            } else if (data.type == "close") {
                for (let groupset_id in walls) {
                    let index = 0;
                    for (let conversation of walls[groupset_id]) {
                        if (conversation.conversation_id == data.cid) {
                            if (this.groupset_id == groupset_id) {
                                //当前页面是该群落
                                for (let j = 0; j < this.showDataList.length; j++) {
                                    if (this.showDataList[j].conversation_id == data.cid) {
                                        //实时直播的会话再当前页面
                                        this.closePlayer(j);
                                        break;
                                    }
                                }
                            }
                            let lastMsg = conversation.lastMsg;
                            this.$store.commit("groupset/updateGroupsetWall", {
                                groupset_id: groupset_id,
                                index: index,
                                updateObj: {
                                    message: lastMsg,
                                },
                            });

                            break;
                        }
                        index++;
                    }
                }
            }
        },
        checkPlayRealtime() {
            for (let index = 0; index < this.showDataList.length; index++) {
                let item = this.showDataList[index];
                if (item.message && item.message.msg_type == this.systemConfig.msg_type.RealTimeVideo) {
                    this.openPlayer(index);
                }
            }
            console.log("checkPlayRealtime");
        },
        closeAllPlayer() {
            destroyGallery();
            console.log("closeAllPlayer");
        },
        openPlayer(index) {
            this.$nextTick(() => {
                let item = this.showDataList[index];
                let str = `monitor_${item.conversation_id}_${item.friend_id}`;
                let dom = this.$refs[str][0];
                let header = document.querySelector(".header_bar").clientHeight;
                let leftBar = document.querySelector(".left_container").clientWidth;
                var clientWidth = document.documentElement.clientWidth;
                var clientHeight = document.documentElement.clientHeight;
                var maxTop = 15 + 90;
                var maxLeft = 30;
                var maxWidth = clientWidth - 66;
                var maxHeight = clientHeight - 114 - 54;
                //一个像素，需要几个点来显示，苹果机器，与其他机器不一样
                var dpr = window.devicePixelRatio;
                let conversation = this.conversationList[item.conversation_id];
                let ultrasound_video = "";
                for (let video of conversation.video_list) {
                    if (video.type == 1) {
                        ultrasound_video = video.url;
                    }
                }
                let json = {
                    id: item.conversation_id,
                    // img_id:data.img_id,
                    ultrasound_video: ultrasound_video,
                    player_index: index,
                    dpr: dpr,
                    width: dom.clientWidth * dpr,
                    height: dom.clientHeight * dpr,
                    top: (dom.offsetTop + header + 10) * dpr,
                    left: (dom.offsetLeft + leftBar + 10) * dpr,
                    top_zoom: maxTop * dpr,
                    left_zoom: maxLeft * dpr,
                    width_zoom: maxWidth * dpr,
                    height_zoom: maxHeight * dpr,
                };
                window.CWorkstationCommunicationMng.enterVideoWall(json);
            });
        },
        closePlayer(index) {
            console.log("closePlayer");
            let item = this.showDataList[index];
            let json = {
                id: item.conversation_id,
                player_index: index,
            };
            window.CWorkstationCommunicationMng.exitVideoWall(json);
        },
        toggleGroupsetExam(view_mode, isInit) {
            this.$store.commit("groupset/updateCurrentGroupset", {
                view_mode: view_mode,
            });
            this.$nextTick(() => {
                this.closeAllPlayer();
                if (view_mode == 1) {
                    this.$root.eventBus.$emit("initGroupsetExamView");
                } else {
                    !isInit && this.checkPlayRealtime();
                }
            });
        },
        deleteGroupset() {
            const params = {
                groupSetID: parseInt(this.groupset_id),
            };
            this.$confirm(this.$t('delete_groupset_confirm'), this.$t('tip_title'), {
                confirmButtonText: this.$t('confirm_button_text'),
                cancelButtonText: this.$t('cancel_button_text'),
                type: "warning",
            }).then(() => {
                this.loadingDelete = true;
                window.main_screen.deleteGroupset(params, (data) => {
                    this.loadingDelete = false;
                    if (data.error_code == 0) {
                        this.$store.commit("groupset/deleteGroupset", {
                            id: this.groupset_id,
                        });
                        this.$store.commit("groupset/deleteGroupsetWall", {
                            id: this.groupset_id,
                        });
                    }
                });
            });
        },
        gotoTvWall() {
            const id = this.groupset_id;
            if (this.isCef) {
                this.$router.push(`/main/index/chat_window/0/groupset_wall/${id}/tv_wall_web`);
            } else {
                this.$router.push(`/main/index/chat_window/0/groupset_wall/${id}/tv_wall_web`);
            }
        }
    },
};
</script>
<style lang="scss">
.group_set_wall {
    position: absolute;
    left: 300px;
    top: 0;
    z-index: 3;
    right: 0;
    bottom: 0;
    background: #f5f8fa;
    display: flex;
    flex-direction: column;
    user-select: none;
    .navbar {
        padding: 10px;
        font-size: 16px;
        p {
            line-height: 40px;
            margin-right: 10px;
        }
        .iconlist,
        .icongroupchat-icon {
            color: #779a98;
            font-size: 28px;
            line-height: 40px;
            margin-left: 12px;
            cursor: pointer;
        }
    }
    .grouset_wall_panel {
        flex: 1;
        display: flex;
        flex-direction: column;
        padding: 0px 10px;
        .row {
            flex: 1;
            display: flex;
            .column {
                flex: 1;
                padding: 6px 4px;
                overflow: hidden;
                .tv_wall_item {
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    cursor: pointer;
                    background: linear-gradient(to bottom right, #000, #363636);
                    .subject {
                        color: #aaa;
                        padding-left: 8px;
                    }
                    .monitor {
                        flex: 1;
                        position: relative;
                        padding-bottom: 2px;
                        .el-image {
                            width: 100%;
                            height: 100%;
                            display: block;
                            img {
                                position: absolute;
                                top: 50%;
                                left: 50%;
                                transform: translate(-50%, -50%);
                                max-width: 100%;
                                max-height: 100%;
                                width: auto;
                                height: auto;
                            }
                        }
                        .iconvideo_fill_light {
                            position: absolute;
                            bottom: 0;
                            left: 6px;
                            color: #fff;
                            font-size: 26px;
                        }
                        .empty_bg {
                            color: #d5e808;
                            text-align: center;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                            .time {
                                font-size: 18px;
                                white-space: nowrap;
                            }
                        }
                    }
                    .footer {
                        font-size: 18px;
                        color: #aaa;
                        background: #555;
                        height: 32px;
                        line-height: 32px;
                        padding: 0 6px;
                        .subject {
                            margin-right: 6px;
                        }
                        .count {
                            display: flex;
                        }
                        i {
                            font-size: 22px;
                            color: #ccc;
                            cursor: pointer;
                            margin: 0 4px;
                        }
                        i:hover {
                            color: #fff;
                        }
                        .iconslider1,
                        .icongroupchat-icon {
                            display: none;
                        }
                        .iconvideo {
                            color: #16f533;
                            font-weight: bold;
                            &:hover {
                                color: #16f533;
                            }
                        }
                    }
                    &:hover .footer i {
                        display: block;
                    }
                }
            }
        }
    }
}
.groupset_operate {
    padding: 0;
    .operate_list > div {
        line-height: 50px;
        font-size: 18px;
        color: #000;
        cursor: pointer;
        padding-left: 10px;
        &:hover {
            color: #fff;
            background: #bbcdce;
        }
    }
}
</style>
