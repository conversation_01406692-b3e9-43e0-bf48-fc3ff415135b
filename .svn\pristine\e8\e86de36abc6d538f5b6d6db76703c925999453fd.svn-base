<template>
	<div class="exam_msg">
        <div class="exam_message">
            <div class="exam_patient" @click.left.stop="toggleImages()" :key="'exam_msg_'+message.gmsg_id">
                <i v-show="!openState" class="icon iconfont icondown"></i>
                <i v-show="openState" class="icon iconfont iconup"></i>
                <div class="patient_info clearfix">
                    <p class="patient_info_item longwrap" v-if="message.patient_name" :title="message.patientInfo.patient_name">{{$t('exam_patient_name')}}: {{message.patientInfo.patient_name}}</p>
                    <p class="patient_info_item longwrap" v-if="message.exam_type!=9" :title="$t('exam_types')[message.exam_type]">{{$t('exam_type')}}: {{$t('exam_types')[message.exam_type]}}</p>
                    <p class="patient_info_item longwrap" v-if="message.patient_age!=-1">{{$t('exam_patient_age')}}: {{message.patientInfo.patient_age}}</p>
                    <p class="patient_info_item longwrap" v-if="message.patient_sex!=2">{{$t('exam_patient_sex')}}: {{message.patientInfo.patient_sex}}</p>
                    <p class="patient_info_item longwrap" v-if="message.exam_custom_info&&message.exam_custom_info.annotator">{{$t('calibrater_text')}}{{message.exam_custom_info.annotator}}</p>
                    <p class="patient_info_item longwrap" v-else >{{$t('calibrater_text')}}{{$t('uncalibrated_text')}}</p>
                    <p class="patient_info_item longwrap">{{$t('image_count')}}: {{message.resourceList.length}}</p>
                    <template  v-if="isObstetricQCMulticenter">
                        <p class="patient_info_full" :title="message.patient_series_datetime">{{$t('exam_time')}}: {{formatTime(message.patient_series_datetime)}}</p>
                    </template>
                    <template v-else>
                        <template v-if="isAiAnalyze||isDrAiAnalyze">
                            <p class="patient_info_full" :title="message.patient_series_datetime">{{$t('exam_time')}}: {{formatTime(message.patient_series_datetime)}}</p>
                        </template>
                        <template  v-else>
                            <p class="patient_info_full" :title="message.patient_series_datetime">{{$t('exam_time')}}: {{formatTime(message.patient_series_datetime)}}</p>
                        </template>
                    </template>
                </div>
                <div v-if="message.protocol_name" class="iworks_info clearfix">
                    <div class="icon_title">
                        <i class="iconfont iconquanbuwenjian-weixuanzhong" id="iworks"></i>
                    </div>
                    <p>{{$t('iWorks')}}:{{message.protocol_name}}
                    </p>
                </div>
            </div>
            <div class="exam_image_list clearfix" v-show="openState">
                <div style="display: none">{{messageImage.length}}</div>
                <div class="list clearfix" v-loading="loadingImage">
                    <div v-for="img of imageList" class="file_item" :class="{is_create:img.isCreate,is_delete:img.isDelete}" :key="'img_'+img.resource_id" @contextmenu.stop="callImageMenu($event,img,'chatComponent_examImageItem')">
                        <div class="file_item" v-if="getResourceTempState(img.resource_id) === 0">
                            <img src="static/resource_pc/images/default.png" class="file_img" @contextmenu.stop>
                            <p class="view_name" @contextmenu.stop>{{$t('ref_res_expired')}}</p>
                        </div>
                        <v-touch @click.native.stop="clickGallery($event,img,2)" v-else>
                            <p v-if="img.protocol_view_name"  class="view_name">{{img.protocol_view_name}}</p>
                            <img v-if="img.url_local" :src="img.error_image||img.url_local"  class="file_image" @error="setErrorImage(img)">
                            <img v-else src="static/resource_pc/images/default.png" class="file_image">
                            <!-- <div v-else class="empty_view"></div> -->
                            <i v-if="img.msg_type==systemConfig.msg_type.Cine" class="icon iconfont iconvideo_fill_light" ></i>
                            <span v-show="showAiAnalyzeIcon(img)">
                               <span v-for="iconObj,index in imageStandardIcon(img)" :key="index" :class="[iconObj.css]" :title="iconObj.tips">
                                    {{iconObj.label}}
                               </span>
                            </span>
                        </v-touch>
                    </div>
                    <div v-for="(item,index) in invalidImage" class="file_item" :key="'invalidImage'+index">
                        <img src="static/resource_pc/images/default.png" class="file_img" @contextmenu.stop>
                        <p class="view_name" @contextmenu.stop>{{$t('ref_res_expired')}}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import base from '../lib/base'
import {
    setIworksInfoToMsg,
    getRealUrl,
    patientDesensitization,
    imageStandardIcon,
    checkIworksTest,
    hasAiAnalyzeResult,
    getResourceTempState,
    parseImageListToLocal
} from '../lib/common_base'
import { CHAT_TYPE } from '../lib/constants'
export default {
    mixins: [base],
    name: 'ExamMsg',
    components: {},
    permission: true,
    props:{
        message:{
            type:Object,
            default:()=>{
                return {}
            }
        },
        //检查到图片列表空，是否反复加载图片
        isReload:{
            type:Boolean,
            default:true
        },
        cid:{
            type:[String,Number],
            default:''
        },
        chatType:{
            type:[String,Number],
            default:CHAT_TYPE['CHAT_WINDOW']
        }
    },
    data(){
        return {
            getResourceTempState,
            hasAiAnalyzeResult,
            openState:false,
            loadingImage:false,
            imageList:[],
            invalidImage:[],
            imageStandardIcon,
            checkIworksTest,
        }
    },
    watch:{

    },
    computed:{
        conversation(){
            return this.conversationList[this.cid]||{}
        },
        isHFRMulticenter: function(){
            return this.conversation.multicenter_type == 'hfr_multicenter'
        },
        isObstetricQCMulticenter: function(){
            return this.conversation.multicenter_type == 'obstetric_qc_multicenter'
        },
        avatarObj(){
            let obj={
                avatar:this.message.avatar,
                sex:this.message.sex,
                is_single_chat:1,
                nickname:this.message.nickname,
            }
            return obj;
        },
        messageImage(){
            let imageList=this.message.imageList;
            // if (!imageList && this.isReload) {
            //     this.reloadResourceList();
            // }
            return imageList||[]
        },
        isAiAnalyze(){
            return this.conversation.service_type==this.systemConfig.ServiceConfig.type.AiAnalyze
        },
        isDrAiAnalyze(){
            return this.conversation.service_type==this.systemConfig.ServiceConfig.type.DrAiAnalyze
        }
    },
    mounted(){
        this.$nextTick(()=>{
            this.$root.eventBus.$off('getExamImageListForAutoDownload').$on('getExamImageListForAutoDownload',this.getExamImageListForAutoDownload);
        })
    },
    methods:{
        showAiAnalyzeIcon(img){
            return (this.$checkPermission({regionPermissionKey: 'breastAI'})&&this.isAiAnalyze)||(this.isDrAiAnalyze&&this.$checkPermission({regionPermissionKey: 'drAIAssistant'}))||(this.$checkPermission({regionPermissionKey: 'obstetricalAI'}) && img.mc_resource_map)||true
        },
        isFinishedAiAnalyze(item){
            let isUnfinished = false
            if(item&&item.ai_analyze_list){
                isUnfinished = (item.ai_analyze_list).reduce((h,v)=>{
                    return h&&v.status
                },1)
            }
            if(!isUnfinished&&item.resourceList){
                isUnfinished = (item.resourceList).reduce((h,v)=>{
                    if(v.ai_analyze_id){
                        if(this.$store.state.gallery.commentObj[v.id]){
                            let storeItem = this.$store.state.gallery.commentObj[v.id]
                            if(storeItem.ai_analyze_report&&storeItem.ai_analyze_report.ai_analyze_id==v.ai_analyze_id){
                                return h&&storeItem.ai_analyze_report.status
                            }else{
                                return h
                            }
                        }else{
                            return h
                        }
                    }else{
                        return h
                    }
                },1)
            }
            return isUnfinished
        },
        toggleImages(){
            this.openState=!this.openState;
            if (this.openState) {
                this.setImageList()
            }
        },
        reloadResourceList(){
            if (this.openState) {
                this.setImageList()
            }
        },
        getExamImageListForAutoDownload(msg,callback){
            console.log('getExamImageListForAutoDownload')
            if (msg && msg.exam_id) {
                let resource_id_list=[];
                let imageList=[];
                for(let resource of msg.resourceList){
                    resource_id_list.push(resource.id);
                }
                if(this.cid && this.conversationList != undefined && this.conversationList[this.cid] != undefined) {
                    this.controller=this.conversationList[this.cid].socket;
                }

                if ( !this.controller) {
                    this.openConversation(msg.group_id,10,null,(is_suc,conversation)=>{
                        if(!is_suc){
                            return
                        } else {
                            // this.conversation = this.conversationList[this.cid]
                            this.controller=this.conversation.socket;
                            this.controller.emit('get_gallery_messages_by_resource_ids',{resource_id_list},(is_succ,data)=>{
                                console.log('get_gallery_messages_by_resource_ids',data)
                                let gallery_list = data.gallery_list || []
                                if (is_succ) {
                                    patientDesensitization(gallery_list);
                                    for(let image of gallery_list){
                                        image.realUrl = getRealUrl(image)
                                        if (image.img_encode_type &&'DCM' == image.img_encode_type.toUpperCase()) {
                                            image.dicomUrl=image.url.replace("thumbnail.jpg",`SingleFrame.${image.img_encode_type}`);
                                        }
                                        imageList.push(image)
                                    }
                                    // msg.imageList = imageList
                                    // console.error('test------4',msg)
                                    callback&&callback(imageList)
                                } else {
                                    // msg.imageList = imageList
                                    callback&&callback(imageList)
                                }
                            })
                        }
                    })
                } else {
                    this.controller.emit('get_gallery_messages_by_resource_ids',{resource_id_list},(is_succ,data)=>{
                        // console.error('test------3',data)
                        console.log('get_gallery_messages_by_resource_ids',data)
                        let gallery_list = data.gallery_list || []
                        if (is_succ) {
                            patientDesensitization(gallery_list);
                            for(let image of gallery_list){
                                image.realUrl = getRealUrl(image)
                                if (image.img_encode_type&&'DCM' == image.img_encode_type.toUpperCase()) {
                                    image.dicomUrl=image.url.replace("thumbnail.jpg",`SingleFrame.${image.img_encode_type}`);
                                }
                                imageList.push(image)
                            }
                            // msg.imageList = imageList
                            // console.error('test------4',msg)
                            callback&&callback(imageList)
                        } else {
                            // msg.imageList = imageList
                            callback&&callback(imageList)
                        }
                    })
                }
            }
        },

        async setImageList(tryNuber=0){
            return new Promise((resolve, reject) => {
                let i = tryNuber;
                if (this.loadingImage) {
                    reject()
                    return ;
                }
                let message=this.message;
                let resource_id_list=[];
                for(let resource of this.message.resourceList){
                    resource_id_list.push(resource.id);
                }
                if (message.imageList && message.imageList.length >= resource_id_list.length) {
                    this.imageList=this.message.imageList;
                    this.setInvalidImages(resource_id_list)
                    resolve(this.imageList)
                }else{
                    if(this.imageList&&this.imageList.length>0 && this.imageList.length >= resource_id_list.length){
                        resolve(this.imageList)
                        return
                    }
                    this.loadingImage=true;
                    this.conversation.socket.emit('get_gallery_messages_by_resource_ids',{resource_id_list},(is_succ,data)=>{
                        if (is_succ) {
                            patientDesensitization(data.gallery_list);
                            this.setIworks(data.gallery_list,message.protocol_execution_guid)
                            this.imageList=parseImageListToLocal(data.gallery_list,'url');
                            this.$store.commit('conversationList/updateChatMessage',{
                                gmsg_id:message.gmsg_id,
                                group_id:this.cid,
                                imageList:this.imageList
                            })
                            this.setInvalidImages(resource_id_list)
                            if (this.imageList && this.imageList.length<1 && this.isReload && i < 3) {
                                i  = i + 1;
                                setTimeout( async ()=>{
                                    await this.setImageList(i);
                                },500)
                            }
                            resolve(this.imageList)
                        }
                        this.loadingImage=false;
                    })
                }
            })
        },
        setInvalidImages(resource_id_list){
            let length=resource_id_list.length-this.imageList.length;
            this.invalidImage=[]
            for(let index=0;index<length;index++){
                this.invalidImage.push(index);
            }
        },
        setIworks(list,protocol_execution_guid){
            for(let item of list){
                setIworksInfoToMsg(item,protocol_execution_guid);
            }
        },
        clickGallery(event,file,type){
            if(Number(window.vm.$root.currentLiveCid) === Number(this.cid)){
                this.$message.error(this.$t('playing_video_tip'))
                return
            }
            this.$store.commit('gallery/setGallery',{
                list:this.imageList,
                openFile:file,
                loadMore:false,
                loadMoreCallback:null
            })
            if(this.chatType === CHAT_TYPE['CHAT_WINDOW']){
                this.$router.push(this.$route.fullPath+'/gallery')
            }

            // this.$nextTick(() => {
            //     this.$router.push({
            //         path: `${this.$route.path}/gallery`,
            //         query: this.$route.query,
            //     });
            // });
        },
    }
}
</script>
<style lang="scss">
.exam_msg{
    .exam_message{
    	width: 400px;
        padding: .5rem .6rem;
        margin-top: 10px;
        background-color: #fff;
        border-radius: 0.3rem;
        box-shadow: 0.1rem 0.1rem 0.2rem rgba(140,152,155,0.7);

        .exam_patient{
            font-size: 0.6rem;
            padding-top: 0.6rem;
            color: rgb(101,109,112);
            position: relative;
            cursor: pointer;
            .icondown,.iconup{
                position: absolute;
                right: -5px;
                top: -10px;
                line-height:1;
                font-size: 26px;
            }
            .patient_info{
                margin-bottom: 0.1rem;
                .patient_info_full{
                    flex: 1;
                    word-break: break-all;
                    padding-right: 0.3rem;
                }
                .patient_info_item{
                    width: 33%;
                    float: left;
                    word-break: break-all;
                    padding-right: 0.3rem;
                    .standard_number{
                        color:blue;
                    }
                }
                .refute_reason {
                    color: red;
                }
            }
            .iworks_info{
                display: flex;
                align-messages: center;
                .icon_title{
                    background-color: #666;
                    border-radius: 50%;
                    width: 0.85rem;
                    height: 0.85rem;
                    i{
                        position: relative;
                        font-size: 1em;
                        top: -.3em;
                        left: .225em;
                        fill: none;
                        color: #fff;
                        float: left;
                    }
                }
                &>p{
                    display: inline-block;
                    line-height: 1rem;
                    padding-left: 0.4rem;
                    font-size: 0.55rem;
                }
            }
        }
        .exam_image_list{
            background-color: #fff;
            .list{
                margin-top:10px;
            }
            .file_item{
                float: left;
                margin-right: 5px;
                margin-bottom: 5px;
                width: 120px;
                height: 86px;
                background-color: #000;
                position: relative;
                cursor: pointer;
                border-radius: 0.2rem;
                overflow: hidden;
                img{
                    max-width: 100%;
                    max-height: 100%;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
                i{
                    position: absolute;
                    bottom: 6px;
                    color: #fff;
                    font-size: 24px;
                    line-height: 1;
                    left: 4px;
                }
                .review_time{
                    font-size: 12px;
                    text-align: center;
                    color: #fff;
                    position: absolute;
                    white-space: nowrap;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
                .review_text{
                    font-size: 12px;
                    text-align: center;
                    color: yellow;
                    position: absolute;
                    white-space: nowrap;
                    width: 100%;
                    top: 5%;
                }
                &.is_create{
                    .view_name{
                        color: #f9f122;
                    }
                }
                &.is_delete{
                    .view_name{
                        text-decoration: line-through;
                    }
                }
                .view_name{
                    color: #fff;
                    position: absolute;
                    top: 6px;
                    z-index: 9;
                    left: 2px;
                    transform: none;
                    font-size: 14px;
                    white-space: normal;
                    text-align: left;
                }
                .empty_view{
                    width: 100%;
                    height: 100px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    background-color:#2c2d2f;
                    transform: translate(-50%, -50%);
                }
                .iconplus {
                    width:24px;
                    height:24px;
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                }
                .qc_standard_icon{
                    right: 3px;
                    bottom: 0px;
                    position: absolute;
                    color: rgb(0,255,102);
                    font-size: 10px;
                }
                .qc_non_standard_icon{
                    right: 3px;
                    bottom: 0px;
                    position: absolute;
                    color: rgb(255,153,51);
                    font-size: 10px;
                }
                .ai_result_deletion_icon{
                    right: 3px;
                    bottom: 0px;
                    position: absolute;
                    color: rgb(255,0,0);
                    font-size: 10px;
                }
                .bg_blue{
                    background: rgb(0,255,102);
                }
                .bg_red{
                    background: rgb(255,0,0);
                }
                .dr_result_icon{
                    right: 3px;
                    bottom: 3px;
                    position: absolute;
                    color: rgb(241 246 243);
                    font-size: 12px;
                    height: 17px;
                    width: 16px;
                    border-radius: 7px;
                    display: block;
                    text-align: center;
                    line-height: 19px;
                    font-weight: bold;

                }
            }
            .iworks_panel{
                border: 1px solid #aaa;
                margin-bottom: 10px;
                padding: 10px;
                border-radius: 4px;
                &>p{
                    margin-bottom: 4px;
                    padding-right: 26px;
                    position: relative;
                    display:inline-block;
                    i{
                        font-size: 20px;
                        position: absolute;
                        right: 0;
                        top: -2px;
                        cursor: pointer;
                    }
                }
                .iworks_list{
                    .iworks_view{
                        float: left;
                        margin-right: 6px;
                        margin-bottom: 6px;
                        width: 140px;
                        height: 120px;
                        background-color: #000;
                        position: relative;
                        cursor: pointer;
                        img{
                            max-width: 100%;
                            max-height: 100%;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            transform: translate(-50%, -50%);
                        }
                        &>p{
                            color: #f9f90f;
                            position: absolute;
                            top: 6px;
                            z-index: 9;
                            left: 2px;
                        }
                    }
                }

            }
        }
    }
}
</style>
