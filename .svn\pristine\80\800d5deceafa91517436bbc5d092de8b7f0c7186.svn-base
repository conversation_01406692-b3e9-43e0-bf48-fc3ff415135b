<template>
    <transition name="menu-fade">
        <div v-show="isShowMenuVisible && menuItems.length > 0" class="base_menu" ref="base_menu" :style="menuStyles" :key="menuId">
            <div v-for="(item, index) in menuItems" :key="index"
                 class="menu-item"
                 :class="{ 'disabled': item.disabled }"
                 @click="handleItemClick(item)">
                <i v-if="item.icon" :class="item.icon"></i>
                <span>{{ item.label }}</span>
            </div>
        </div>
    </transition>
</template>

<script>
import Tool from '@/common/tool'
export default {
    name: 'BaseMenu',
    props: {
        // 菜单显示状态
        menuShow: {
            type: Boolean,
            default: false
        },
        // 菜单项配置
        menuItems: {
            type: Array,
            default: () => []
        },
        // 事件数据，支持 PointerEvent 类型
        eventData: {
            type: [Object, PointerEvent],
            default: () => ({
                clientX: 0,
                clientY: 0
            }),
            validator: function(value) {
                // 检查是否为 PointerEvent 或具有必要属性的对象
                return value instanceof PointerEvent ||
                       (typeof value === 'object' &&
                        ('clientX' in value || 'center' in value) &&
                        ('clientY' in value || 'center' in value));
            }
        }
    },
    data() {
        return {
            menuPosition: {
                x: 0,
                y: 0
            },
            // 菜单实例ID
            menuId: null,
            // 缓存的布局尺寸
            layoutDimensions: {
                headerHeight: 0,
                sidebarWidth: 0
            }
        }
    },
    computed: {
        isShowMenuVisible: {
            get() {
                return this.menuShow;
            },
            set(val) {
                if (!val) {
                    // 当菜单关闭时，从Vuex中注销
                    this.$store.dispatch('menu/closeMenu', this.menuId);
                }
                this.$emit("update:menuShow", val);
            }
        },
        // 为菜单添加样式计算属性，动态设置位置
        menuStyles() {
            return {
                left: `${this.menuPosition.x}px`,
                top: `${this.menuPosition.y}px`
            };
        }
    },
    created() {
        // 生成唯一的菜单ID
        this.menuId = Tool.genID();
    },
    beforeDestroy() {
        // 确保组件销毁时移除事件监听和注销菜单
        document.removeEventListener("click", this.handleClickOutside);
        this.$store.dispatch('menu/closeMenu', this.menuId);
    },
    mounted() {
        document.addEventListener("click", this.handleClickOutside);
        // 初始化时获取布局尺寸
        this.updateLayoutDimensions();
    },
    watch: {
        // 监听事件数据变化
        eventData: {
            handler(val) {
                if (this.isShowMenuVisible) {
                    // 当菜单显示时，注册到Vuex
                    this.$store.dispatch('menu/showMenu', {
                        id: this.menuId,
                        instance: this
                    });

                    // 当事件数据变化且菜单显示时，重新计算位置
                    this.calculateMenuPosition();
                    this.$nextTick(() => {
                        this.adjustMenuPosition();
                    });
                }
            },
            deep: true
        }
    },
    methods: {
        handleClickOutside(event) {
            if(this.$refs.base_menu && !this.$refs.base_menu.contains(event.target)) {
                this.isShowMenuVisible = false;
            }
        },

        handleItemClick(item) {
            if (item.disabled) {
                return;
            }
            if (item.handler) {
                item.handler();
            }
            this.isShowMenuVisible = false;
        },

        // 动态获取布局尺寸
        updateLayoutDimensions() {
            // 查找header元素并获取其高度
            const headerElement = document.querySelector('.header-bar, .header_bar');
            if (headerElement) {
                this.layoutDimensions.headerHeight = headerElement.offsetHeight;
            } else {
                // 如果找不到header元素，使用默认值
                this.layoutDimensions.headerHeight = 64;
            }

            // 查找左侧边栏元素并获取其宽度
            const sidebarElement = document.querySelector('.left-content, .app-sidebar');
            if (sidebarElement) {
                this.layoutDimensions.sidebarWidth = sidebarElement.offsetWidth;
            } else {
                // 如果找不到侧边栏元素，使用默认值
                this.layoutDimensions.sidebarWidth = 96;
            }
        },

        // 计算初始菜单位置（不考虑菜单尺寸）
        calculateMenuPosition() {
            // 确保布局尺寸是最新的
            this.updateLayoutDimensions();

            // 获取事件坐标，优先使用 clientX/clientY，其次使用 center
            const { clientX, clientY, center } = this.eventData;
            let menuX = clientX !== undefined ? clientX : (center?.x || 0);
            let menuY = clientY !== undefined ? clientY : (center?.y || 0);

            // clientX/clientY 是相对于整个浏览器窗口的坐标
            // 菜单使用 position: absolute，相对于 body 定位
            // 所以直接使用 clientX/clientY 作为菜单位置
            this.menuPosition = {
                x: menuX,
                y: menuY
            };
        },

        // 调整菜单位置（考虑菜单尺寸和窗口边界）
        adjustMenuPosition() {
            const contextMenu = this.$refs.base_menu;
            if (!contextMenu || !this.isShowMenuVisible) {
                return;
            }

            // 获取菜单和窗口尺寸
            const menuWidth = contextMenu.offsetWidth;
            const menuHeight = contextMenu.offsetHeight;
            // 菜单现在是相对于整个窗口定位的，所以使用完整的窗口尺寸
            const windowWidth = window.innerWidth;
            const windowHeight = window.innerHeight;

            // 调整位置，防止菜单超出窗口边界
            let { x: menuX, y: menuY } = this.menuPosition;

            // 边界检查 - 确保菜单不超出窗口边界
            if (menuX + menuWidth > windowWidth) {
                menuX = windowWidth - menuWidth;
            }
            if (menuY + menuHeight > windowHeight) {
                menuY = windowHeight - menuHeight;
            }
            if (menuX < 0) {
                menuX = 0;
            }
            if (menuY < 0) {
                menuY = 0;
            }

            // 更新位置信息
            this.menuPosition = { x: menuX, y: menuY };
        }
    }
}
</script>

<style lang="scss" scoped>
.base_menu {
    position: absolute;
    z-index: 3001;
    background: #ffffff;
    min-width: 120px;
    padding: 5px 0;
    border-radius: 4px;
    box-shadow: 0 3px 9px rgba(0, 0, 0, 0.12);
    border: 1px solid #e9e9e9;
    user-select: none;
    .menu-item {
        cursor: pointer;
        font-size: 14px;
        line-height: 1.6;
        padding: 7px 18px;
        color: #2c3e50;
        white-space: nowrap;
        word-break: keep-all;
        transition: background-color 0.15s ease-out, color 0.15s ease-out;
        border-radius: 0;

        &:hover {
            background-color: #eaf5ff;
            color: #007bff;
        }
        &.disabled {
            color: #999;
            cursor: not-allowed;

            &:hover {
                background-color: transparent;
            }
        }

        i {
            margin-right: 8px;
            font-size: 14px;
        }
    }
}

// 菜单显示/隐藏过渡动画
.menu-fade-enter-active,
.menu-fade-leave-active {
    transition: opacity 0.3s ease;
}

.menu-fade-enter,
.menu-fade-leave-to {
    opacity: 0;
}
</style>
