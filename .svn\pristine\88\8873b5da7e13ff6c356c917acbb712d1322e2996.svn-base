<template>
    <div>
        <CommonDialog
            class="multicenter"
            :title="title"
            :show.sync="visible"
            :close-on-click-modal="false"
            width="90%"
            height="90%"
            :modal="false"
            @closed="back"
            :footShow="false"
            @handleClickDialogClose="handleClickDialogClose"
        >
            <div class="multi_center_page" v-loading="loading">
                <div class="container">
                    <el-button @click="enterCreateMulticenter" type="primary">{{ $t('create_multicenter') }}</el-button>
                    <div class="multicenter_list">
                        <el-card
                            class="box-card"
                            v-for="(item, index) of list"
                            :key="index"
                            @click.native="enterMulticenter(item)"
                            v-show="
                                item.type != names.obstetric_qc_multicenter ||
                                ($checkPermission({regionPermissionKey:'obstetricalAI'}) && item.type === names.obstetric_qc_multicenter)
                            "
                        >
                            <span>{{ item.name }}</span>
                            <span class="tag" v-if="item.status === 0">{{ $t('pending_approval') }}</span>
                            <span class="tag" v-if="item.status === 2">{{ $t('to_be_configured') }}</span>
                            <span class="tag" v-if="item.status === 4">{{ $t('withdrawn') }}</span>
                        </el-card>
                    </div>
                </div>
                <router-view></router-view>
            </div>
        </CommonDialog>
        <CommonDialog
            :close-on-click-modal="false"
            :show.sync="isShowStep"
            width="50%"
            :modal="false"
            class="multicenter_step_dialog"
            :title="$t('tip_title')"
            @closed="closeStep"
            :footShow="false"
        >
            <div class="multicenter_step_container">
                <p>{{ $t('pending_approval_tip') }}</p>
                <el-steps :active="1" finish-status="success" align-center>
                    <el-step :title="$t('applying_tip')"></el-step>
                    <el-step :title="$t('multicenter_approval')"></el-step>
                    <el-step :title="$t('multicenter_config_tip')"></el-step>
                    <el-step :title="$t('multicenter_release')"></el-step>
                </el-steps>
            </div>
            <div class="multicenter_step_footer">
                <el-button @click="closeStep" class="close_step" type="default">{{ $t('back_button') }}</el-button>
                <el-button @click="withdrawalMulticenter" type="primary">{{
                    $t('withdrawal_of_application')
                }}</el-button>
            </div>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import service from "../service/multiCenterService.js";
import CommonDialog from "../MRComponents/commonDialog.vue"; //3rd change

export default {
    mixins: [base],
    name: "MulticenterPage",
    permission: true,
    components: {
        CommonDialog,
    },
    data() {
        return {
            loading: false,
            config: this.$store.state.multicenter.config,
            types: this.$store.state.multicenter.type,
            names: this.$store.state.multicenter.name,
            title: "",
            isShowStep: false,
            showId: 0,
            visible: false,
        };
    },
    computed: {
        list() {
            return this.$store.state.multicenter.list;
        },
        enterByGroup() {
            return this.$store.state.multicenter.enterByGroup;
        },
    },
    created() {},
    mounted() {
        this.$nextTick(() => {
            this.visible = true;
        });
        this.title = this.$t('multicenter_title');
        this.$root.eventBus.$off("refreshMultiCenterList").$on("refreshMultiCenterList", this.getMultiCenterList);
        this.getMultiCenterList();
        let anonymous = this.$store.state.multicenter.anonymous;
        if (anonymous && anonymous.id && anonymous.type) {
            this.enterMulticenter(anonymous);
        }
    },
    watch: {
        $route: function (to, from) {
            // 如果路由返回，则执行操作
            if (to.name === "multiCenter") {
                this.title = this.$t('multicenter_title');
                if (this.enterByGroup.cid > 0 || this.enterByGroup.fid > 0) {
                    this.back();
                }
            }
        },
    },
    methods: {
        getMultiCenterList(callback) {
            this.loading = true;
            service.getMultiCenterList(callback).then((res) => {
                this.loading = false;
                if (res.data.error_code === 0) {
                    this.$store.commit("multicenter/setMultiCenterList", res.data.data);
                    callback&&callback()
                }
            });
            service.getMultiCenterAllOptions().then((res) => {
                if (res.data.error_code == 0) {
                    this.$store.commit("multicenter/updateMCOptionList", res.data.data);
                }
            });
        },
        enterMulticenter(item) {
            if (item.status === 0) {
                // 审核中
                this.showId = item.id;
                this.showStep();
            } else if (item.status === 2) {
                // 待配置
                this.$store.commit("multicenter/setCurrentMulticenter", item);
                this.showSetting(item);
            } else if (item.status === 4) {
                // 已撤回
                this.$store.commit("multicenter/setCurrentMulticenter", item);
                const cid = window.vm.$route.params.cid;
                this.$router.push(`/main/index/chat_window/${cid}/multicenter/generic_multicenter/create/2`);
            } else {
                // 进入多中心
                const type = this.types[item.type];
                const config = this.config[item.type];
                const route = config.roleRoute[item.userInfo.role];
                this.title = item.name;
                this.$store.commit("multicenter/setCurrentConfig", config);
                this.$store.commit("multicenter/setCurrentMulticenter", item);
                let cid = window.vm.$route.params.cid;
                if (item.type == this.names.obstetric_qc_multicenter) {
                    if (this.$checkPermission({regionPermissionKey:'obstetricalAI'})) {
                        this.$router.push(`/main/index/chat_window/${cid}/multicenter/${type}/${route}`);
                    }
                } else {
                    this.$router.push(`/main/index/chat_window/${cid}/multicenter/${type}/${route}`);
                }
            }
        },
        clearMcData() {
            this.$store.commit("multicenter/updateEnterByGroup", { cid: 0, fid: 0 });
            this.$store.commit("multicenter/setCurrentConfig", null);
            this.$store.commit("multicenter/setCurrentMulticenter", null);
            this.$store.commit("multicenter/setAnonymous", {});
        },
        enterCreateMulticenter() {
            const cid = window.vm.$route.params.cid;
            this.$router.push(`/main/index/chat_window/${cid}/multicenter/generic_multicenter/create/1`);
        },
        showStep() {
            this.isShowStep = true;
        },
        closeStep() {
            this.isShowStep = false;
        },
        showSetting(item) {
            const cid = window.vm.$route.params.cid;
            const type = this.types[item.type];
            const config = this.config[item.type];
            const route = config.roleRoute[item.userInfo.role];
            this.title = item.name;
            this.$router.push(`/main/index/chat_window/${cid}/multicenter/generic_multicenter/setting`);
        },
        withdrawalMulticenter() {
            this.closeStep();
            this.loading = true;
            service
                .withdrawalMulticenter({
                    mcID: this.showId,
                })
                .then((res) => {
                    this.loading = false;
                    if (res.data.error_code === 0) {
                        this.getMultiCenterList();
                    }
                });
        },
        handleClickDialogClose(){
            console.log(this.$route)
            if(this.$route.name === 'multiCenter'){
                this.visible = false
            }else{
                this.back()
            }

        }
    },

    destroyed() {
        this.clearMcData();
    },
};
</script>
<style lang="scss">
.multicenter {
    .el-dialog:not(.self_define_height) {
        margin-top: 5vh !important;
        height: 80vh !important;
    }
    th,
    td {
        border: 1px solid #bbb;
        font-size: 14px;
        padding: 6px;
        text-align: left;
    }
    table {
        color: #333;
        border: 1px solid #bbb;
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
    }
    .multi_center_page {
        height: 100%;
        overflow: hidden;
        overflow-x: hidden;
        position:relative;
        .container {
            position: relative;
            overflow: auto;
            height: 100%;
            .multicenter_list {
                margin-top: 10px;
                .box-card {
                    margin-bottom: 10px;
                    cursor: pointer;
                    .tag {
                        color: #f00;
                        background: #f4ce9e;
                        border-radius: 8px;
                        padding: 4px 10px;
                        font-size: 12px;
                        margin-left: 20px;
                    }
                }
            }
        }
    }
}
.multicenter_step_dialog {
    .el-dialog {
        height: 300px !important;
    }
    .el-dialog__body {
        display: flex;
        flex-direction: column;
    }
    .multicenter_step_container {
        flex: 1;
        padding: 10px 20px;
        & > p {
            font-size: 16px;
            margin-bottom: 40px;
        }
    }
    .multicenter_step_footer {
        display: flex;
        justify-content: center;
        .close_step {
            margin-right: 20px;
        }
    }
}
</style>
