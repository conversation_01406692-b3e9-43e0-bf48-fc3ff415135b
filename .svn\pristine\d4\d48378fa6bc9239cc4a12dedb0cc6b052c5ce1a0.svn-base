<template>
    <keep-alive :include="keepAliveComponents" v-if="isRouterAlive">
        <router-view></router-view>
    </keep-alive>
</template>
<script>

import Tool from '@/common/tool.js';
import {configureGlobalNotifications} from './lib/configureGlobalNotifications.js';
export default {
    mixins: [],
    name: 'app',
    components: {},
    data(){
        return {
            isRouterAlive:false,
            keepAliveComponents: ['MainPage'] // 需要缓存的组件名称
        }
    },
    beforeCreate(){
        Tool.getBrowserUniqueId()
        configureGlobalNotifications(this);
    },
    created(){
        this.isRouterAlive = true
        console.error(`buildTime:${process.env.VUE_APP_BUILD_TIME}`)
    },
    mounted(){
        this.$nextTick(()=>{
            this.$root.eventBus.$off('reloadRouter').$on('reloadRouter',this.reloadRouter);
            this.$root.eventBus.$off('clearKeepAliveCache').$on('clearKeepAliveCache',this.clearKeepAliveCache);
        })
    },

    beforeDestroy() {
        // 清理事件监听器
        this.$root.eventBus.$off('reloadRouter', this.reloadRouter);
        this.$root.eventBus.$off('clearKeepAliveCache', this.clearKeepAliveCache);
    },
    methods: {

        reloadRouter () {
            this.$router.replace('/login')
        },

        clearKeepAliveCache() {
            // 清除keep-alive缓存的方法：
            // 1. 先移除MainPage从缓存列表
            this.keepAliveComponents = [];

            // 2. 强制重新渲染keep-alive组件
            this.isRouterAlive = false;
            this.$nextTick(() => {
                // 3. 重新启用router-view和keep-alive
                this.isRouterAlive = true;
                // 4. 重新设置需要缓存的组件
                this.keepAliveComponents = ['MainPage'];
            });
        },

    }
}

</script>
<style lang="scss">
/*重置element-ui样式*/
.el-checkbox{
    .el-checkbox__input.is-checked .el-checkbox__inner{
        background-color: #88aaa8;
        border-color: #80a3a1;
    }
    .el-checkbox__input.is-focus .el-checkbox__inner{
        border-color: #80a3a1;
    }
    .el-checkbox__inner:hover{
        border-color: #80a3a1;
    }
    .el-checkbox__label{
        color: #6a8f8d;
    }
    .el-checkbox__input.is-checked+.el-checkbox__label{
        color: #6a8f8d;
    }
}
.el-radio__input.is-disabled.is-checked .el-radio__inner{
    background-color: #779a98;
    border-color:#779a98;
}
body{
    &.el-popup-parent--hidden {
        padding-right: 0 !important;
    }
    .el-loading-mask{
        background-color: rgba(255,255,255,.5);
        .el-loading-spinner .path{
            stroke: #6b908e;
            stroke-width:4px;
        }
        &.is-fullscreen{
            .el-loading-text{
                color: #000;
                font-size: 24px;
            }
        }
    }
    .el-message{
        top:30% !important;
    }
    .el-message-box__message p{
        word-break: break-word;
    }
    .el-dialog__wrapper{
        // position:absolute;
        background:rgba(47, 47, 47,.7);
        .el-dialog{
            margin:0;
            margin:20vh auto 0;
            top:0;
            left:0;
            right:0;
            height:60%;
            .main_btn{
                background-color:#5A817E;
                border:1px solid #5A817E;
                padding:6px 14px;
                font-size:16px;
                color:#fff;
            }
            .el-input__inner,.el-textarea__inner{
                border-color:#999;
            }
            .el-input__inner:focus{
                outline:none;
                border-color:#5A817E;
            }
            .el-dialog__header{
                padding:4px 14px;
                height:36px;
                border-bottom:1px solid #ccc;
                .el-dialog__headerbtn{
                    top: 0px;
                    right: 4px;
                    font-size: 28px;
                    height: 36px;
                    i{
                        color: #333;
                        font-weight: bolder;
                    }
                    i:hover{
                        color:#7fa09e
                    }
                }
            }
            .el-dialog__body{
                padding:14px 14px;
                height:calc(100% - 36px);
                overflow:auto;
                position:relative;
            }
            .el-dialog__footer{
                padding:14px 14px;
                background: inherit;
            }

        }
    }
    .el-select{
        *::selection{
            background:#fff;
        }
    }
}
.el-popconfirm {
    .el-popconfirm__main {
        margin-top: 10px;
        margin-bottom: 10px;
    }
    .el-popconfirm__action {
        text-align: right;
    }
}
.textEllipsis{
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.normal_btn{
    border-radius: 10px;
    height: 50px;
    background-color: #5A817E;
    font-size: 18px;
    color:#fff;
    cursor:pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover{
        background-color: #6AB3AD;
    }
}
</style>
