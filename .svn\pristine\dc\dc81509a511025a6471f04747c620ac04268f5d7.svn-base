import Tool from '@/common/tool.js'
import {parseImageListToLocal,htmlUnescape,getTimestamp,pushImageToList,getFileIcon,transferPatientInfo} from '../lib/common_base'
import {uploadFile,pauseUpload,resumeUpload,cancelUpload,getOssOptions} from '@/common/oss/index'
import permissionManager from '@/common/permission/index.js'
export default {
    data(){
        return {
            uploadingTimer:{}
        }
    },
    computed:{
        supportFileTypeStrings(){
            let fileTypeStrings = ''
            if(this.systemConfig.serverInfo.SupportFileType){
                this.systemConfig.serverInfo.SupportFileType.forEach(item=>{
                    fileTypeStrings =fileTypeStrings +  `.${item},`
                })
            }

            return fileTypeStrings
        },
    },
    mounted(){
        this.$root.eventBus.$off('setStartVoiceMsg').$on('setStartVoiceMsg', this.setStartVoiceMsg);
        this.$root.eventBus.$off('setStopVoiceMsg').$on('setStopVoiceMsg', this.setStopVoiceMsg);
        this.$root.eventBus.$off('updateStartVoiceMsg').$on('updateStartVoiceMsg', this.updateStartVoiceMsg);
        this.$root.eventBus.$off('deleteStartVoiceMsg').$on('deleteStartVoiceMsg', this.deleteStartVoiceMsg);
    },
    methods:{
        submitTextMessage(){
            if(!Tool.checkConversationConnect(this.cid)){
                this.$message.error(this.$t('network_error_tip'))
                return
            }
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                this.$message.error(this.$t('no_speak_permission'));
                return;
            }
            let el=this.$refs.edit_content
            var text=el.innerHTML
            text=text.replace(/<div>/g,'\n');
            text=text.replace(/<br>/g,'\n');
            text=text.replace(/<\/div>/g,'');
            text=text.trim();
            text=text.replace(/&nbsp;/g,' ');
            text=text.trim();
            if (text=='') {
                el.innerHTML=''
                return ;
            }
            text=htmlUnescape(text);
            el.innerHTML=''
            this.$root.isScrollingList[this.cid] = false
            this.sendTextMessage({text});
            this.scrollToBottom();
        },
        sendTextMessage({text,quote_message}){
            var content_array=this.sliceContent(text,quote_message)
            var that=this;
            var controller=this.conversation.socket;

            controller.emit("send_messages",content_array,function(is_succ,data){
                that.sendMessageCallback(is_succ,data,controller.cid);
            });
            this.setSendingMessage(content_array)
        },
        sliceContent(content,quote_message = null){
            //将待发消息体切割成小块
            var start = 0;
            var length = 1024;
            var count=0
            var content_array = [];
            while(start < content.length) {
                var  sub_content = content.substr(start, length);
                start += length;
                count+=1
                let mentionList = []
                var timestamp=getTimestamp()
                if(Array.isArray(this.atUser)&&this.atUser.length>0){
                    this.atUser.forEach(item=> {
                        mentionList.push(item.uid)
                    });
                }

                content_array.push({
                    msg_type: this.systemConfig.msg_type.Text,
                    msg_body: sub_content,
                    text:sub_content,
                    timestamp:timestamp.time,
                    tmp_gmsg_id:Tool.genID(),
                    tmp_index:Tool.genID(3),
                    mentionList,
                    quote_message:quote_message,
                    quote_id:quote_message?.gmsg_id
                });

            };
            console.log(content_array,'content_array')
            return content_array
        },
        setSendingMessage(content_array,ocid){
            //将准备发送消息推送到消息记录
            let cid = ocid||this.cid
            let that=this;
            var arr=[]
            for(let i=0;i<content_array.length;i++){
                let content=Object.assign({},content_array[i])
                content.sender_id=this.user.uid //推回聊天记录时前端匹配头像和昵称用
                content.group_id=cid;
                // content.sendingTimer=setTimeout(()=>{
                //     content.sending=true;
                // },500)
                const attendee = this.conversationList[cid]?.attendeeList['attendee_'+content.sender_id]
                content.nickname =  (attendee.alias_name ? attendee.alias_name : attendee.nickname) || this.user.nickname;
                content.sending=true;
                content.sendFail=false;
                content.original_msg_body = content.msg_body
                content.msg_body=this.parseMessageBody(content.msg_body);
                if(!content.hasOwnProperty('tmp_index')){
                    content.tmp_index = Tool.genID(3)
                }
                content.timeout=setTimeout(function(){
                    //60s后没有收到确认则发送失败
                    that.$store.commit('conversationList/setSendFail',{
                        cid:cid,
                        tmp_gmsg_id:content.tmp_gmsg_id
                    })
                },that.systemConfig.serverInfo.client_send_chat_message_retry_timeout)
                //设置到chatList最后一条消息
                this.$store.commit('chatList/addMessage',content)
                arr.push(content)
            }
            let obj={
                list:arr,
                cid:cid,
                type:'append'
            }
            this.$store.commit('conversationList/setChatMessage',obj)
        },
        sendMessageCallback(is_succ, data,cid){
            var that=this;
            console.log('sendMessageCallback',is_succ, data,cid)
            //发送消息成功回调
            if (is_succ) {
                for(let message of data){
                    parseImageListToLocal([message],'url')
                    message.original_msg_body = message.msg_body
                    message.msg_body=this.parseMessageBody(message.msg_body);
                    message.patientInfo=transferPatientInfo(message);
                    if (message.sender_id!=this.user.uid) {
                        //发送者改变了，用于AI分析上传图片的流程
                        message.avatar=this.conversationList[message.group_id].attendeeList['attendee_'+message.sender_id].avatar
                        message.avatar_local=this.conversationList[message.group_id].attendeeList['attendee_'+message.sender_id].avatar_local
                        message.nickname=this.conversationList[message.group_id].attendeeList['attendee_'+message.sender_id].nickname
                    }
                    that.$store.commit('conversationList/sendAck',{
                        cid:message.group_id,
                        message:message
                    });
                    if(message.msg_type == this.systemConfig.msg_type.AI_ANALYZE){
                        parseImageListToLocal(message.ai_analyze.messages,'url')
                        let ignoreConsultationImages=false
                        let cid=message.group_id;
                        if (this.systemConfig.ServiceConfig.type.AiAnalyze==this.conversationList[cid].service_type) {
                            //AI分析图片不放入总图像列表里
                            //AI分析图片不放入-放入总列表
                            //ignoreConsultationImages=true
                        }
                        for(let item of message.ai_analyze.messages){
                            // item.url_local=this.getLocalImgUrl(item.url||'');
                            let obj=Object.assign({},item,true)
                            pushImageToList(obj,ignoreConsultationImages)
                        }
                    }
                    pushImageToList(message);
                    that.mapResourceIdWithImageID(message);
                }

            }else{
                setTimeout(()=>{
                    for(let message of data){
                        that.$store.commit('conversationList/setSendFail',{cid:message.group_id,tmp_gmsg_id:message.tmp_gmsg_id})
                    }
                },600)
                this.$message.error(this.$t('send_message_error'))
            }
        },
        reupload(index){
            let msg=this.chatMessageList[index]
            this.$store.commit('conversationList/deleteChatMessage',{
                cid:this.cid,
                index:index
            })
            if (msg.msg_type==this.systemConfig.msg_type.Sound) {
                let file=this.sound_chat_message_buffer_list[msg.fid]
                this.uploadSoundFile(file.file_info, file.buffer, 0);
                this.setSoundMessage(file.file_info)
            }else if(msg.msg_type==this.systemConfig.msg_type.OBAI||msg.msg_type==this.systemConfig.msg_type.Frame||msg.msg_type==this.systemConfig.msg_type.Cine){
                this.deleteImgInfoByImgId(msg.img_id);
                this.$MessageBox.alert(this.$t('reupload_frame_tip'))
                return ;
            }else{
                let file_id=msg.file_id;
                let reupload_file=null
                for(let item of this.$root.uploadList){
                    if (item.file_id==file_id) {
                        reupload_file=item.file;
                        this.removeFileFromUploadList(file_id)
                        break;
                    }
                }
                if (reupload_file) {
                    this.prepareFileMessage(reupload_file,index).then((message)=>{
                        if(message){
                            this.setSendingFile(message,index)
                            this.uploadAttachment(message, reupload_file, 0)
                        }
                    })


                }
            }
        },
        resend(index){
            //点击重发消息
            let message=this.chatMessageList[index]
            this.$store.commit('conversationList/deleteChatMessage',{
                cid:this.cid,
                index:index
            })
            var that=this;
            var controller=this.conversation.socket;
            controller.emit("send_messages",[message],function(is_succ,data){
                that.sendMessageCallback(is_succ,data,controller.cid);
            });
            this.setSendingMessage([message])
        },
        /*点击发送文件*/
        uploadPicture(){
            if(!Tool.checkSpeakPermission(this.cid, this.user.uid)){
                this.$message.error(this.$t('no_speak_permission'));
                return;
            }
            if(false == this.systemConfig.serverInfo.enable_file_transfer_function){
                this.$message.error(this.$t('banned_this_moment'));
                return false;
            }
            this.$refs.upload_picture_input.click();
            // document.getElementById("upload_picture").click()
        },
        uploadPictureStart(files){
            this.file_tag=new Date().valueOf();
            let index=0;//同时发生可能时间戳一样导致UI更新异常
            const limitFileSize = getOssOptions().bigFileSize
            for(let file of files){
                index++;
                this.isShowOperate=false;
                if (file.size > 20*1024 * 1024 * 1024) {
                    this.$message.error(`${this.$t('upload_max_text')}20GB`);
                    return;
                }
                if (file.size == 0) {
                    this.$message.error(`${this.$t('upload_min_text')}0M`);
                    return;
                }
                if (!this.isSupportFileType(file.name)) {
                    this.$message.error(this.$t('upload_forbidden_file_type_text'));
                    return;
                }
                if(!Tool.validateIllegalCharacters(file.name)){
                    this.$message.error(this.$t('filename_illegal_characters'));
                    return;
                }
                var is_w_db = 0;
                if (/image\/\w+/.test(file.type)) {
                    is_w_db = 1;
                    if (file.size > 20 * 1024 * 1024) {
                        this.$message.error(`${this.$t('upload_image_max_text')}20M`);
                        return;
                    }
                }else{
                    if (!window.main_screen.gateway.check) {
                        this.$message.error(this.$t('upload_net_error'));
                        return;
                    }
                }
                if((file.size >= limitFileSize)&&(window.vm.$store.state.systemConfig.serverInfo.network_environment===0)){
                    const expireDays = getOssOptions().expireDays
                    let tipsStr = this.$t('big_files_tips').replace("{limitFileSize}", limitFileSize/1024/1024 ).replace("{expireDays}",expireDays)
                    this.$MessageBox.confirm(tipsStr, this.$t('tip_title'), {
                        confirmButtonText: this.$t('confirm_txt'),
                        callback: action => {
                            if(action==='confirm'){
                                this.prepareFileMessage(file,index).then(msg => {
                                    if(msg){
                                        if(msg.error) { // msg.error为真，表示图片损坏，停止发送
                                            return
                                        }
                                        this.setSendingFile(msg, index)
                                        this.uploadAttachment(msg, file, is_w_db)
                                    }
                                })
                            }
                        }
                    });
                    console.log('>500M')
                    return
                }else{
                    this.prepareFileMessage(file,index).then(msg => {
                        if(msg){
                            if(msg.error) { // msg.error为真，表示图片损坏，停止发送
                                return
                            }
                            this.setSendingFile(msg, index)
                            this.uploadAttachment(msg, file, is_w_db)
                        }
                    })
                }
            }
        },
        compressImage(file) {
            return new Promise((resolve,reject)=>{
                const reader = new FileReader();

                reader.onload = (e) => {
                    const img = new Image();
                    img.onload = () => {
                        const canvas = document.createElement('canvas');
                        const ctx = canvas.getContext('2d');

                        const maxWidth = 300; // 设置最大宽度
                        const maxHeight = 300; // 设置最大高度

                        let width = img.width;
                        let height = img.height;

                        if (width > height) {
                            if (width > maxWidth) {
                                height *= maxWidth / width;
                                width = maxWidth;
                            }
                        } else {
                            if (height > maxHeight) {
                                width *= maxHeight / height;
                                height = maxHeight;
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        ctx.drawImage(img, 0, 0, width, height);

                        canvas.toBlob((blob) => {
                            const compressedFile = new File([blob], file.name, { type: file.type });
                            const compressedImageUrl = URL.createObjectURL(compressedFile);
                            resolve(compressedImageUrl)
                        }, file.type);
                    };
                    img.onerror = (err) => {
                        console.log("图片加载错误：", err);
                        reject(err);
                    };

                    img.src = e.target.result;

                };

                reader.readAsDataURL(file);
            })

        },
        prepareFileMessage: async function(file,index){
            if(!Tool.checkMainScreenConnected()){
                this.$Message.error(this.$t('network_error_tip'));
                return false
            }
            var msg = {
                file_id:  this.cid+'-'+this.user.uid+'-'+new Date().getTime()+'_'+index,
                group_id:this.cid,
                resource_file_size:file.size
            };
            let file_type=Tool.getFileType(file.name);
            let URL=window.URL||window.webkitURL
            const limitFileSize = getOssOptions().bigFileSize
            const isBigFile = file.size >= limitFileSize;
            if(isBigFile){
                //文件
                msg.msg_type = this.systemConfig.msg_type.File;
                msg.file_name=file.name
            }else{
                if (/image\/\w+/.test(file.type)) {
                    //图片
                    try {
                        const url = await this.compressImage(file)
                        msg.msg_type = this.systemConfig.msg_type.Image;
                        msg.url = url;
                        msg.url_local = url;
                        msg.file_name = file.name;
                    } catch (error) {
                        // 图片损坏，加载失败
                        msg.error = true;
                        URL.revokeObjectURL(msg.url);
                        this.$message(this.$t('image_corruption_text'));
                    }
                } else if(this.isVideoFile(file.type)){
                    //视频
                    msg.url=getFileIcon(file.name)
                    msg.url_local=msg.url
                    msg.msg_type = this.systemConfig.msg_type.Video;
                    msg.file_name="video."+file_type
                } else{
                    //文件
                    msg.msg_type = this.systemConfig.msg_type.File;
                    msg.file_name=file.name
                }
            }

            msg.file_name = Tool.encodeSpecialChars(msg.file_name)
            if(!msg.error) {
                //开始上传前将文件记录到上传队列用于重传
                this.$root.uploadList.push({
                    file_id:msg.file_id,
                    file:file
                })
            }

            return msg;
        },
        uploadAttachment(msg, file, is_w_db){
            var that=this;
            //OSS
            /////上传到OSS的视频文件，由于截图的功能问题，不保持原有文件名
            var attachment = false;
            if (this.systemConfig.msg_type.Video == msg.msg_type) {
                msg.original_file_name =  msg.file_name;
                msg.file_name = "video." + msg.file_name.substring(msg.file_name.lastIndexOf(".")+1);

                msg.thumb = "_thumb.jpg";
                msg.poster = "_poster.jpg";
            } else if (this.systemConfig.msg_type.Image == msg.msg_type) {
                msg.original_file_name =  msg.file_name;
                // msg.file_name = "image." + msg.file_name.substring(msg.file_name.lastIndexOf(".")+1);
                msg.thumb = "_thumb.jpg";
            } else {
                attachment = true;
            }
            let msg_body = {
                "file_id": msg.file_id,
                "file_name": msg.file_name,
                "original_file_name": msg.original_file_name,
                "attachment_storage_type":that.systemConfig.serverInfo.attachment_storage_type,
                "thumb": msg.thumb
            };
            if (this.systemConfig.msg_type.Video == msg.msg_type) {
                msg_body["poster"] = msg.poster;
            }
            msg.msg_body=JSON.stringify(msg_body)
            let filePath = that.systemConfig.serverInfo.oss_attachment_server.sub_dir + "/" + that.cid + '/' + that.user.uid + '/' + msg.file_id + '/' + msg.file_name;
            uploadFile({
                bucket:that.systemConfig.serverInfo.oss_attachment_server.bucket,
                filePath,
                file,
                callback:(event, data,uploadId)=>{
                    this.handleUploadCallback(event, data,uploadId,msg)
                }
            })
        },
        uploadReport(file, cb){
            var that=this;
            let URL=window.URL||window.webkitURL
            let dir = new Date().getTime() + parseInt(Math.random() * 1000 + 1000, 10)  // 目录
            var date = new Date();
            var time = date.getFullYear()
                + '-'
                + ((date.getMonth() < 9) ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1))
                + '-'
                + (date.getDate()  < 10 ? ('0' + date.getDate()) : date.getDate());
            let file_type= file.name.replace(/.+\./,'')
            let filePath = `HighFrameRateContrast/CaseData/${time}/${dir}/image.${file_type}`
            let url = URL.createObjectURL(file)

            //OSS
            uploadFile({
                bucket:window.vm.$store.state.systemConfig.serverInfo.multi_center.oss.bucket,
                filePath:filePath,
                file,
                callback:(event, data)=>{
                    console.log('uploadFile',event,data)
                    if ("complete" == event) {
                        if(cb) {
                            cb(null, {
                                url: filePath,
                                storage_type: 2
                            }, url)
                        }
                    } else if ("error" == event) {
                        if(cb) {
                            cb('error on uploadReport', null, '')
                        }
                    }
                }
            })
        },
        isVideoFile(type){
            type = type.substring(type.lastIndexOf("/")+1).toLowerCase();
            var ret = false;
            for (let key of this.systemConfig.VideoType) {
                if(key == type) {
                    ret = true;
                    break;
                }
            }
            return ret;
        },
        isSupportFileType(name) {
            const fileType = Tool.getFileType(name)
            const supportFileType = this.systemConfig.serverInfo.SupportFileType
            return supportFileType.includes(fileType)
        },
        setSendingFile(msg,index,doNotScroll){
            let that=this;
            msg.sender_id=this.user.uid //推回聊天记录时前端匹配头像和昵称用
            msg.group_id=this.cid
            msg.sending=false;
            msg.sendFail=false;
            msg.uploading=true;
            msg.uploadFail=false;
            msg.percent=0;
            msg.url_local= msg.url_local ? msg.url_local : msg.url;
            msg.tmp_gmsg_id=Tool.genID()
            msg.timestamp=getTimestamp().time
            if(!msg.hasOwnProperty('tmp_index')){
                msg.tmp_index = Tool.genID(3)
            }
            const attendee = this.conversationList[this.cid]?.attendeeList['attendee_'+this.user.uid]
            msg.nickname =  (attendee.alias_name ? attendee.alias_name : attendee.nickname) || this.user.nickname;
            //设置到chatList最后一条消息
            this.$store.commit('chatList/addMessage',msg)
            let obj={
                list:[msg],
                cid:this.cid,
                type:'append'
            }
            this.$store.commit('conversationList/setChatMessage',obj)
            if(!doNotScroll){   //分享群二维码到会话不需要滚动到底部
                this.scrollToBottom()
            }
        },
        updateSendingMessage(cid,file_id){
            //上传完文件更新为发送消息中
            let chatMessageList=this.$store.state.conversationList[cid].chatMessageList
            let result=null
            // console.log('updateSendingMessage',chatMessageList,cid,file_id)
            for (var i = chatMessageList.length - 1; i >= 0; i--) {
                //从聊天记录尾部遍历，提高效率
                let message=chatMessageList[i]
                if (message.file_id==file_id) {
                    this.$store.commit('conversationList/setMessageSending',{
                        cid:cid,
                        index:i
                    });
                    result=message
                    break
                }
            }
            return result;
        },
        removeFileFromUploadList(file_id){
            let uploadList=this.$root.uploadList
            //上传成功后从上传队列移除
            for(let i=0;i<uploadList.length;i++){
                if (uploadList[i].file_id==file_id) {
                    uploadList.splice(i,1);
                }
            }
        },
        initMachineTransfer(json_str){
            console.log('initMachineTransfer',json_str)
            let controllerImgMap=this.$root.controllerImgMap
            var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
            json.timestamp = new Date().getTime();
            var id = json.SessionId;
            if (id && id>0) {
                if (this.conversationList[id]) {
                    let imgId=json.ImgId
                    //记录上传检查信息
                    if (!controllerImgMap[imgId]) {
                        controllerImgMap[imgId] = [];
                    }
                    if (0 == json.ImgType) {
                        json.ImgType = this.systemConfig.msg_type.Frame;
                    } else if (1 == json.ImgType) {
                        json.ImgType = this.systemConfig.msg_type.Cine;
                    }
                    let file_id=json.ImgId+'_'+new Date().valueOf()
                    let msg_body = {
                        file_id: file_id,
                        img_id:json.ImgId,
                        exam_id:json.ExamId,
                    };
                    controllerImgMap[imgId].push({
                        cid:id,
                        file_id:file_id,
                        exam_id:json.ExamId,
                        img_id:json.ImgId,
                        imgId:json.ImgId,
                        msg_type:json.ImgType,
                        timestamp:json.timestamp,
                        msg_body:JSON.stringify(msg_body),
                        // patient_name:json.PatientName,
                        // patient_sex:json.PatientGender,
                        // patient_age:json.PatientAge,
                        // patient_age_unit:json.PatientAgeUnit,
                        // consultation_file_storage_type: "undefined" == typeof(json.ConsultationFileStorageType) ? 1 : json.ConsultationFileStorageType,
                    });
                    let uid=this.$store.state.user.uid;
                    let msg={
                        file_id:file_id,
                        img_id:json.ImgId,
                        exam_id:json.ExamId,
                        group_id:id,
                        url:'data:image/png;base64,'+json.Src,
                        url_local:'data:image/png;base64,'+json.Src,
                        msg_type:json.ImgType,
                        sender_id:uid,
                        is_istation:true,
                        patient_name:json.PatientName,
                        patient_sex:json.PatientGender,
                        patient_age:json.PatientAge,
                        patient_age_unit:json.PatientAgeUnit,
                        sending:false,
                        sendFail:false,
                        uploading:true,
                        uploadFail:false,
                        percent:0,
                        tmp_gmsg_id:Tool.genID(),
                        timestamp:getTimestamp().timestamp,
                        msg_body:JSON.stringify(msg_body),
                        tmp_index:Tool.genID(3),
                        // photometricInterpretation:image.photometricInterpretation
                    }
                    console.log('initMachineTransfer json_str%%%%%%%%%%%%%%%%%%',msg)
                    //设置到chatList最后一条消息
                    window.vm.$store.commit('chatList/addMessage',msg)
                    let obj={
                        list:[msg],
                        cid:id,
                        type:'append'
                    }
                    window.vm.$store.commit('conversationList/setChatMessage',obj)

                    this.uploadingTimer[msg.file_id]=setTimeout(()=>{
                        this.$store.commit('conversationList/updateUploadFail',{
                            cid:id,
                            file_id:msg.file_id
                        })
                    },this.systemConfig.serverInfo.client_send_chat_message_retry_timeout)
                } else {
                    console.log("[error] CWorkstationCommunicationMng.InitTransferState");
                }
            }
        },
        updateMachineTransfer(json_str){
            console.log('updateMachineTransfer',json_str)
            var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
            var img_id = json.ImgId;
            var list = this.getImgInfoByImgId(img_id);
            if (0 < list.length) {
                for (var image of list) {
                    var cid = image.cid;
                    if (cid>0) {
                        //更新进度
                        json.percent=json.Progress;
                        json.cid=cid;
                        json.file_id=image.file_id;
                        this.$store.commit('conversationList/updateProgressByImgId',json)
                        clearTimeout(this.uploadingTimer[image.file_id]);
                        this.uploadingTimer[image.file_id]=setTimeout(()=>{
                            this.$store.commit('conversationList/updateUploadFail',{
                                cid:cid,
                                file_id:image.file_id
                            })
                        },this.systemConfig.serverInfo.client_send_chat_message_retry_timeout)
                        console.log('UpdateProgress json_str%%%%%%%%%%%%%%%%%%',json_str)
                    }
                }
            }
        },
        finishMachineTransfer(json_str){
            console.log('finishMachineTransfer',json_str)
            var json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
            var img_id = json.ImgId;
            var list = this.getImgInfoByImgId(img_id);
            this.deleteImgInfoByImgId(img_id);
            if (0 < list.length) {
                for (var image of list) {
                    var cid = image.cid;
                    if (cid>0) {
                        //更新进度
                        if(json.Error){
                            json.percent=0;
                            image.progress=-1;
                        }else{
                            json.percent=100;
                            image.progress=100;
                        }
                        json.cid=cid;
                        json.file_id=image.file_id;
                        this.$store.commit('conversationList/updateProgressByImgId',json)
                        this.updateUploadProgress(image);
                        clearTimeout(this.uploadingTimer[image.file_id])
                        this.uploadingTimer[image.file_id]=null;
                        console.log('UpdateProgress json_str%%%%%%%%%%%%%%%%%%',json_str)
                    }
                }
            }
        },
        getImgInfoByImgId(imgId){
            var imgList = this.$root.controllerImgMap[imgId]||[];
            return imgList;
        },
        deleteImgInfoByImgId(imgId){
            this.$root.controllerImgMap[imgId]=null
        },
        transferBodyIfAnalyze(tempMsg){
            if (permissionManager.checkPermission({regionPermissionKey:'breastAI'})&&this.$store.state.conversationList[tempMsg.group_id].service_type==this.systemConfig.ServiceConfig.type.AiAnalyze&&tempMsg.msg_type==this.systemConfig.msg_type.Image) {
                //对AI分析发送图片时，转化为AI分析消息
                this.$message.success(this.$t('auto_analyze'))
                tempMsg.sender_id=this.$store.state.conversationList[tempMsg.group_id].fid;
                tempMsg.msg_type=this.systemConfig.msg_type.AI_ANALYZE;
                tempMsg.messages=[]
                tempMsg.messages.push({
                    msg_type:this.systemConfig.msg_type.Image,
                    msg_body:tempMsg.msg_body
                })
            }
        },
        setStartVoiceMsg(){
            this.setVoiceMsgCommon(this.systemConfig.msg_type.SYS_START_RT_VOICE);
        },
        setStopVoiceMsg(){
            this.setVoiceMsgCommon(this.systemConfig.msg_type.SYS_STOP_RT_VOICE);
        },
        updateStartVoiceMsg(){
            this.$store.commit('conversationList/updateStartVoiceMsg',{cid:this.cid})
        },
        deleteStartVoiceMsg(){
            this.$store.commit('conversationList/deleteStartVoiceMsg',{cid:this.cid})
        },
        setVoiceMsgCommon(type){
            const cid = this.$route.query.cid||this.$route.params.cid
            var timestamp=getTimestamp()
            let sending=type==this.systemConfig.msg_type.SYS_START_RT_VOICE
            let msg={
                msg_type:type ,
                msg_body: '',
                timestamp:timestamp.time,
                tmp_gmsg_id:Tool.genID(),
                sender_id:this.user.uid,
                sending:sending,
                group_id:cid,
                tmp_index:Tool.genID(3)
            }
            const attendee = this.conversationList[cid]?.attendeeList['attendee_'+this.user.uid]
            msg.nickname =  (attendee.alias_name ? attendee.alias_name : attendee.nickname) || this.user.nickname;
            let obj={
                list:[msg],
                cid:cid,
                type:'append',
            }
            this.$store.commit('conversationList/setChatMessage',obj)
            this.$store.commit('chatList/addMessage',msg)
        },
        uploadConsultationFile(option, cb) {
            //OSS
            uploadFile({
                bucket:this.systemConfig.serverInfo.oss_consultation_file_storage_server.bucket,
                filePath:option.filePath,
                file:option.file,
                callback:(event, data)=>{
                    if ("progress" == event) {
                        cb && cb (null, {
                            type:"progress",
                            percent: data > 99 ? 99 : data
                        });
                    } else if ("complete" == event) {
                        cb && cb (null, {
                            type:"progress",
                            percent: 100
                        });
                    } else if ("error" == event) {
                        cb && cb (null, {
                            type:"progress",
                            percent: -1
                        });
                    }
                }
            })
        },
        // DR血站初始化上传数据
        DealNotifySendFileToConversation(json_str){
            let that=this
            console.log('DealNotifySendFileToConversation: ',json_str)
            let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
            let controllerImgMap=this.$root.controllerImgMap
            let timestamp = new Date().getTime();
            let cid = json.consultation_id;
            let file_list=json.file_list;
            let serverInfo = window.vm.$store.state.systemConfig.serverInfo
            let upload_list={
                ConsultationFileStorageType: that.systemConfig.serverInfo.attachment_storage_type,
                ConsultationFileStorageOSSBucket: serverInfo.oss_attachment_server.bucket,
                ConsultationFileStorageOSSEndPoint: serverInfo.oss_attachment_server.end_point,
                ConsultationFileStorageOSSRecordSubDir: serverInfo.oss_attachment_server.sub_dir,
                soa: serverInfo.oss_attachment_server.soa,
                seo: serverInfo.oss_attachment_server.seo,
                vip: serverInfo.vip,
                file_list: []
            };

            // 必须是外网
            if (cid && cid>0 && file_list && file_list.length > 0 && that.systemConfig.serverInfo.attachment_storage_type==2) {

                let index=0;//同时发生可能时间戳一样导致UI更新异常
                for (let file of file_list) {
                    index++;
                    let file_id=cid+'-'+that.user.uid+'-'+timestamp+'-'+index;
                    let imgId=file_id;
                    let file_path=file;
                    file_path=file_path.replace(/\\/g, '/')
                    let file_name=file.substr(file_path.lastIndexOf('/')+1) || file_id;
                    //获取后缀
                    let ext = file_name.substr(file_name.lastIndexOf(".")+1);
                    let img_type=['png', 'jpg', 'jpeg', 'bmp', 'gif', 'webp', 'psd', 'svg', 'tiff'].indexOf(ext.toLowerCase()) !== -1 ? that.systemConfig.msg_type.Image : that.systemConfig.msg_type.File;
                    upload_list.file_list.push({
                        ImgId: file_id,
                        fileSrc: file,
                        fileDes: that.systemConfig.serverInfo.oss_attachment_server.sub_dir + "/" + cid + '/' + that.user.uid + '/' + file_id + '/' + file_name,
                    })
                    //记录上传检查信息
                    if (!controllerImgMap[imgId]) {
                        controllerImgMap[imgId] = [];
                    }
                    controllerImgMap[imgId].push({
                        cid:cid,
                        file_id: file_id,
                        img_id: imgId,
                        imgId: imgId,
                        msg_type: img_type,
                        timestamp: timestamp
                    });

                    let msg={
                        file_id:file_id,
                        group_id:cid,
                        sender_id:that.user.uid,
                        msg_type:img_type,
                        url: false,
                        url_local: false,
                        file_name: file_name,
                        sending:false,
                        sendFail:false,
                        uploading:true,
                        uploadFail:false,
                        percent:0,
                        is_localdb_msg: 0,
                        tmp_gmsg_id:Tool.genID(),
                        timestamp:getTimestamp().timestamp,
                        tmp_index:Tool.genID(3),
                        msg_body: '{"file_id":"'+file_id+'","file_name":"'+file_name+'","original_file_name":"'+file_name+'","attachment_storage_type":2}'
                    }
                    // console.log('DealNotifySendFileToConversation file:', msg)
                    if (that.conversationList[cid]) {
                        //设置到chatList最后一条消息
                        window.vm.$store.commit('chatList/addMessage',msg)
                        let obj={
                            list:[msg],
                            cid:cid,
                            type:'append'
                        }
                        window.vm.$store.commit('conversationList/setChatMessage',obj)
                        that.uploadingTimer[msg.file_id]=setTimeout(()=>{
                            that.$store.imgId('updateUploadFail',{
                                cid:cid,
                                ImgId:msg.file_id
                            })
                        },that.systemConfig.serverInfo.client_send_chat_message_retry_timeout)
                    } else {
                        that.$root.droc_auto_upload_temp[cid] = that.$root.droc_auto_upload_temp[cid] || []
                    }
                }
                if (that.conversationList[cid]) {
                    // console.error('DealNotifySendFileToConversation SendFileToOSS%%%%%%%%%%%%%%%%%%',upload_list)
                    window.CWorkstationCommunicationMng.SendFileToOSS(upload_list);
                } else {
                    // console.error('DealNotifySendFileToConversation openConversation%%%%%%%%%%%%%%%%%%',upload_list)
                    that.openConversation(cid,10,{},(is_suc,conversation)=>{
                        if(!is_suc){
                            console.log('DealNotifySendFileToConversation openConversation Fail!')
                            return
                        }else{
                            console.error('DealNotifySendFileToConversation openConversation success!')
                            // that.DealNotifySendFileToConversation(json_str)
                            // return
                        }
                    })

                    that.$root.droc_auto_upload_temp[cid] = that.$root.droc_auto_upload_temp[cid] || []
                    that.$root.droc_auto_upload_temp[cid].push(json_str)
                }
                //SendFileToOSS

            }
        },
        // DR血站更新上传进度
        UpdateSendFileToConversation(json_str){
            console.error('UpdateSendFileToConversation',json_str)
            let json = typeof json_str == 'string' ? JSON.parse(json_str) : json_str;
            let img_id = json.fileId;
            let list = this.getImgInfoByImgId(img_id);
            if (0 < list.length) {
                for (let image of list) {
                    let cid = image.cid;
                    console.log('UpdateSendFileToConversation cid',cid)
                    if (Number(cid)>0) {
                        //更新进度
                        if(json.Error){
                            json.percent=0;
                            image.progress=-1;
                        }else{
                            json.percent=Number(json.Progress);
                            image.progress=Number(json.Progress);
                        }
                        if (Number(json.Progress)>=100) {
                            //更新进度
                            json.percent=json.percent>100? 100 : json.percent;
                            image.progress=json.progress>100? 100 : image.progress;
                            json.cid=cid;
                            json.file_id=image.file_id;
                            this.$store.commit('conversationList/updateProgressByImgId',json)
                            this.updateUploadProgress(image);
                            clearTimeout(this.uploadingTimer[image.file_id])
                            this.uploadingTimer[image.file_id]=null;
                            console.log('UpdateSendFileToConversation json%%%%%%%%%%%%%%%%%%',json)
                        } else {
                            console.log('commit json',json)
                            json.percent=json.percent>100? 100 : json.percent;
                            json.cid=cid;
                            json.file_id=image.file_id;
                            this.$store.commit('conversationList/updateProgressByImgId',json)
                            clearTimeout(this.uploadingTimer[image.file_id]);
                            this.uploadingTimer[image.file_id]=setTimeout(()=>{
                                this.$store.commit('conversationList/updateUploadFail',{
                                    cid:cid,
                                    file_id:image.file_id
                                })
                            },this.systemConfig.serverInfo.client_send_chat_message_retry_timeout)
                            console.log('UpdateSendFileToConversation json_str%%%%%%%%%%%%%%%%%%',json)
                        }
                    }
                }
            }
        },
        mapResourceIdWithImageID(message){
            if(this.$store.state.device.isIStationInfoDR){
                let resource_id = message.resource_id
                let img_id = message.img_id||message.file_id
                if(!resource_id&&message.ai_analyze&&message.ai_analyze.messages&&message.ai_analyze.messages.length>0&&message.ai_analyze.messages[0].resource_id){
                    resource_id = message.ai_analyze.messages[0].resource_id
                    img_id = message.ai_analyze.messages[0].img_id || img_id
                }
                if(!resource_id&&message.ai_analyze_report&&message.ai_analyze_report.resource_id){
                    resource_id = message.ai_analyze_report.resource_id
                }
                if(!resource_id&&message.ai_analyze_list&&message.ai_analyze_list.length>0&&message.ai_analyze_list[0].resource_id){
                    resource_id = message.ai_analyze_list[0].resource_id
                }
                if(resource_id&&img_id&&this.$root.aiAnalyzeImgMap&&this.$root.aiAnalyzeImgMap[img_id]){
                    this.$root.aiAnalyzeImgMap[img_id].resource_id = resource_id
                }
            }
        },
        handleUploadStatus(message){
            if(!message.uploading||!message.uploadId){
                return
            }
            if(message.pauseUpload){
                this.handleReUpload(message.uploadId)
            }else{
                this.handlePauseUpload(message.uploadId)
            }
        },
        handleCancelUpload(message){
            if(!message.uploading){
                return
            }
            if(message.uploadId){
                cancelUpload(message.uploadId)
            }
            this.$store.commit("conversationList/deleteUploadChatMessage", {
                cid: this.cid,
                file_id:message.file_id
            });
        },
        handleReUpload(uploadId){
            console.log('handleReUpload')
            if(!Tool.checkConversationConnect(this.cid)){
                this.$message.error(this.$t('network_error_tip'))
                return
            }
            let msg = null
            for(let i = this.chatMessageList.length - 1; i >= 0; i--){
                if(this.chatMessageList[i].uploadId == uploadId){
                    msg = this.chatMessageList[i]
                }
            }
            resumeUpload({uploadId})
        },
        handlePauseUpload(uploadId){
            pauseUpload(uploadId)
            console.log('handlePauseUpload')
        },
        handleUploadCallback(event, data,uploadId,msg){
            console.log("uploadFile", event, data,uploadId);
            if(event === 'getOssClient'){
                console.error('client',data)
            }
            if ("progress" == event) {
                const params = {
                    msg,
                    percent:data,
                    uploadId:uploadId,
                    pauseUpload:false,
                    uploadError:false
                }
                this.$store.commit('conversationList/updateFileProgress',params)

            } else if ("complete" == event) {
                const params = {
                    ...msg,
                    percent:100,
                    progress:100,
                    cid:msg.group_id,
                    pauseUpload:false,
                    uploadError:false
                }
                this.updateUploadProgress(params)
            } else if ("error" == event) {
                let params = {
                    msg,
                    uploadId:uploadId,
                    uploadError:true,
                    pauseUpload:true,
                }
                if(data.name ==='pause'){
                    params.uploadError = false
                }
                this.$store.commit('conversationList/updateFileProgress',params)
                if(data.name=='tokenError'){
                    this.$store.commit("conversationList/deleteUploadChatMessage", {
                        cid: msg.group_id,
                        file_id:msg.file_id
                    });
                }
            }

            // this.$root.eventBus.$emit("updateProgressOSS", progressData);
        },
        updateUploadProgress(data){
            var that=this;
            let cid=data.cid;
            if (data.progress==100) {
                let controller=this.$store.state.conversationList[cid].socket
                let message=that.updateSendingMessage(cid,data.file_id)
                if (!message) {
                    //上传过程中取消上传，找不到message
                    return
                }
                console.log('before prepare step 1%%%%%%%%%%%%%%%%%%',message,data)
                data.msg_type=data.msg_type||message.msg_type;
                data.msg_body=data.msg_body||message.msg_body;
                if (data.msg_type==this.systemConfig.msg_type.File){
                    //其他文件类型直接发送消息
                    message.msg_type=data.msg_type
                    message.msg_body=data.msg_body
                    controller.emit("send_messages",[message],function(is_succ,data){
                        that.sendMessageCallback(is_succ,data,cid);
                    });

                    message.timeout=setTimeout(function(){
                        //60s后没有收到确认则发送失败
                        that.$store.commit('conversationList/setSendFail',{
                            cid:cid,
                            tmp_gmsg_id:message.tmp_gmsg_id
                        })
                    },that.systemConfig.serverInfo.client_send_chat_message_retry_timeout)
                }else{
                    let request_option={};
                    let target='';
                    //深拷贝消息体，不向服务器发送Url
                    let tempMsg=Object.assign({},message);
                    tempMsg.nickname='';
                    console.log('before prepare step 2%%%%%%%%%%%%%%%%%%',tempMsg)
                    if (data.msg_type==this.systemConfig.msg_type.Frame||data.msg_type==this.systemConfig.msg_type.Cine) {
                        request_option={
                            img_id: data.img_id,
                            exam_id: data.exam_id,
                            msg_type: data.msg_type,
                            consultation_file_storage_type: window.vm.$store.state.systemConfig.serverInfo.consultation_file_storage_type
                        };
                        target='request_generate_consultation_file_thumbnail';
                        tempMsg.msg_body = JSON.stringify({
                            img_id:tempMsg.img_id,
                            exam_id:tempMsg.exam_id,
                            timestamp:tempMsg.timestamp,
                            patient_name:tempMsg.patient_name,
                            patient_sex:tempMsg.patient_sex,
                            patient_age:tempMsg.patient_age,
                            patient_age_unit:tempMsg.patient_age_unit,
                            photometricInterpretation:tempMsg.photometricInterpretation,
                        });
                        tempMsg.url='';
                        tempMsg.url_local='';
                    }else if (data.msg_type==this.systemConfig.msg_type.Video||data.msg_type==this.systemConfig.msg_type.Image){
                        request_option={
                            type:data.msg_type,
                            body:JSON.parse(data.msg_body)
                        };
                        target='request_generate_thumbnail';
                        tempMsg.msg_body=data.msg_body;
                        tempMsg.url='';
                        tempMsg.url_local='';
                    }
                    const limitFileSize = getOssOptions().bigFileSize
                    const isBigFile = data.resource_file_size >= limitFileSize;

                    if (isBigFile) {
                        console.log('say message body%%%%%%%%%%%%%',tempMsg)
                        that.transferBodyIfAnalyze(tempMsg)
                        controller.emit("send_messages",[tempMsg],function(is_succ,data){
                            that.sendMessageCallback(is_succ,data,cid);
                        });
                        message.timeout=setTimeout(function(){
                            //60s后没有收到确认则发送失败
                            that.$store.commit('conversationList/setSendFail',{
                                cid:cid,
                                tmp_gmsg_id:message.tmp_gmsg_id
                            })
                        },that.systemConfig.serverInfo.client_send_chat_message_retry_timeout)
                    }else{
                        //图片或视频生成缩略图
                        controller.emit(target,request_option,function(oData){
                            let data = oData
                            if(typeof data === 'boolean'){
                                data = {
                                    error_code:data?0:-1
                                }
                            }
                            //图片上传完发送消息
                            if (data.error_code === 0) {
                                console.log('say message body%%%%%%%%%%%%%',tempMsg)
                                that.transferBodyIfAnalyze(tempMsg)
                                controller.emit("send_messages",[tempMsg],function(is_succ,data){
                                    that.sendMessageCallback(is_succ,data,cid);
                                });
                                message.timeout=setTimeout(function(){
                                    //60s后没有收到确认则发送失败
                                    that.$store.commit('conversationList/setSendFail',{
                                        cid:cid,
                                        tmp_gmsg_id:message.tmp_gmsg_id
                                    })
                                },that.systemConfig.serverInfo.client_send_chat_message_retry_timeout)

                            }else{
                                //截图失败
                                that.$message.error('request_generate_thumbnail error')
                                setTimeout(() => {
                                    that.$store.commit('conversationList/setSendFail',{
                                        cid:cid,
                                        tmp_gmsg_id:message.tmp_gmsg_id
                                    })
                                }, 1000);
                            }

                        })
                    }

                }
            }else if(data.progress==-1){
                //上传失败
                that.$store.commit('conversationList/updateUploadFail',{
                    cid:cid,
                    file_id:data.file_id
                })
            }else{
                //更新上传进度
                that.$store.commit('conversationList/updateFileProgress',{
                    msg:{
                        group_id:data.file_id.split('-')[0],
                        file_id:data.file_id
                    },
                    percent:data.progress
                })
            }
        },
    }
}
