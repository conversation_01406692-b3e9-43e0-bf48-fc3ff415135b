<template>
    <div class="ai-main">
        <div class="tab-container">
            <div class="custom-tabs-header">
                <!-- <div class="back-button" @click="goBack">
                    <i class="el-icon-arrow-left"></i>
                </div> -->
                <el-tabs v-model="activeTab" class="ai-chat-tabs ai-chat-tabs--small" @tab-click="handleTabClick">
                    <el-tab-pane :label="$t('ai_qa')" name="ai_chat"></el-tab-pane>
                    <el-tab-pane :label="$t('ai_clinical_thinking_training')" name="practice_overview"></el-tab-pane>
                    <el-tab-pane :label="$t('ultrasonic_quality_control_report')" name="ultrasound_report_qc_index" v-permission="{regionPermissionKey:'ultrasoundQCReport'}"></el-tab-pane>
                </el-tabs>
            </div>

            <div class="tab-content">
                <keep-alive>
                    <router-view></router-view>
                </keep-alive>
            </div>
        </div>
    </div>
</template>

<script>
import base from "../../../lib/base";
import Tool from "@/common/tool";
export default {
    mixins: [base],
    name: "AiMain",
    components: {},
    data() {
        return {
            activeTab: "ai_chat",
            role: "admin",//admin 主任  normal 医生
            lastActiveTab: "ai_chat",
        };
    },
    permission: true,
    watch: {
        $route: {
            immediate: true,
            handler(to) {
                this.setActiveTabByRoute(to.path);
            },
        },
    },
    created() {
        this.setActiveTabByRoute(this.$route.path);
    },
    methods: {
        goBack() {
            const cid = this.$route.params.cid;
            this.$router.replace(`/main/index/chat_window/${cid}`);
        },
        setActiveTabByRoute(path) {
            if (path.includes("/ai_plus/ai_chat")) {
                this.activeTab = "ai_chat";
                this.lastActiveTab = "ai_chat";
            } else if (path.includes("/ai_plus/practice_overview")) {
                this.activeTab = "practice_overview";
                this.lastActiveTab = "practice_overview";
            } else if (path.includes("/ai_plus/ultrasound_report_qc_index")) {
                this.activeTab = "ultrasound_report_qc_index";
                this.lastActiveTab = "ultrasound_report_qc_index";
            }
        },
        async handleTabClick(tab) {
            const cid = this.$route.params.cid;
            if(tab.name === this.lastActiveTab){
                return
            }

            let targetRoute;
            if (cid) {
                // 从聊天窗口进入的路由
                targetRoute = `/main/index/chat_window/${cid}/education/ai_plus/${tab.name}`;
            } else {
                // 直接访问的路由
                targetRoute = `/main/education/ai_plus/${tab.name}`;
            }

            await Tool.loadModuleRouter(targetRoute);
            this.lastActiveTab = tab.name;
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/aiChat.scss';
.ai-main {
    position: absolute;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 20;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
}

.tab-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    background: #fff;
}

.custom-tabs-header {
    display: flex;
    align-items: center;
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding-left: 40px;
    position: relative;

    .back-button {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 50px;
        height: 50px;
        cursor: pointer;
        margin-right: 10px;
        font-weight: 600;
        i {
            font-size: 25px;
            color: #000;
        }

        &:hover i {
            color: #409eff;
        }
    }
}

.custom-tabs {
    flex: 1;

    :deep(.el-tabs__header) {
        margin-bottom: 0;
        border-bottom: none;

        .el-tabs__nav-wrap {
            &::after {
                display: none;
            }
        }

        .el-tabs__nav {
            border: none;
        }

        .el-tabs__item {
            height: 80px;
            line-height: 80px;
            font-size: 20px;
            color: #000;
            border: none;

            &.is-active {
                font-weight: bold;
            }
        }

        .el-tabs__active-bar {
            height: 4px;
            background: $ai-theme-gradient;
            border-radius: 3px;
        }
    }
}

.tab-content {
    flex: 1;
    overflow: hidden;
    position: relative;
}

.coming-soon {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;

    h1 {
        font-size: 28px;
        color: #909399;
        font-weight: 400;
    }
}
</style>
