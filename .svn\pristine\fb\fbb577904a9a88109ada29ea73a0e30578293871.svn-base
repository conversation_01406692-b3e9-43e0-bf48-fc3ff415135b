import BasePermissionManager from './BasePermissionManager.js';

/**
 * 组件权限管理器
 * 负责组件级别的权限控制和显示隐藏逻辑
 */
class ComponentPermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.componentPermissions = new Map(); // 组件权限配置
        this.elementPermissions = new Map(); // 元素权限配置
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadComponentPermissions();
        this.loadElementPermissions();
    }

    /**
     * 加载组件权限配置
     */
    loadComponentPermissions() {
        const componentPermissions = {
            // 菜单组件权限
            'menu': {
                'system_setting': { roles: [1, 2, 3, 4, 5, 6], permissions: ['user'] },
                'system_info': { roles: [1, 2, 3, 4, 5, 6], permissions: ['user'] },
                'background_manage': { roles: [2, 3, 5], permissions: ['admin'] }
            },

            // 侧边栏组件权限
            'sidebar': {
                'admin_panel': { roles: [2, 3, 5], permissions: ['admin'] },
                'user_management': { roles: [2, 3, 5], permissions: ['user_manage'] },
                'group_management': { roles: [2, 3, 5], permissions: ['group_manage'] }
            },

            // 多中心组件权限
            'multicenter': {
                'admin_view': { roles: [5], permissions: ['super_admin'] },
                'judge_view': { roles: [4], permissions: ['judge'] },
                'annotation_view': { roles: [3], permissions: ['review'] },
                'assignment_view': { roles: [2], permissions: ['assignment'] },
                'normal_view': { roles: [1, 6], permissions: ['normal'] }
            },

            // 按钮组件权限
            'button': {
                'delete': { roles: [2, 3, 5], permissions: ['delete'] },
                'edit': { roles: [2, 3, 4, 5], permissions: ['edit'] },
                'create': { roles: [2, 3, 4, 5], permissions: ['create'] },
                'export': { roles: [2, 3, 5], permissions: ['export'] },
                'import': { roles: [2, 3, 5], permissions: ['import'] }
            },

            // 表格组件权限
            'table': {
                'user_role_edit': { roles: [2, 3, 5], permissions: ['user_role_edit'] },
                'data_export': { roles: [2, 3, 5], permissions: ['data_export'] },
                'batch_operation': { roles: [2, 3, 5], permissions: ['batch_operation'] }
            },

            // 对话框组件权限
            'dialog': {
                'user_edit': { roles: [2, 3, 5], permissions: ['user_edit'] },
                'group_edit': { roles: [2, 3, 5], permissions: ['group_edit'] },
                'organization_edit': { roles: [2, 3, 5], permissions: ['organization_edit'] }
            }
        };

        // 将配置存储到Map中
        Object.entries(componentPermissions).forEach(([component, permissions]) => {
            this.componentPermissions.set(component, permissions);
        });
    }

    /**
     * 加载元素权限配置
     */
    loadElementPermissions() {
        const elementPermissions = {
            // 基于ID的元素权限
            'admin-panel': { roles: [2, 3, 5], permissions: ['admin'] },
            'user-management-btn': { roles: [2, 3, 5], permissions: ['user_manage'] },
            'delete-btn': { roles: [2, 3, 5], permissions: ['delete'] },
            'edit-btn': { roles: [2, 3, 4, 5], permissions: ['edit'] },

            // 基于class的元素权限
            '.admin-only': { roles: [2, 3, 5], permissions: ['admin'] },
            '.super-admin-only': { roles: [5], permissions: ['super_admin'] },
            '.manager-only': { roles: [2, 3, 4, 5], permissions: ['manager'] }
        };

        Object.entries(elementPermissions).forEach(([element, config]) => {
            this.elementPermissions.set(element, config);
        });
    }

    /**
     * 检查组件权限
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(component, action = null, context = {}) {
        if (!this.isInitialized()) {
            console.warn('ComponentPermissionManager not initialized');
            return false;
        }

        // 如果只传入组件名，检查组件的基本访问权限
        if (!action) {
            return this.checkComponentAccess(component, context);
        }

        // 检查组件的特定操作权限
        return this.checkComponentActionPermission(component, action, context);
    }

    /**
     * 检查组件访问权限
     * @param {string} component - 组件名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkComponentAccess(component, context = {}) {
        const componentConfig = this.componentPermissions.get(component);

        if (!componentConfig) {
            // 如果没有配置，默认允许访问
            return true;
        }

        // 检查是否有任何一个操作的权限
        for (let [action, config] of Object.entries(componentConfig)) {
            if (this.checkPermissionConfig(config, context)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检查组件操作权限
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkComponentActionPermission(component, action, context = {}) {
        const componentConfig = this.componentPermissions.get(component);

        if (!componentConfig) {
            return true; // 没有配置默认允许
        }

        const actionConfig = componentConfig[action];
        if (!actionConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(actionConfig, context);
    }

    /**
     * 检查权限配置
     * @param {Object} config - 权限配置
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermissionConfig(config, context = {}) {
        // 检查角色权限
        if (config.roles && config.roles.length > 0) {
            const userRole = this.getUserRole();
            if (!config.roles.includes(userRole)) {
                return false;
            }
        }

        // 检查特定权限
        if (config.permissions && config.permissions.length > 0) {
            return config.permissions.every(permission =>
                this.checkSpecificPermission(permission, context)
            );
        }

        return true;
    }

    /**
     * 检查特定权限
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkSpecificPermission(permission, context = {}) {
        switch (permission) {
        case 'admin':
            return this.isAdmin();
        case 'super_admin':
            return this.isSuperAdmin();
        case 'user':
            return this.getUserId() !== null;
        case 'manager':
            return this.getUserRole() >= 2;
        case 'delete':
            return this.isAdmin();
        case 'edit':
            return this.getUserRole() >= 2;
        case 'create':
            return this.getUserRole() >= 2;
        case 'export':
            return this.isAdmin();
        case 'import':
            return this.isAdmin();
        case 'user_manage':
            return this.isAdmin();
        case 'group_manage':
            return this.isAdmin();
        case 'user_role_edit':
            return this.isAdmin();
        case 'data_export':
            return this.isAdmin();
        case 'batch_operation':
            return this.isAdmin();
        case 'user_edit':
            return this.isAdmin();
        case 'group_edit':
            return this.isAdmin();
        case 'organization_edit':
            return this.isAdmin();
        case 'judge':
            return this.getUserRole() === 4;
        case 'review':
            return this.getUserRole() === 3;
        case 'assignment':
            return this.getUserRole() === 2;
        case 'normal':
            return this.getUserRole() >= 1;
        default:
            return true;
        }
    }

    /**
     * 检查元素权限
     * @param {string} elementSelector - 元素选择器
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkElementPermission(elementSelector, context = {}) {
        const elementConfig = this.elementPermissions.get(elementSelector);

        if (!elementConfig) {
            return true; // 没有配置默认允许
        }

        return this.checkPermissionConfig(elementConfig, context);
    }

    /**
     * 获取组件可见性
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否可见
     */
    isComponentVisible(component, action = null, context = {}) {
        return this.hasPermission(component, action, context);
    }

    /**
     * 获取组件禁用状态
     * @param {string} component - 组件名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否禁用
     */
    isComponentDisabled(component, action = null, context = {}) {
        return !this.hasPermission(component, action, context);
    }

    /**
     * 批量检查组件权限
     * @param {Array} components - 组件配置数组 [{component, action, context}]
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(components) {
        const results = {};

        components.forEach(({ component, action, context = {}, key }) => {
            const permissionKey = key || `${component}${action ? '_' + action : ''}`;
            results[permissionKey] = this.hasPermission(component, action, context);
        });

        return results;
    }

    /**
     * 添加组件权限配置
     * @param {string} component - 组件名称
     * @param {Object} permissions - 权限配置
     */
    addComponentPermission(component, permissions) {
        this.componentPermissions.set(component, permissions);
    }

    /**
     * 添加元素权限配置
     * @param {string} elementSelector - 元素选择器
     * @param {Object} config - 权限配置
     */
    addElementPermission(elementSelector, config) {
        this.elementPermissions.set(elementSelector, config);
    }

    /**
     * 移除组件权限配置
     * @param {string} component - 组件名称
     */
    removeComponentPermission(component) {
        this.componentPermissions.delete(component);
    }

    /**
     * 移除元素权限配置
     * @param {string} elementSelector - 元素选择器
     */
    removeElementPermission(elementSelector) {
        this.elementPermissions.delete(elementSelector);
    }
}

export default ComponentPermissionManager;
