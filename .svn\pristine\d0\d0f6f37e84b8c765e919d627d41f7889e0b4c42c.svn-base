import { USER_ROLE } from './constant.js';

/**
 * 权限管理器基类
 * 定义权限管理器的通用接口和方法
 */
class BasePermissionManager {
    constructor() {
        this.permissions = new Map(); // 权限缓存
        this.cacheMetadata = new Map(); // 缓存元数据（包含过期时间等）
        this.userInfo = null; // 当前用户信息
        this.initialized = false; // 初始化状态
        this.cacheConfig = {
            defaultTTL: 5 * 60 * 1000, // 默认缓存5分钟
            maxCacheSize: 1000, // 最大缓存条目数
            enableCache: true // 是否启用缓存
        };
    }

    /**
     * 初始化权限管理器
     * @param {Object} userInfo - 用户信息
     * @param {Object} config - 配置信息
     */
    async initialize(userInfo, config = {}) {
        this.userInfo = userInfo;
        this.config = config;
        this.initialized = true;
        await this.loadPermissions();
    }

    /**
     * 加载权限数据 - 子类需要实现
     */
    async loadPermissions() {
        throw new Error('loadPermissions method must be implemented by subclass');
    }

    /**
     * 检查权限 - 子类需要实现
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission, context = {}) {
        throw new Error('hasPermission method must be implemented by subclass');
    }

    /**
     * 检查多个权限（AND逻辑）
     * @param {Array<string>} permissions - 权限标识数组
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否都有权限
     */
    hasAllPermissions(permissions, context = {}) {
        return permissions.every(permission => this.hasPermission(permission, context));
    }

    /**
     * 检查多个权限（OR逻辑）
     * @param {Array<string>} permissions - 权限标识数组
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有任一权限
     */
    hasAnyPermission(permissions, context = {}) {
        return permissions.some(permission => this.hasPermission(permission, context));
    }

    /**
     * 获取用户角色
     * @returns {number|string} 用户角色
     */
    getUserRole() {
        return this.userInfo?.role || 0;
    }

    /**
     * 获取用户ID
     * @returns {string|number} 用户ID
     */
    getUserId() {
        return this.userInfo?.uid || this.userInfo?.id;
    }

    /**
     * 获取用户类型
     * @returns {number} 用户类型
     */
    getUserType() {
        return this.userInfo?.type || 1;
    }

    /**
     * 检查是否为管理员
     * @returns {boolean} 是否为管理员
     */
    isAdmin() {
        const role = this.getUserRole();
        return role === USER_ROLE.ADMIN ||
               role === USER_ROLE.SUPER_ADMIN
    }

    /**
     * 检查是否为超级管理员
     * @returns {boolean} 是否为超级管理员
     */
    isSuperAdmin() {
        return this.getUserRole() === USER_ROLE.SUPER_ADMIN; // 超级管理员
    }

    /**
     * 更新用户信息
     * @param {Object} userInfo - 新的用户信息
     */
    updateUserInfo(userInfo) {
        const oldUserId = this.getUserId();
        const oldUserRole = this.getUserRole();

        this.userInfo = { ...this.userInfo, ...userInfo };

        const newUserId = this.getUserId();
        const newUserRole = this.getUserRole();

        // 如果用户ID或角色发生变化，清除所有缓存
        if (oldUserId !== newUserId || oldUserRole !== newUserRole) {
            this.clearCache();
        } else {
            // 只清除可能受用户信息影响的缓存
            this.clearCache(`_${oldUserId}_`);
        }

        this.loadPermissions();

        // 子权限管理器不应该触发权限变化事件，只有主权限管理器才触发
        // 这里不触发事件，由主权限管理器统一处理
    }

    /**
     * 触发权限变化事件
     * @param {Object} changeInfo - 变化信息
     */
    emitPermissionChange(changeInfo) {
        // 防重复触发机制
        const changeKey = `${changeInfo.oldRole}->${changeInfo.newRole}-${changeInfo.oldUserId}->${changeInfo.newUserId}`;
        const now = Date.now();

        // 如果在100ms内有相同的变更，则忽略
        if (this._lastChangeKey === changeKey && (now - this._lastChangeTime) < 100) {
            console.log('[Permission] Duplicate permission change ignored:', changeKey);
            return;
        }

        this._lastChangeKey = changeKey;
        this._lastChangeTime = now;

        // 只触发浏览器事件，避免重复触发
        if (typeof window !== 'undefined') {
            const event = new CustomEvent('permission:changed', {
                detail: changeInfo
            });
            window.dispatchEvent(event);
        }

        // 记录日志
        console.log('[Permission] Permission changed:', changeInfo);
    }

    /**
     * 生成缓存键
     * @param {string} type - 权限类型
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @returns {string} 缓存键
     */
    generateCacheKey(type, permission, context = {}) {
        const userId = this.getUserId() || 'anonymous';
        const userRole = this.getUserRole() || 0;

        // 基础键
        let key = `${type}_${permission}_${userId}_${userRole}`;

        // 添加重要的上下文信息到缓存键
        if (context.targetUserId) {
            key += `_target_${context.targetUserId}`;
        }
        if (context.conversationId) {
            key += `_conv_${context.conversationId}`;
        }
        if (context.departmentId) {
            key += `_dept_${context.departmentId}`;
        }

        return key;
    }

    /**
     * 检查缓存是否过期
     * @param {string} key - 缓存键
     * @returns {boolean} 是否过期
     */
    isCacheExpired(key) {
        if (!this.cacheConfig.enableCache) {
            return true;
        }

        const metadata = this.cacheMetadata.get(key);
        if (!metadata) {
            return true;
        }

        return Date.now() > metadata.expireTime;
    }

    /**
     * 清除权限缓存
     * @param {string} pattern - 可选的模式匹配，清除特定模式的缓存
     */
    clearCache(pattern = null) {
        if (pattern) {
            // 清除匹配模式的缓存
            const keysToDelete = [];
            for (const key of this.permissions.keys()) {
                if (key.includes(pattern)) {
                    keysToDelete.push(key);
                }
            }
            keysToDelete.forEach(key => {
                this.permissions.delete(key);
                this.cacheMetadata.delete(key);
            });
        } else {
            // 清除所有缓存
            this.permissions.clear();
            this.cacheMetadata.clear();
        }
    }

    /**
     * 清理过期缓存
     */
    cleanExpiredCache() {
        const now = Date.now();
        const expiredKeys = [];

        for (const [key, metadata] of this.cacheMetadata.entries()) {
            if (now > metadata.expireTime) {
                expiredKeys.push(key);
            }
        }

        expiredKeys.forEach(key => {
            this.permissions.delete(key);
            this.cacheMetadata.delete(key);
        });
    }

    /**
     * 控制缓存大小
     */
    evictCacheIfNeeded() {
        if (this.permissions.size <= this.cacheConfig.maxCacheSize) {
            return;
        }

        // 按照访问时间排序，删除最旧的缓存
        const entries = Array.from(this.cacheMetadata.entries())
            .sort((a, b) => a[1].accessTime - b[1].accessTime);

        const deleteCount = this.permissions.size - this.cacheConfig.maxCacheSize + 100; // 多删除一些，避免频繁清理

        for (let i = 0; i < deleteCount && i < entries.length; i++) {
            const key = entries[i][0];
            this.permissions.delete(key);
            this.cacheMetadata.delete(key);
        }
    }

    /**
     * 设置权限缓存
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {number} ttl - 生存时间（毫秒），可选
     */
    setCache(key, value, ttl = null) {
        if (!this.cacheConfig.enableCache) {
            return;
        }

        const now = Date.now();
        const expireTime = now + (ttl || this.cacheConfig.defaultTTL);

        this.permissions.set(key, value);
        this.cacheMetadata.set(key, {
            expireTime,
            accessTime: now,
            createTime: now
        });

        // 控制缓存大小
        this.evictCacheIfNeeded();
    }

    /**
     * 获取权限缓存
     * @param {string} key - 缓存键
     * @returns {any} 缓存值，如果不存在或过期返回undefined
     */
    getCache(key) {
        if (!this.cacheConfig.enableCache) {
            return undefined;
        }

        if (this.isCacheExpired(key)) {
            // 清除过期缓存
            this.permissions.delete(key);
            this.cacheMetadata.delete(key);
            return undefined;
        }

        // 更新访问时间
        const metadata = this.cacheMetadata.get(key);
        if (metadata) {
            metadata.accessTime = Date.now();
        }

        return this.permissions.get(key);
    }

    /**
     * 带缓存的权限检查包装器
     * @param {string} type - 权限类型
     * @param {string} permission - 权限标识
     * @param {Object} context - 上下文信息
     * @param {Function} checkFunction - 实际的权限检查函数
     * @returns {boolean} 权限检查结果
     */
    checkWithCache(type, permission, context, checkFunction) {
        const cacheKey = this.generateCacheKey(type, permission, context);

        // 先尝试从缓存获取
        const cachedResult = this.getCache(cacheKey);
        if (cachedResult !== undefined) {
            return cachedResult;
        }

        // 缓存未命中，执行实际检查
        const result = checkFunction();

        // 缓存结果
        this.setCache(cacheKey, result);

        return result;
    }

    /**
     * 检查是否已初始化
     * @returns {boolean} 是否已初始化
     */
    isInitialized() {
        return this.initialized;
    }

    /**
     * 销毁权限管理器
     */
    destroy() {
        this.permissions.clear();
        this.cacheMetadata.clear();
        this.userInfo = null;
        this.initialized = false;
    }

    /**
     * 获取缓存统计信息
     * @returns {Object} 缓存统计
     */
    getCacheStats() {
        const now = Date.now();
        let expiredCount = 0;

        for (const metadata of this.cacheMetadata.values()) {
            if (now > metadata.expireTime) {
                expiredCount++;
            }
        }

        return {
            totalEntries: this.permissions.size,
            expiredEntries: expiredCount,
            activeEntries: this.permissions.size - expiredCount,
            maxCacheSize: this.cacheConfig.maxCacheSize,
            cacheEnabled: this.cacheConfig.enableCache
        };
    }

    /**
     * 配置缓存设置
     * @param {Object} config - 缓存配置
     */
    configureCaching(config) {
        this.cacheConfig = { ...this.cacheConfig, ...config };

        // 如果禁用缓存，清除所有缓存
        if (!this.cacheConfig.enableCache) {
            this.clearCache();
        }
    }

    /**
     * 获取权限错误信息
     * @param {string} permission - 权限标识
     * @returns {string} 错误信息
     */
    getPermissionErrorMessage(permission) {
        return `您没有权限执行此操作: ${permission}`;
    }

    /**
     * 记录权限检查日志
     * @param {string} permission - 权限标识
     * @param {boolean} result - 检查结果
     * @param {Object} context - 上下文信息
     */
    logPermissionCheck(permission, result, context = {}) {
        if (window.console && window.console.debug) {
            console.debug(`[Permission Check] ${permission}: ${result}`, {
                userId: this.getUserId(),
                role: this.getUserRole(),
                context
            });
        }
    }
}

export default BasePermissionManager;
