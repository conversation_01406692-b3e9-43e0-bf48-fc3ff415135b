<template>
    <div>
        <CommonDialog
            class="system_setting"
            :title="$t('setting_title')"
            :show.sync="visible"
            :close-on-click-modal="false"
            width="40%"
            :modal="false"
            @closed="back"
            v-click-n-times:7="handleOpenDevDetail"
            :footShow="false"
        >
            <div class="system_setting_container">
                <div class="other">
                    <div class="item">
                        <p class="title">{{ $t("auto_forwarding") }}：</p>
                        <el-select v-model="auto_upload">
                            <el-option :label="$t('confirm_button_text')" value="1"></el-option>
                            <el-option :label="$t('cancel_button_text')" value="0"></el-option>
                        </el-select>
                    </div>
                    <div class="item" v-if="auto_upload == 1">
                        <div class="content">
                            {{ $t("auto_forwarding_for") }}{{ default_conversation.subject }}
                            <el-button @click="open_transmit" class="modify" type="primary" size="mini">{{
                                $t("modify_btn_text")
                            }}</el-button>
                        </div>
                    </div>
                    <div class="item">
                        <p class="title">{{ $t("auto_download_attachment") }}：</p>
                        <el-select v-model="auto_download.enable">
                            <el-option :label="$t('confirm_button_text')" :value="1"></el-option>
                            <el-option :label="$t('cancel_button_text')" :value="0"></el-option>
                        </el-select>
                    </div>
                    <div class="item" v-show="isShowAutoPushStream">
                        <p class="title">{{ $t("auto_push_stream") }}：</p>
                        <el-select v-model="auto_push_stream.enable">
                            <el-option :label="$t('confirm_button_text')" :value="1"></el-option>
                            <el-option :label="$t('cancel_button_text')" :value="0"></el-option>
                        </el-select>
                    </div>
                    <div class="item" v-if="auto_push_stream.enable == 1" v-show="isShowAutoPushStream">
                        <div class="content">
                            {{ $t("auto_push_stream_for") }}{{ auto_push_stream.subject }}
                            <el-button
                                @click="edit_auto_push_stream_default_target"
                                class="modify"
                                type="primary"
                                size="mini"
                                >{{ $t("modify_btn_text") }}</el-button
                            >
                        </div>
                        <div class="content" v-permission="{regionPermissionKey:'live'}">
                            <span>{{ $t("group_setting_is_live_record") }}：</span>
                            <el-radio v-model="auto_push_stream.record_mode" :label="0">{{
                                $t("cancel_button_text")
                            }}</el-radio>
                            <el-radio v-model="auto_push_stream.record_mode" :label="1">{{
                                $t("confirm_button_text")
                            }}</el-radio>
                        </div>
                    </div>
                    <div class="item" v-if="$checkPermission({regionPermissionKey:'live'}) && isCef">
                        <p class="title">{{ $t("default_push_way") }}：</p>
                        <el-select v-model="defaultPushWay" class="default_push_way">
                            <el-option :label="$t('ultrasound_seeding')" :value="'doppler'"></el-option>
                            <el-option :label="$t('desktop_direct_seeding')" :value="'desktop'"></el-option>
                        </el-select>
                    </div>
                    <div class="item" v-if="isCef">
                        <p class="title">{{ $t("mac_addr") }}：</p>
                        <el-input v-model="deviceInfo.device_id" :disabled="true"></el-input>
                    </div>
                    <div class="item" v-if="isCef">
                        <p class="title">{{ $t("machine_info_name") }}：</p>
                        <el-input
                            v-model.trim="device_name"
                            :placeholder="$t('undefined_name')"
                            :maxlength="16"
                        ></el-input>
                    </div>
                    <div class="item item_device">
                        <p class="title">{{ $t("bind_ultrasound_device_title") }}：</p>
                        <div v-if="bindULinkerDeviceId">
                            <!-- <span>已绑定的设备:{{ bindULinkerDeviceId }}</span> -->
                            <el-input v-model.trim="local_bind_uLinker_device_name" disabled></el-input>
                            <el-button @click="unbindULinker" type="danger">{{ $t("unbind") }}</el-button>
                        </div>
                        <div v-else>
                            <el-input
                                v-model.trim="remote_bind_uLinker_device_name"
                                readonly
                                :placeholder="$t('no_data_txt')"
                            ></el-input>
                            <el-button
                                @click="bindDeviceToULinker"
                                type="primary"
                                v-if="!bindULinkerDeviceId && remote_bind_uLinker_device_id"
                                >{{ $t("binding") }}</el-button
                            >
                        </div>
                    </div>
                    <div class="item" v-if="isCef && isShowSetClient">
                        <p class="title">{{ $t("AutoStart") }}：</p>
                        <el-select v-model="autoStart" class="default_push_way">
                            <el-option :label="$t('confirm_button_text')" :value="1"></el-option>
                            <el-option :label="$t('cancel_button_text')" :value="0"></el-option>
                        </el-select>
                    </div>
                    <div class="item" v-show="isCef && isShowSetClient">
                        <p class="title">{{ $t("cloud_statistics_client_type_short") }}：</p>
                        <div class="content">
                            <el-select v-model="clinetType" class="default_push_way">
                                <el-option :label="$t('AppWorkstation')" :value="'AppWorkstation'"></el-option>
                                <el-option :label="$t('AppClient')" :value="'AppClient'"></el-option>
                            </el-select>
                            <div class="info_tip">
                                <li v-if="oldClientConfig.clinetType != clinetType">
                                    {{ $t("effect_after_restart") }}
                                </li>
                                <li
                                    class="warning"
                                    v-if="oldClientConfig.clinetType != clinetType && clinetType == 'AppWorkstation'"
                                >
                                    {{ $t("switch_app_client_to_workstation_tip") }}
                                </li>
                            </div>
                        </div>
                    </div>
                    <div class="item">
                        <p class="title">{{ $t("camera_default_settings") }}：</p>
                        <el-select v-model="isCameraDefaultSetting">
                            <el-option :label="$t('confirm_button_text')" :value="true"></el-option>
                            <el-option :label="$t('cancel_button_text')" :value="false"></el-option>
                        </el-select>
                    </div>

                    <div class="item">
                        <p class="title">{{ $t("international_title") }}：</p>
                        <el-select v-model="curLanguage" @change="changeLang">
                            <el-option
                                v-for="item in languageOptions"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                            >
                            </el-option>
                        </el-select>
                    </div>
                </div>

                <template v-if="isShowDevDetail">
                    <p style="user-select: text">buildTime:{{ buildTime }}</p>
                    <div>
                        <el-button @click="downloadLog" type="primary" size="medium">download logs</el-button>
                    </div>
                </template>
                <div class="footer">
                    <el-button @click="submit" class="submit" type="primary" size="medium">{{
                        $t("submit_btn")
                    }}</el-button>
                </div>
            </div>
        </CommonDialog>
    </div>
</template>
<script>
import base from "../lib/base";
import Tool from "@/common/tool.js";
import service from "../service/service";
import { downloadLog } from "@/common/console";
import CommonDialog from "../MRComponents/commonDialog.vue"; //3rd change
import { setLanguage, getLanguage } from "@/common/i18n";
export default {
    mixins: [base],
    name: "systemSetting",
    components: { CommonDialog },
    model: {
        prop: 'value',
        event: 'input'
    },
    props: {
        value: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            downloadLog,
            intoWall: 0,
            push_image_mode: 1,
            auto_upload: "0",
            default_conversation: {},
            auto_download: {
                enable: 0,
            },
            auto_push_stream: {
                enable: 0,
                record_mode: 0,
            },
            defaultPushWay: "doppler",
            device_name: "",
            old_device_name: "",
            isShowDevDetail: false,
            remote_bind_uLinker_device_id: "",
            remote_bind_uLinker_device_name: "",
            local_bind_uLinker_device_name: "",
            bindULinkerDeviceId: "",
            autoStart: 1,
            clinetType: "AppClient",
            oldClientConfig: { autoStart: 1, clinetType: "AppClient" },
            curLanguage: "",
            languageOptions: [
                {
                    value: "CN",
                    label: "简体中文",
                },
                {
                    value: "EN",
                    label: "English",
                },
                {
                    value: "ES",
                    label: "Español",
                },
                {
                    value: "PTBR",
                    label: "Português",
                },
                {
                    value: "RU",
                    label: "Русский язык",
                },
                {
                    value: "DE",
                    label: "Deutsch",
                },
                {
                    value: "FR",
                    label: "Français",
                },
                {
                    value: "IT",
                    label: "Italiano",
                },
            ],
            isShowSetClient: false,
            isCameraDefaultSetting: false,
        };
    },
    computed: {
        // isShowMonitorWall(){
        //     return this.user.enable_monitor_wall&&this.user.role>1
        // },
        visible: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            }
        },
        isShowAutoPushStream() {
            return Tool.ifAppWorkstationClientType(this.systemConfig.clientType) && this.$checkPermission({regionPermissionKey:'live'});
        },
        isWorkStation() {
            return this.isCef && Tool.ifAppWorkstationClientType(this.systemConfig.clientType);
        },
        deviceInfo() {
            return this.$store.state.device;
        },
        buildTime() {
            return process.env.VUE_APP_BUILD_TIME;
        },
    },
    created() {
        this.initBindULinkerDeviceInfo();
        this.getOwnerULinkerDevice();
        this.getDeviceNameById();
        this.initCameraDefaultSetting();
        this.getAppClinetConfig();
        this.curLanguage = getLanguage();
    },
    mounted() {
        this.$nextTick(() => {
            this.intoWall = parseInt(this.user.is_into_tv_wall);
            //设置推流参数
            this.push_image_mode = window.catch_option.image_mode || 1;
            this.auto_upload = this.user.preferences.auto_upload;
            this.default_conversation = this.user.preferences.default_conversation;
            this.auto_download = JSON.parse(JSON.stringify(this.globalParams.auto_download));
            this.auto_push_stream = JSON.parse(JSON.stringify(this.globalParams.auto_push_stream));
            const defaultPushWay = window.localStorage.getItem("defaultPushWay");
            this.defaultPushWay = defaultPushWay ? defaultPushWay : "doppler";
        });
        this.bindMainScreenNotify();
    },
    beforeDestroy() {
        this.unBindMainScreenNotify();
    },
    methods: {
        submit() {
            if (this.user.is_into_tv_wall != this.intoWall) {
                this.modify_personal_fancy();
            }
            // if (this.push_image_mode!=(window.catch_option.image_mode || 1)) {
            //     this.modify_push_mode();
            // }
            if (
                this.auto_upload != this.user.preferences.auto_upload ||
                this.default_conversation.cid != this.user.preferences.default_conversation.cid ||
                this.default_conversation.fid != this.user.preferences.default_conversation.fid
            ) {
                this.modify_auto_upload();
            }

            if (this.auto_download.enable != this.globalParams.auto_download.enable) {
                this.modify_auto_download();
            }

            if (Tool.ifAppWorkstationClientType(this.systemConfig.clientType)) {
                if (
                    this.auto_push_stream.enable != this.globalParams.auto_push_stream.enable ||
                    this.auto_push_stream.value_type != this.globalParams.auto_push_stream.value_type ||
                    this.auto_push_stream.value != this.globalParams.auto_push_stream.value ||
                    this.auto_push_stream.record_mode != this.globalParams.auto_push_stream.record_mode
                ) {
                    this.modift_auto_push_stream();
                }
            }
            this.modify_default_push_way();
            if (this.isCef) {
                this.saveDeviceNameChange();
            }
            // 保存摄像头默认设置
            Tool.setCameraDefaultSetting(this.isCameraDefaultSetting);
            this.setAppClinetConfig();

            this.back();
        },
        modify_personal_fancy() {
            if (this.user.is_into_tv_wall == this.intoWall) {
                this.back();
                return;
            }
            service
                .modify_personal_fancy({
                    user_id: this.user.uid,
                    is_into_tv_wall: this.intoWall,
                })
                .then((res) => {
                    if (res.data.is_succ) {
                        this.$message.success(this.$t("update_success_text"));
                        this.$store.commit("user/updateUser", {
                            is_into_tv_wall: res.data.is_into_tv_wall,
                        });
                        // this.closeSettingIfNeed();
                    }
                });
        },
        modify_auto_upload() {
            //自动上传
            let data = {
                auto_upload: this.auto_upload,
                default_conversation: this.default_conversation,
            };
            const preferences = this.user.preferences;
            data = Object.assign(preferences, data);
            this.$root.socket.emit("set_user_other_info", data, (is_succ, info) => {
                let user = {
                    preferences: info,
                };
                this.$store.commit("user/updateUser", user);
                // this.closeSettingIfNeed();
            });
        },
        modify_auto_download() {
            //自动下载
            var auto_download_config = {
                enable: this.auto_download.enable,
                Frame: 1,
                Frame_DCM: 1,
                Cine: 1,
                Cine_DCM: 1,
            };
            this.$store.commit("globalParams/updateGlobalAutoDownload", auto_download_config);
            window.localStorage.setItem("auto_download", JSON.stringify(auto_download_config));
        },
        modift_auto_push_stream() {
            //自动推流
            if (Tool.ifAppWorkstationClientType(this.systemConfig.clientType)) {
                this.$store.commit("globalParams/updateGlobalAutoPushStream", this.auto_push_stream);
                window.localStorage.setItem("auto_push_stream_" + this.user.id, JSON.stringify(this.auto_push_stream));

                var json_data = {
                    error: 0,
                    record_mode: this.auto_push_stream,
                    event_name: "notify_update_device_cur_session",
                    type: this.systemConfig.DeviceInfoUpdateType.cur_session,
                };
                if (1 == this.auto_push_stream.enable) {
                    json_data.cur_session_id = this.auto_push_stream.value;
                    json_data.cur_session_type = this.auto_push_stream.value_type == "Conversation" ? 0 : 1;
                } else {
                    json_data.cur_session_id = 0;
                    json_data.cur_session_type = 0;
                }
                console.log(json_data);
                window.main_screen.controller.emit("notify_device_event", json_data);
            }
        },
        open_transmit() {
            this.$root.eventBus.$emit("openTransmit", {
                callback: this.setDefaultConversation,
                comfirm_msg: this.$t("auto_forwarding_for"),
            });
        },
        setDefaultConversation(item) {
            let default_conversation = {};
            if (item.cid) {
                default_conversation.cid = item.cid;
                default_conversation.type = 0;
                default_conversation.subject = item.subject;
            } else {
                default_conversation.fid = item.id;
                default_conversation.type = 2;
                default_conversation.subject = item.subject;
            }
            this.default_conversation = default_conversation;
        },
        edit_auto_push_stream_default_target() {
            this.$root.eventBus.$emit("openTransmit", {
                callback: this.editAutoPushStreamDefaultTarget,
                comfirm_msg: this.$t("auto_push_stream_for"),
                isServiceTypeNone: 1,
            });
        },
        editAutoPushStreamDefaultTarget(item) {
            if (!item) {
                return;
            }
            var auto_push_stream = {
                enable: this.auto_push_stream.enable,
                record_mode: this.auto_push_stream.record_mode,
            };

            if (item.cid) {
                auto_push_stream.value_type = "Conversation";
                auto_push_stream.value = item.cid;
                auto_push_stream.subject = item.subject;
            } else {
                auto_push_stream.value_type = "FriendId";
                auto_push_stream.value = item.id;
                auto_push_stream.subject = item.nickname;
            }

            this.auto_push_stream = auto_push_stream;
        },
        modify_default_push_way() {
            window.localStorage.setItem("defaultPushWay", this.defaultPushWay);
        },
        saveDeviceNameChange() {
            return new Promise((resolve, reject) => {
                if (!this.device_name) {
                    resolve(true);
                    return;
                }
                if (this.old_device_name === this.device_name) {
                    resolve(true);
                    return;
                }

                const params = {
                    deviceId: this.deviceInfo.device_id,
                    name: this.device_name,
                };
                window.main_screen.deviceRename(params, (res) => {
                    if (res.error_code === 0) {
                        this.$store.commit("device/updateDeviceInfo", { device_name: this.device_name });
                        resolve(true);
                    } else {
                        reject(false);
                    }
                });
            });
        },
        getDeviceNameById() {
            if (!this.deviceInfo.device_id) {
                return;
            }
            return new Promise((resolve, reject) => {
                const params = {
                    deviceId: this.deviceInfo.device_id,
                };
                window.main_screen.getDeviceNameById(params, (res) => {
                    if (res.error_code === 0) {
                        this.device_name = res.data.name;
                        this.old_device_name = this.device_name;
                        this.$store.commit("device/updateDeviceInfo", { device_name: res.data.name });
                        resolve(true);
                    } else {
                        reject(res.error_msg);
                    }
                });
            });
        },
        handleOpenDevDetail() {
            this.isShowDevDetail = true;
            localStorage.setItem("DEBUG_MODE", "DEBUG_MODE_2025!");
        },
        bindDeviceToULinker() {
            if (!this.remote_bind_uLinker_device_id) {
                return;
            }

            window.main_screen.sendMsgOwner(
                {
                    action: "sendBindDeviceToULinker",
                    body: {
                        deviceId: this.remote_bind_uLinker_device_id,
                        browserId: Tool.getBrowserUniqueId(),
                        status: 1,
                    },
                },
                (res) => {
                    this.$message.success(this.$t("instruction_sent"));
                }
            );
        },
        getOwnerULinkerDevice() {
            window.main_screen.getOwnClientInfo({}, (res) => {
                if (Array.isArray(res.data)) {
                    const deviceInfo = res.data.find(
                        (item) => item.client_type === this.systemConfig.client_type["UltraSoundMobile"]
                    );
                    if (deviceInfo) {
                        this.remote_bind_uLinker_device_id = deviceInfo.device_id;
                        let device_name = deviceInfo.device_name;
                        if (device_name) {
                            this.remote_bind_uLinker_device_name = `${device_name}_${this.remote_bind_uLinker_device_id}`;
                        } else {
                            this.remote_bind_uLinker_device_name = this.remote_bind_uLinker_device_id;
                        }
                    }
                }
            });
        },
        unbindULinker() {
            this.deleteBindULinkerDeviceIdFromStorage(this.bindULinkerDeviceId);
        },
        bindMainScreenNotify() {
            window.main_screen.controller.on("notify_msg_from_owner", this.handleNotifyMsrFromOwner);
        },
        unBindMainScreenNotify() {
            window.main_screen.controller.off("notify_msg_from_owner", this.handleNotifyMsrFromOwner);
        },
        handleNotifyMsrFromOwner(data) {
            console.error("handleNotifyMsrFromOwner", data);
            const action = data.action;

            if (action === "ackBindULinkerFromPC") {
                const deviceId = data.body.deviceId;
                this.$message.success(this.$t("U-Linker_agree_binding"));
                this.saveBindULinkerDeviceIdToStorage(deviceId);
            }
        },
        getBindULinkerDeviceIdFromStorage() {
            let bindULinkerDeviceId = "";
            try {
                // 从 localStorage 获取绑定设备ID，如果不存在则返回空字符串
                bindULinkerDeviceId = localStorage.getItem("bindULinkerDeviceId");

                // 如果 localStorage 中没有该设备ID，返回空字符串
                if (!bindULinkerDeviceId) {
                    return "";
                }

                return bindULinkerDeviceId;
            } catch (error) {
                console.error("Error reading bindULinkerDeviceId from localStorage:", error);
                return "";
            } finally {
                this.bindULinkerDeviceId = bindULinkerDeviceId;
            }
        },
        // 保存绑定设备ID到 localStorage
        saveBindULinkerDeviceIdToStorage(deviceId) {
            let bindULinkerDeviceId = "";
            try {
                // 获取当前的绑定设备ID
                bindULinkerDeviceId = this.getBindULinkerDeviceIdFromStorage();

                // 如果当前存储的设备ID与要保存的不同，则更新
                if (bindULinkerDeviceId !== deviceId) {
                    localStorage.setItem("bindULinkerDeviceId", deviceId);
                    console.log(`Device ${deviceId} has been saved.`);
                } else {
                    console.log(`Device ${deviceId} is already saved.`);
                }
            } catch (error) {
                console.error("Error saving bindULinkerDeviceId to localStorage:", error);
            } finally {
                // this.initBindULinkerDeviceInfo()
                this.bindULinkerDeviceId = deviceId;
                this.local_bind_uLinker_device_name = this.remote_bind_uLinker_device_name;
            }
        },
        // 从 localStorage 中删除绑定的设备ID
        deleteBindULinkerDeviceIdFromStorage() {
            let bindULinkerDeviceId = "";
            try {
                // 获取当前的绑定设备ID
                bindULinkerDeviceId = this.getBindULinkerDeviceIdFromStorage();

                // 如果设备ID存在，则删除
                if (bindULinkerDeviceId) {
                    localStorage.removeItem("bindULinkerDeviceId");
                    console.log(`Device ${bindULinkerDeviceId} has been removed from storage.`);
                } else {
                    console.log("No device found in storage to remove.");
                }
            } catch (error) {
                console.error("Error deleting bindULinkerDeviceId from localStorage:", error);
            } finally {
                console.error("bindULinkerDeviceId", bindULinkerDeviceId, this.bindULinkerDeviceId);
                this.bindULinkerDeviceId = "";
            }
        },
        getTargetDeviceNameById(device_id) {
            return new Promise((resolve, reject) => {
                const params = {
                    deviceId: device_id,
                };
                window.main_screen.getDeviceNameById(params, (res) => {
                    if (res.error_code === 0) {
                        resolve(res.data.name);
                    } else {
                        reject(res.error_msg);
                    }
                });
            });
        },
        async initBindULinkerDeviceInfo() {
            const bindULinkerDeviceId = this.getBindULinkerDeviceIdFromStorage();
            if (bindULinkerDeviceId) {
                const device_name = await this.getTargetDeviceNameById(bindULinkerDeviceId);
                if (device_name) {
                    this.local_bind_uLinker_device_name = `${device_name}_${bindULinkerDeviceId}`;
                } else {
                    this.local_bind_uLinker_device_name = bindULinkerDeviceId;
                }
            }
        },
        getAppClinetConfig() {
            Tool.createCWorkstationCommunicationMng({
                name: "RequestGetClientConfInfo",
                emitName: "NotifyRequestGetClientConfInfo",
                params: {},
                timeout: 5000,
            }).then((res) => {
                if (res.error) {
                    console.error("RequestGetClientConfInfo error");
                } else {
                    this.isShowSetClient = true;
                    const { autoStart = false, clinetType = "AppClient" } = res.data;
                    this.autoStart = autoStart ? 1 : 0;
                    this.clinetType = clinetType == "AppWorkstation" ? "AppWorkstation" : "AppClient";
                    this.oldClientConfig = { autoStart: this.autoStart, clinetType: this.clinetType };
                }
            });
        },
        setAppClinetConfig() {
            if (!this.isShowSetClient) {
                return;
            }
            let params = {};
            if (this.oldClientConfig.autoStart !== this.autoStart) {
                params.autoStart = !!this.autoStart;
            }
            if (this.oldClientConfig.clinetType !== this.clinetType) {
                params.clinetType = this.clinetType || "AppClient";
            }
            if (Object.keys(params).length > 0) {
                Tool.createCWorkstationCommunicationMng({
                    name: "RequestSetClientConfInfo",
                    emitName: "NotifyRequestSetClientConfInfo",
                    params,
                    timeout: 5000,
                }).then((res) => {
                    if (res.error) {
                        console.error("RequestSetClientConfInfo error");
                    }
                    const { autoStart = !!this.autoStart, clinetType = this.clinetType } = res.data || {};
                    this.autoStart = autoStart ? 1 : 0;
                    this.clinetType = clinetType == "AppWorkstation" ? "AppWorkstation" : "AppClient";
                    this.oldClientConfig = { autoStart: this.autoStart, clinetType: this.clinetType };
                });
            }
        },
        changeLang(lang) {
            if (this.isCef) {
                const params = {
                    language: lang,
                };
                Tool.createCWorkstationCommunicationMng({
                    name: "RequestSetClientConfInfo",
                    emitName: "NotifyRequestSetClientConfInfo",
                    params,
                    timeout: 5000,
                }).then((res) => {
                    if (res.error) {
                        console.error("RequestSetClientConfInfo error");
                        return;
                    }
                    this.$confirm(this.$t("effect_after_restart"), this.$t("tip_title"), {
                        confirmButtonText: this.$t("confirm_button_text"),
                        cancelButtonText: this.$t("cancel_button_text"),
                        type: "warning",
                    }).then(() => {
                        window.CWorkstationCommunicationMng.switchLanguage(lang);
                        this.$message.success(this.$t("change_language_success"));
                    }).catch(() => {});
                });
            } else {
                setLanguage(lang);
                this.$message.success(this.$t("change_language_success"));
            }
        },
        initCameraDefaultSetting() {
            this.isCameraDefaultSetting = Tool.getCameraDefaultSetting();
        },
        back() {
            // 关闭弹窗
            this.visible = false;
        },
    },
};
</script>
<style lang="scss">
.system_setting {
    user-select: none;
    .system_setting_container {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: 100%;
        .other {
            flex: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }
        .fancy,
        .other,
        .realtime_video,
        .language_list {
            .info_tip {
                color: #8d8d8d;
                margin-top: 5px;
            }
            .item {
                display: flex;
                flex-direction: row;
                padding-bottom: 10px;
                align-items: center;

                .title {
                    width: 300px;
                    font-size: 18px;
                    margin: 8px 0;
                }
                .content {
                    max-width: calc(100% - 300px);
                    font-size: 18px;
                    margin: 12px 0;
                    .modify {
                        margin-left: 14px;
                    }
                    .warning {
                        color: #e5a13b;
                    }
                }
                .default_push_way {
                    width: 260px;
                }
                .el-input {
                    width: 260px;
                }
            }
            .item_device {
                .el-button {
                    margin-left: 15px;
                }
            }
        }
        .footer {
            display: flex;
            height: 60px;
            justify-content: flex-end;
            padding: 10px 0;
            .submit {
                height: 40px;
                // margin:20px 10px;
                // position: absolute;
                // right: 0;
                // bottom: 0;
            }
        }
    }
}
</style>
