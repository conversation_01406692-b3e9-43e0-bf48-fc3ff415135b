import BaseEventHandler from './BaseEventHandler'

/**
 * 其他事件处理器
 * 处理媒体传输和设备相关的事件
 */
class OtherEventHandler extends BaseEventHandler {
    /**
     * 构造函数
     * @param {Object} vueInstance Vue实例
     * @param {Object} autoLoginManager 自动登录管理器
     * @param {Object} mainScreenManager 主屏幕管理器
     * @param {Object} eventListenerManager 事件监听管理器
     */
    constructor(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager) {
        super(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager)
    }
    /**
     * 初始化其他相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        // 媒体传输相关事件
        this.initMediaTransferEvents(controller)

        // 设备相关事件
        this.initDeviceEvents(controller)
        controller.on("notify_update_announcement", (data) => {
            this.NotifyUpdateAnnouncement(data)
        })
    }

    /**
     * 初始化媒体传输相关事件
     * @param {Object} controller 控制器
     */
    initMediaTransferEvents(controller) {
        controller.on("notify_update_media_transfer_task", (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/updateMediaTransferTasks', result.list)
            }
        })

        controller.on("notify_delete_media_transfer_task", (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/deleteMediaTransferTasks', result.list)
            }
        })

        // 查询媒体传输任务
        controller.emit("query_media_transfer_tasks", {}, (err, result) => {
            if (!err) {
                this.vm.$store.commit('taskList/initMediaTransferTasks', result.list)
            }
        })
    }

    /**
     * 初始化设备相关事件
     * @param {Object} controller 控制器
     */
    initDeviceEvents(controller) {
        controller.on('equipment_server_device_alram_update', (data) => {
            let deviceFailure = this.vm.$store.state.device.deviceFailure
            let num = deviceFailure[data.device_id] || 0
            if (data.status === 'NEW') {
                num++
            } else if (data.status === 'RESOLVE') {
                num > 0 ? num-- : 0
            }
            deviceFailure[data.device_id] = num
            this.vm.$store.commit('device/updateDeviceFailure', deviceFailure)
        })
    }

    /**
     * 根据ID获取设备名称
     */
    getDeviceNameById() {
        if (!this.vm.isCef) {
            return
        }
        if (!this.vm.$store.state.device.device_id) {
            setTimeout(() => {
                this.getDeviceNameById();
            }, 3000)
            return;
        }
        return new Promise((resolve, reject) => {
            const params = {
                deviceId: this.vm.$store.state.device.device_id,
            }
            window.main_screen.getDeviceNameById(params, (res) => {
                if (res.error_code === 0) {
                    this.vm.$store.commit('device/updateDeviceInfo', { device_name: res.data.name })
                    resolve(true)
                } else {
                    reject(res.error_msg)
                }
            })
        })
    }
    /**
     * 通知更新公告
     * @param {Object} data 公告数据
     */
    NotifyUpdateAnnouncement(data) {
        if (data.switch) {
            this.vm.$store.commit('globalParams/updateGlobalParams', {
                closedNotifyBar: false,
                announcementContent: data.content,
            })
        } else {
            this.vm.$store.commit('globalParams/updateGlobalParams', {
                closedNotifyBar: true,
                announcementContent: '',
            })
        }
    }
}

export default OtherEventHandler
