<!-- 统计下画廊-->
<template>
    <div v-if="isShowGallery">
        <el-dialog
            :close-on-click-modal="false"
            :visible.sync="isShowGallery"
            width="90%"
            append-to-body
            custom-class="obstetric_qc_multicenter_gallery_dialog"
            v-loading="loading"
            :before-close="destroyGallery"
        >
            <div class="content">
                <div class="content" v-loading="isLoading">
                    <canvas style="display: none" id="ai_canvas"></canvas>
                    <div class="left-gallery" ref="leftGallery">
                        <div class="gallery-top" id="gallery-box">
                            <div class="main_swiper mui-slider" id="my_slider" ref="topSwiper">
                                <div class="mui-slider-group">
                                    <div v-for="(file, f_index) in imageList" class="mui-slider-item" :key="f_index">
                                        <template>
                                            <template
                                                v-if="
                                                    file.msg_type == msg_type.Image ||
                                                    file.msg_type == msg_type.Frame ||
                                                    file.msg_type == msg_type.OBAI
                                                "
                                            >
                                                <div class="">
                                                    <!-- <div class="loading_span" v-loading="!file.loaded"></div> -->
                                                    <img
                                                        :src="isStructImage ? drawImageSrc(f_index) : realUrl(file)"
                                                        class="preview"
                                                        draggable="false"
                                                    />
                                                </div>
                                            </template>
                                            <template
                                                v-else-if="
                                                    file.msg_type == msg_type.Cine || file.msg_type == msg_type.Video
                                                "
                                            >
                                                <div class="loading_span" v-loading="!file.loaded"></div>
                                                <video
                                                    v-if="!isCef && f_index == currentSliderIndex"
                                                    class="main_video"
                                                    :poster="file.loaded ? file.realUrl : file.url_local"
                                                    :src="file.mainVideoSrc"
                                                    controls
                                                    @error="playVideoError(f_index)"
                                                ></video>
                                                <video
                                                    v-if="!isCef && file.img_has_gesture_video"
                                                    :poster="file.loaded ? file.realUrl : file.url_local"
                                                    :src="file.gestrueVideoSrc"
                                                    class="gesture_video"
                                                    controls
                                                ></video>
                                            </template>
                                            <template v-else>
                                                <img
                                                    src="static/resource_pc/images/poster2.jpg"
                                                    class="preview"
                                                    draggable="false"
                                                />
                                            </template>
                                        </template>
                                    </div>
                                </div>
                            </div>
                            <i
                                v-if="imageList.length > 2 && currentSliderIndex !== 0"
                                @click="prevImage"
                                class="iconfont iconright1"
                            ></i>
                            <i
                                v-if="imageList.length > 2 && currentSliderIndex < imageList.length - 1"
                                @click="nextImage"
                                class="iconfont iconright2"
                            ></i>
                        </div>
                        <div class="thumb_wrap">
                            <div class="thumb_loading" v-show="currentSliderIndex == -1" v-loading="true"></div>
                            <div ref="thumb_scroll_wrap" class="thumb_scroll_wrap">
                                <vue-slide :key="'thumb_'" class="thumb_slide" ref="thumb_slide" :ops="ops">
                                    <div @mousewheel.prevent.stop class="clearfix" style="display: flex">
                                        <div
                                            v-for="(file, f_index) in imageList"
                                            class="thumb_item"
                                            :class="{ current_thumb: f_index == currentSliderIndex }"
                                            @mousedown="mousedownThumb($event, f_index)"
                                            @mouseup="mouseupThumb($event, f_index)"
                                            :key="f_index"
                                        >
                                            <span
                                                v-show="
                                                    file.mc_resource_map &&
                                                    file.msg_type != systemConfig.msg_type.EXPIRATION_RES
                                                "
                                            >
                                                <span
                                                    v-for="(iconObj, index) in imageStandardIcon(file)"
                                                    :key="index"
                                                    :class="iconObj.css"
                                                    :title="iconObj.tips"
                                                >
                                                    {{ iconObj.label }}
                                                </span>
                                            </span>
                                            <!-- <span v-if="file.from_repository&&!file.reviewed" class="unread_tip"></span> -->
                                            <template
                                                v-if="
                                                    file.msg_type == msg_type.Image ||
                                                    file.msg_type == msg_type.Frame ||
                                                    file.msg_type == msg_type.OBAI
                                                "
                                            >
                                                <img
                                                    v-if="file.url"
                                                    :src="file.url"
                                                    class="preview"
                                                    draggable="false"
                                                />
                                                <div v-else class="empty_thump"></div>
                                                <i class="icon iconfont iconpicture"></i>
                                            </template>
                                            <template
                                                v-else-if="
                                                    file.msg_type == msg_type.Cine || file.msg_type == msg_type.Video
                                                "
                                            >
                                                <img :src="file.url" class="preview" draggable="false" />
                                                <i class="icon iconfont iconvideo_fill_light"></i>
                                            </template>
                                            <template v-else>
                                                <img
                                                    src="static/resource_pc/images/poster2.jpg"
                                                    class="preview"
                                                    draggable="false"
                                                />
                                            </template>
                                            <span
                                                v-if="file.imageType"
                                                class="image_tag"
                                                :class="'tag_' + file.imageType"
                                                >{{ imageTagType[file.imageType] }}</span
                                            >
                                            <p v-if="file.protocol_view_name" class="view_name">
                                                {{ file.protocol_view_name }}
                                            </p>
                                        </div>
                                    </div>
                                </vue-slide>
                            </div>
                            <i @click="lastPage" class="icon iconfont iconsanjiaoxing last_page"></i>
                            <i @click="nextPage" class="icon iconfont iconsanjiaoxing next_page"></i>
                        </div>
                    </div>
                    <div class="right-gallery" ref="rightGallery" :style="{ zIndex: onlyleft ? '-1' : '10' }">
                        <div class="report">
                            <slot>
                                <galleryDialog
                                    :currentFile="currentFile"
                                    :isShowDialog="true"
                                    :className="'content_all'"
                                    :isShowImageNumber="true"
                                    :isShowBackButton="false"
                                    @updataMCResourceMapRconsider="handleUpdataMCResourceMapRconsider"
                                    @updataLoadingStatus="handleUpdataLoadingStatus"
                                    @reloadImage="reloadImage"
                                    :index="currentSliderIndex"
                                ></galleryDialog>
                            </slot>
                        </div>
                    </div>
                </div>
                <div class="closebtn">
                    <i class="el-dialog__close el-icon el-icon-close" @click="closeDialog"></i>
                </div>
                <!-- <template #footer>
        </template> -->
            </div>
        </el-dialog>
    </div>
</template>

<script>
import base from "../../lib/base";
import galleryDialog from "./galleryDialog";
import { initVideoPage, initDcm, destroyGallery, getRealtimeConsultationSize } from "../../lib/common_realtimeVideo";
import {
    getRealUrl,
    imageStandardIcon,
    switchRealUrlToBColorUrl,
    getPositionOffsetFromUrl,
} from "../../lib/common_base";
import vueSlide from "vuescroll";
import { cloneDeep } from "lodash";
import Tool from "@/common/tool";
export default {
    name: "MrGallery",
    mixins: [base],
    permission: true,
    props: {
        currentExam: {
            type: Object,
            default: () => {
                return {};
            },
        },
        loading: {
            type: Boolean,
            default: false,
        },
        onlyleft: {
            type: Boolean,
            default: false,
        },
    },
    components: { vueSlide, galleryDialog },
    watch: {
        imageList: {
            handler(val, oldValue) {
                let temp = this.imageList[this.currentSliderIndex] || { mc_resource_map: {} };
                this.currentFile = { ...this.currentExam, ...{ mc_resource_map: {} }, ...temp };
                this.currentFile.mc_resource_map.mc_option = this.mc_options && this.mc_options.more_details;
            },
            deep: true,
        },
        currentExam:{
            handler(val, oldValue) {
                if (val ) {
                    this.currentFile = this.currentFile
                }
            },
            deep: true,

        },
        "currentFile": {
            handler(newValue, oldValue) {
                if (newValue.ai_report && oldValue.ai_report) {
                    if (newValue.ai_report.finshed) {
                        let new_isReconsider = newValue.isReconsider || false;
                        let new_reconsider_report = newValue.reconsider_report || {};
                        let old_reconsider_report = oldValue.reconsider_report || {};

                        if (new_reconsider_report.finshed != old_reconsider_report.finshed) {
                            this.preLoad(this.currentSliderIndex, true);
                        }
                    }
                }
            },
            immediate: true,
            deep: true,
        },
    },
    computed: {
        mc_options() {
            return this.$store.state.multicenter.optionList[
                this.$store.state.multicenter.obstetricEarlyPregnancy.mcOpId
            ];
        },
        currentConfig() {
            return this.$store.state.multicenter.currentConfig;
        },
        msg_type() {
            return this.systemConfig.msg_type;
        },
    },
    data() {
        return {
            getPositionOffsetFromUrl,
            switchRealUrlToBColorUrl,
            isShowGallery: false,
            imageStandardIcon,
            currentSliderIndex: -1,
            swiperTop: null,
            mousedownThumpPoint: null,
            exam: null,
            isShowReject: false,
            rejectContent: "",
            sliderTimer: 0, //图片预加载
            rejcetClick: false,
            imageList: [],
            ops: {
                vuescroll: {
                    mode: "slide",
                    sizeStrategy: "percent",
                    detectResize: true,
                    /** 锁定一种滚动方向， 锁定的方向为水平或者垂直方向上滑动距离较大的那个方向 */
                    locking: true,
                },
                scrollPanel: {
                    scrollingY: false,
                },
            },
            playRealtimeVideo: false, //是否正在播放实时视频
            currentFile: { mc_resource_map: {} }, //当前图片
            isExpand: false,
            isPlayVideo: false,
            mc_resource_maps: {}, //img_id为key的
            imageTag: [],
            isStructImage: false,
            displayColors: [],
            isDisplayBColor: false, //是否显示b
            isLoading: false, //申请复议时是否在分析中
        };
    },
    mounted() {
        // this.$root.eventBus.$off('expandRightWidth').$on('expandRightWidth',this.expandRightWidth)
        // this.$root.eventBus.$off('closeVideo').$on('closeVideo',this.closeVideo)
        // this.$root.eventBus.$off('openBeforeVideo').$on('openBeforeVideo',this.openBeforeVideo)
        // this.$root.eventBus.$off('regainVideo').$on('regainVideo',this.regainVideo)
        // document.addEventListener('visibilitychange',()=>{
        //     var isHidden = document.hidden;
        //     if(isHidden){
        //         this.destroyGallery()
        //     }
        // })
        this.$nextTick(() => {
            var that = this;
            this.$root.eventBus
                .$off("updateAiReportInMulitcenter")
                .$on("updateAiReportInMulitcenter", this.updateAiReportInMulitcenter);
        });
    },
    beforeDestroy() {
        this.destroyGallery();
    },
    methods: {
        updateAiReportInMulitcenter(msg) {
            for (let i in this.imageList) {
                let image = this.imageList[i];
                if (
                    msg.mc_resource_map &&
                    msg.mc_resource_map.webim_exam_id == image.exam_id &&
                    msg.mc_resource_map.img_id == image.file_id
                ) {
                    this.imageList[i].mc_resource_map = msg.mc_resource_map;
                }
            }
        },
        closeDialog() {
            this.isShowGallery = false;
            this.isLoading = false;
        },
        prevImage() {
            if (this.currentSliderIndex != 0) {
                this.slideTop(this.currentSliderIndex - 1);
            }
        },
        nextImage() {
            if (this.currentSliderIndex < this.imageList.length) {
                this.slideTop(this.currentSliderIndex + 1);
            }
        },
        deleteDuplicatingImg(list) {},
        openGallery(image, index, exam) {
            this.isDisplayBColor = false;
            this.isShowGallery = true;
            this.isDisplayBColor = false;
            // console.log('openGallery',exam)
            let mc_resource_map_ids = [];
            let listObj = {};
            this.mc_resource_maps = (Object.keys(exam.mc_resource_maps_info) || []).reduce((h, k) => {
                let v = exam.mc_resource_maps_info[k];
                h[v.resource_id] = v;
                return h;
            }, {});
            for (let k in exam.image_list || []) {
                let v = exam.image_list[k];
                if (v && v.mc_resource_map) {
                    if (listObj[v.img_id]) {
                        if (
                            listObj[v.img_id].score &&
                            v.score &&
                            parseFloat(v.score) > parseFloat(listObj[v.img_id].score)
                        ) {
                            listObj[v.img_id] = v;
                            if (
                                image &&
                                image.mc_resource_map &&
                                image.mc_resource_map.type == v.mc_resource_map.type
                            ) {
                                image = v;
                            }
                        }
                    } else {
                        listObj[v.img_id] = v;
                        if (image && image.mc_resource_map && image.mc_resource_map.type == v.mc_resource_map.type) {
                            image = v;
                        }
                    }
                }
            }
            this.imageList =
                Object.values(listObj || {}).filter((v) => {
                    return v;
                }) || [];
            // console.error('this.imageList:',this.imageList)
            // this.imageList = deDuplicatingImg(cloneDeep(list));
            this.imageList.map((item, i) => {
                if (image.mc_resource_map.type == item.mc_resource_map.type) {
                    index = i;
                }
                if (item.loaded) {
                    return item;
                } else {
                    return (item.realUrl = getRealUrl(item, this.msg_type));
                }
            });
            this.$nextTick(() => {
                this.initSwiper(image, index);
                if (this.onlyleft) {
                    this.$refs.leftGallery.style.width = "100%";
                }
            });
        },
        destroyGallery() {
            this.isShowGallery = false;
            destroyGallery();
            if (!this.isCef) {
                //浏览器关闭画廊停止播放视频
                let videos = document.querySelectorAll("video");
                for (let video of videos) {
                    video.pause();
                }
            }
        },
        closeVideo() {
            destroyGallery();
        },
        openBeforeVideo() {
            this.changeHandler(this.currentSliderIndex);
        },
        initSwiper(image, index) {
            var that = this;
            document.querySelector(".mui-slider").addEventListener("slide", that.swiperTopChange);

            var gallery = window.mui(".mui-slider");
            this.swiperTop = gallery.slider();

            this.positionToIndex(index);
            this.changeHandler(index);
        },
        positionToIndex(index) {
            this.slideTop(index);
        },
        slideTop(index) {
            if (index >= this.imageList.length) {
                this.currentSliderIndex =0;
                index = 0;
            }
            this.currentSliderIndex = index;
            if (!this.swiperTop) {
                var gallery = window.mui(".mui-slider");
                this.swiperTop = gallery.slider();
            }
            this.$nextTick(() => {
                if(this.swiperTop){
                    this.swiperTop.gotoItem(index, 0);
                }
                this.changeHandler(index);
            });
        },

        updateSwiper() {
            if (this.swiperTop) {
                //图片张数改变时更新画廊
                console.log("updateSwiper");
                this.swiperTop.refresh();
                window.gallery = this;
                setTimeout(() => {
                    this.slideThumb(this.currentSliderIndex);
                }, 300);
            }
        },
        slideThumb(index) {
            let thumb_slide = this.$refs.thumb_slide;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            let left = index * 157 - scroll_width / 2 + 78;
            thumb_slide && thumb_slide.scrollTo({ x: left }, 100);
        },
        mousedownThumb(event, index) {
            console.log(index);
            this.mousedownThumpPoint = {
                x: event.x,
                y: event.y,
            };
        },
        mouseupThumb(event, index) {
            let offsetX = this.mousedownThumpPoint.x - event.x;
            let offsetY = this.mousedownThumpPoint.y - event.y;
            if (Math.abs(offsetX) < 20 && Math.abs(offsetY) < 20) {
                if (index == this.currentSliderIndex) {
                    this.initDcmIfNeed(this.currentFile);
                    return;
                }
                this.slideThumb(index);
                this.slideTop(index);
                // console.error(this.swiperTop)
            }
        },
        initDcmIfNeed(file) {
            if (
                file &&
                file.msg_type == this.systemConfig.msg_type.Frame &&
                file.img_encode_type == this.systemConfig.file_type.DCM
            ) {
                //打开dicom播放器
                var dcm_url = file.url.replace("thumbnail.jpg", "SingleFrame.DCM");
                window.CWorkstationCommunicationMng.DisplayDrImage({
                    dcm_url: dcm_url,
                    thumbnail: file.url,
                    img_id: file.img_id,
                });
            }
        },
        lastPage() {
            let thumb_slide = this.$refs.thumb_slide;
            console.log("thumb_slide:", thumb_slide);
            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left -= scroll_width;
            console.log("left:", left);
            thumb_slide && thumb_slide.scrollTo({ x: left }, 150);
        },
        nextPage() {
            let thumb_slide = this.$refs.thumb_slide;
            console.log("thumb_slide:", thumb_slide);
            let left = thumb_slide.getPosition().scrollLeft;
            let thumb_scroll_wrap = this.$refs.thumb_scroll_wrap;
            let scroll_width = thumb_scroll_wrap.clientWidth;
            left += scroll_width;
            console.log("left:", left);
            thumb_slide.scrollTo({ x: left }, 150);
        },
        expandRightWidth(width) {
            console.log("MdGallery right gallery emit ");
            this.$refs.rightGallery.style.width = width;
            if (width == "60%") {
                this.isExpand = true;
            } else {
                this.isExpand = false;
            }
            let domRect = getRealtimeConsultationSize(this.$refs.topSwiper);
            initVideoPage({ file: this.currentFile, domRect });
            initDcm(this.currentFile);
        },
        regainVideo() {
            if (this.isPlayVideo) {
                window.CWorkstationCommunicationMng.showRealTimeVideo({ type: 4 });
            }
        },
        swiperTopChange(event) {
            this.currentSliderIndex = this.swiperTop.getSlideNumber();
            this.slideThumb(this.currentSliderIndex);
            this.changeHandler(this.currentSliderIndex);
        },
        changeHandler(index) {
            this.isStructImage = true;
            this.isDisplayBColor = false;
            this.displayColors = [];
            this.currentFile = this.imageList[index];
            this.currentFile = { ...this.currentExam, ...{ mc_resource_map: {} }, ...this.currentFile };

            //部分测试代码
            // this.currentFile.mc_resource_map = this.mc_resource_maps[this.currentFile.img_id] || this.mc_resource_maps[{}]
            this.currentFile = this.setObjMCResourceMap(this.currentFile);
            // this.currentFile.mc_resource_map = this.currentFile.mc_resource_map||{}
            this.currentFile.mc_resource_map.mc_option = this.mc_options && this.mc_options.more_details;
            console.log("changeHandler", this.currentFile);
            let domRect = getRealtimeConsultationSize(this.$refs.topSwiper);
            initVideoPage({ file: this.currentFile, domRect });
            initDcm(this.currentFile);
            let systemConfig = this.systemConfig;
            if (
                this.currentFile.msg_type == systemConfig.msg_type.Video ||
                this.currentFile.msg_type == systemConfig.msg_type.Cine ||
                this.currentFile.msg_type == systemConfig.msg_type.RealTimeVideoReview ||
                this.currentFile.msg_type == systemConfig.msg_type.VIDEO_CLIP
            ) {
                this.isPlayVideo = true;
            } else {
                this.isPlayVideo = false;
            }
            if (this.isExpand) {
                this.expandRightWidth("60%");
            }
            //快速划过不预加载图片
            this.preLoad(index, true);
            clearTimeout(this.sliderTimer);
            this.sliderTimer = setTimeout(() => {
                if (index - 1 >= 0) {
                    this.preLoad(index - 1, true);
                }
                if (index + 1 < this.imageList.length) {
                    this.preLoad(index + 1, true);
                }
            }, 300);
        },
        setObjMCResourceMap(imageObj) {
            // imageObj.mc_resource_map = this.mc_resource_maps[this.currentFile.resource_id] || this.mc_resource_maps[{}]
            return imageObj;
        },
        realUrl(imageObj) {
            let realUrl = getRealUrl(imageObj);
            if (this.isDisplayBColor) {
                realUrl = this.switchRealUrlToBColorUrl(realUrl);
            }
            return realUrl;
        },
        drawImageSrc(f_index) {
            if (this.isStructImage && this.displayColors.length < 1 && f_index != this.currentSliderIndex) {
                let url = this.imageList[f_index].realUrl;
                if (this.isDisplayBColor) {
                    url = this.switchRealUrlToBColorUrl(url);
                }
                return url;
            } else {
                return this.imageList[f_index].realUrlStruct;
            }
        },
        loadImageList(realUrl,nowTime){
            return new Promise((resolve, reject) => {
                const realImage = new Image();
                realImage.onerror = reject;
                realImage.onload = () => resolve(realImage);
                realImage.setAttribute("crossOrigin", "anonymous");
                realImage.src = `${realUrl}?temp=${nowTime}`;
            });
        },
        async combinationImage (imageList,offset){
            return new Promise((resolve, reject) => {
                if(offset.length>2){
                    let max_width = 0
                    let max_height = 0
                    let off_width = offset[0]
                    let off_height = offset[1]

                    const canvas = document.getElementById("ai_mul_view_canvas");
                    const ctx = canvas.getContext('2d');
                    for(let i=0;i<offset.length;i++){
                        if(i%2==0){
                            max_width  = max_width>offset[i] + imageList[i%2].naturalWidth? max_width : offset[i] + imageList[i%2].naturalWidth
                            max_height = max_height> offset[i+1] + imageList[i%2].naturalHeight? max_height : offset[i+1] + imageList[i%2].naturalHeight
                        }
                    }
                    canvas.width = max_width- off_width;
                    canvas.height  = max_height - off_height ;
                    for(let i=0;i<offset.length;i++){
                        if(i%2==0){
                            ctx.drawImage(imageList[i%2], offset[i]-off_width, offset[i+1] - off_height );
                        }
                    }
                    const mergedDataUrl = canvas.toDataURL('image/png');
                    const newRealImage = new Image();
                    newRealImage.onload = () => resolve(newRealImage);
                    newRealImage.src = mergedDataUrl;
                }else{
                    resolve(imageList[0])
                }
            })
        },
        preLoad(index, force = false) {
            var that = this;
            // let imageObj = index==this.currentSliderIndex? this.currentFile : this.imageList[index]
            let imageObj = that.imageList[index]; //Object.assign({},index==this.currentSliderIndex? this.currentFile : this.imageList[index]);
            if (!imageObj) {
                return;
            }
            imageObj = that.setObjMCResourceMap(imageObj);
            imageObj.preloading = imageObj.preloading || false;
            imageObj.loaded = imageObj.loaded || false;
            if (!force) {
                if (imageObj.loaded || imageObj.url == "") {
                    return;
                }
                if (imageObj.preloading) {
                    //图片未加载完成5s内重复触发不执行，5s后如果仍未加载完成可再次触发
                    setTimeout(() => {
                        imageObj.preloading = false;
                    }, 5000);
                    return;
                }
                imageObj.preloading = true;
            } else {
            }
            let realUrl = imageObj.url;
            let tempRealUrl = "";
            realUrl = getRealUrl(imageObj);
            if (this.isDisplayBColor) {
                realUrl = this.switchRealUrlToBColorUrl(realUrl);
            }

            if (this.systemConfig.serverInfo.network_environment === 1) {
                realUrl = Tool.replaceInternalNetworkEnvImageHost(realUrl);
            }

            if (
                (imageObj.msg_type == that.systemConfig.msg_type.Image ||
                    imageObj.msg_type == that.systemConfig.msg_type.OBAI ||
                    imageObj.msg_type == that.systemConfig.msg_type.Frame ||
                    imageObj.msg_type == that.systemConfig.msg_type.Video ||
                    imageObj.msg_type == that.systemConfig.msg_type.Cine) &&
                (!imageObj.loaded || force)
            ) {
                //预加载大图
                (async ()=>{
                    let resource_id = imageObj.resource_id;
                    let offset = [0,0];
                    let grayImageList = [];
                    const nowTime = Date.now();
                    if (this.isDisplayBColor) {
                        offset = await that.getPositionOffsetFromUrl(realUrl);
                        for(let i=0;i<offset.length;i++){
                            if(i%2==0){
                                grayImageList.push(
                                    this.loadImageList( this.switchRealUrlToBColorUrl(realUrl,i/2+1),nowTime)
                                )
                            }
                        }
                    }
                    if(grayImageList.length<1){
                        grayImageList =[this.loadImageList( realUrl,nowTime)]
                    }
                    Promise.all(grayImageList).then(async images => {
                        console.log('所有图片加载完成:', images.length);
                        let canvasUrl = "";
                        const realImage = await this.combinationImage(images,offset)
                        if (!force) {
                            imageObj.realUrl = that.drawCanvasToImageForMCResource(offset, imageObj, realUrl, realImage);
                            imageObj.realUrlStruct = imageObj.realUrl;
                            imageObj.loaded = true;
                            imageObj.preloading = false;
                            that.$set(that.imageList, index, imageObj);
                        } else {
                            imageObj.realUrlStruct = that.drawCanvasToImageForMCResource(
                                offset,
                                imageObj,
                                realUrl,
                                realImage
                            );
                            imageObj.loaded = true;
                            imageObj.preloading = false;
                            that.$set(that.imageList, index, imageObj);
                        }
                    }).catch(error => {
                        console.log("preloadError", error);
                    });
                })()
            } else {
                return;
            }
        },
        drawCanvasToImageForMCResource(offset, imageObj, realUrl, realImage) {
            console.log("drawCanvasToImageForMCResource", offset);
            let colors = this.$store.state.aiPresetData.colors;
            let mc_resource_map = imageObj.mc_resource_map;
            let ai_report = null;
            if (imageObj && imageObj.mc_resource_map && imageObj.mc_resource_map.ai_report) {
                ai_report = imageObj.mc_resource_map.ai_report;
            }
            if (
                this.$checkPermission({regionPermissionKey:'obstetricalAI'}) &&
                ai_report &&
                ai_report.finshed &&
                ai_report.report &&
                this.mc_options
            ) {
                let ai_report = mc_resource_map.ai_report;
                let viewListObj = this.mc_options && this.mc_options.more_details.listObj;
                var canvas = document.getElementById("ai_canvas");
                var context = canvas.getContext("2d");
                canvas.width = realImage.width;
                canvas.height = realImage.height;
                context.drawImage(realImage, 0, 0);
                context.lineWidth = 2;
                if (ai_report && ai_report.report && ai_report.report.isSuccess) {
                    ai_report = ai_report.report;
                    let strucs = ai_report.structure;

                    // let dispalyStructs = []
                    if (viewListObj[ai_report.view_type]) {
                        let allowedStrucs = viewListObj[ai_report.view_type].detail;
                        let i = 0;
                        let colorsObj = {};
                        viewListObj[ai_report.view_type].item.forEach((item) => {
                            let detail_ids = item.detail_ids;
                            if (detail_ids && detail_ids.length > 0) {
                                let color = colors[i];
                                let old_color = "";
                                detail_ids.forEach((detail_id) => {
                                    if (colorsObj[detail_id]) {
                                        old_color = colorsObj[detail_id];
                                    }
                                });
                                if (!old_color) {
                                    i = i + 1;
                                }
                                detail_ids.forEach((detail_id) => {
                                    if (old_color) {
                                        colorsObj[detail_id] = old_color;
                                    } else {
                                        colorsObj[detail_id] = color;
                                    }
                                });
                            }
                        });
                        let haveSetItem = (ai_report.item || []).reduce((h, v) => {
                            h[v.type] = h[v.type] || [];
                            h[v.type].push(v);
                            return h;
                        }, {});
                        viewListObj[ai_report.view_type].item.forEach((item) => {
                            let detail_ids = item.detail_ids;
                            // if(!isShow){
                            //     detail_ids
                            // }
                            if (true) {
                                if (detail_ids && detail_ids.length > 0) {
                                    detail_ids.forEach((detail_id) => {
                                        strucs.forEach((detect) => {
                                            if (
                                                detail_id == detect.type &&
                                                haveSetItem[item.id] &&
                                                haveSetItem[item.id].length > 0
                                            ) {
                                                let color = colorsObj[detail_id];
                                                if (
                                                    this.displayColors.indexOf(color) > -1 ||
                                                    this.displayColors.length == 0
                                                ) {
                                                    // dispalyStructs.push(detect)
                                                    let position = detect.position;
                                                    position = [
                                                        position[0] - offset[0],
                                                        position[1] - offset[1],
                                                        position[2] - offset[0],
                                                        position[3] - offset[1],
                                                    ];
                                                    let x = position[0];
                                                    let y = position[1];
                                                    let r = 0;
                                                    let w = Math.abs(position[0] - position[2]);
                                                    let h = Math.abs(position[3] - position[1]);
                                                    if (w < 2 * r) {
                                                        r = w / 2;
                                                    }
                                                    if (h < 2 * r) {
                                                        r = h / 2;
                                                    }
                                                    context.beginPath();
                                                    context.moveTo(x + r, y);
                                                    context.arcTo(x + w, y, x + w, y + h, r);
                                                    context.arcTo(x + w, y + h, x, y + h, r);
                                                    context.arcTo(x, y + h, x, y, r);
                                                    context.arcTo(x, y, x + w, y, r);
                                                    context.strokeStyle = color;
                                                    context.save();
                                                    context.closePath();
                                                    context.stroke();
                                                }
                                            }
                                        });
                                    });
                                }
                            }
                        });
                    }

                    // let base64 = canvas.toDataURL("image/jpeg");
                    // return base64;
                }
                let base64 = canvas.toDataURL("image/jpeg");
                return base64;
            } else {
                return realUrl;
            }
        },
        reloadImage(isStructImage, displayColors, isDisplayBColor) {
            this.isStructImage = isStructImage;
            this.displayColors = displayColors;
            this.isDisplayBColor = isDisplayBColor;
            this.preLoad(this.currentSliderIndex, true);
        },
        handleUpdataLoadingStatus(isLoading) {
            this.isLoading = isLoading;
        },
        handleUpdataMCResourceMapRconsider(mc_resource_map) {
            // this.exam.is_reconsider = false
            this.imageList.map((item, i) => {
                if (item.mc_resource_map && mc_resource_map.img_id == item.mc_resource_map.img_id) {
                    this.imageList[i].mc_resource_map = mc_resource_map;
                }
                if (item.mc_resource_map && item.mc_resource_map.img_id == mc_resource_map.img_id) {
                    // this.exam.is_reconsider = item.mc_resource_map.ai_report.isReconsider
                    this.$emit("examUpdataMCResourceMapRconsider", {...this.currentExam,mc_resource_map});
                    item.mc_resource_map = mc_resource_map;
                }
            });
            this.currentFile.mc_resource_map = mc_resource_map;
        },
    },
};
</script>
<style lang="scss">
.obstetric_qc_multicenter_gallery_dialog {
    background: #212121;
    height: 90% !important;
    display: flex;
    flex-direction: column;
    margin-top: 5vh !important;
    box-shadow: 10px 8px 30px #666;
    border: 1px solid #fff;
    .el-dialog__header {
        background: #ecf6f6;
        display: none;
        border-bottom: none !important;
        .el-dialog__close {
            font-size: 22px;
            color: black !important;
        }
        .el-dialog__close:hover {
            color: #909399;
        }
        button {
            top: 5px;
            right: 10px;
        }
    }
    th td {
        word-break: break-all;
    }
    .el-dialog__body {
        position: relative;
        flex: 1;
        display: flex;
        padding: 0 !important;
        height: calc(100% - 30px);
        overflow: hidden;
        background: #212121;
    }
    .mr_gallery {
        color: #000;
        background-color: #a9bfbe;
    }
    .el-tabs__header {
        margin-bottom: 0;
    }
    .el-tabs__nav {
        width: 100%;
        display: flex;
        background-color: #fff;
        color: #000;
        .el-tabs__item {
            flex: 1;
            text-align: center;
            padding: 0;
        }
        .el-tabs__item.is-active,
        .el-tabs__item:hover {
            color: #fff;
            background: #779a98;
        }
        .el-tabs__active-bar {
            background-color: #779a98;
            width: 50%;
        }
    }
    .case_view_wrap {
        height: calc(100% - 44px);
    }
    .annotation_view_wrap {
        height: calc(100% - 44px);
    }
    .reject_dialog {
        .el-dialog__close {
            color: #000 !important;
        }
        .el-textarea {
            width: 100%;
            margin-top: 10px;
        }
        .reject_btn {
            width: 80px;
            float: right;
            margin-top: 15px;
        }
    }

    .qc_standard_icon {
        right: 3px;
        bottom: 0px;
        position: absolute;
        color: rgb(0, 255, 102);
        font-size: 10px;
    }
    .qc_non_standard_icon {
        right: 3px;
        bottom: 0px;
        position: absolute;
        color: rgb(255, 153, 51);
        font-size: 10px;
    }
    .ai_result_deletion_icon {
        right: 3px;
        bottom: 0px;
        position: absolute;
        color: rgb(255, 0, 0);
        font-size: 10px;
    }
    .content {
        width: 100%;
        height: 100%;
        display: flex;
    }
    .left-gallery {
        width: 70%;
        display: flex;
        flex-direction: column;
        padding: 0 10px;
        #gallery-box {
            flex: 1;
            .main_swiper {
                position: relative;
                width: 100%;
                height: 100%;
                z-index: 1;
                overflow: hidden;
                .mui-slider-group {
                    height: 100%;
                    font-size: 0;
                    position: relative;
                    transition: all 0s linear;
                    white-space: nowrap;
                    .mui-slider-item {
                        font-size: 14px;
                        position: relative;
                        display: inline-block;
                        z-index: 1;
                        width: 100%;
                        height: 100%;
                        vertical-align: top;
                        white-space: normal;
                        -webkit-user-select: none;
                        -moz-user-select: none;
                        -ms-user-select: none;
                        user-select: none;
                        .preview {
                            max-width: 100%;
                            max-height: 100%;
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            margin: auto;
                            cursor: pointer;
                        }
                    }
                    .loading_span {
                        position: absolute !important;
                        z-index: 9;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        width: 42px;
                        height: 42px;
                        .el-loading-mask {
                            background: none;
                        }
                    }
                }
            }
        }
        .gallery-top {
            .iconright1 {
                font-size: 40px;
                left: 20px;
                top: 35%;
                position: absolute;
                color: #aaa;
                cursor: pointer;
                z-index: 3;
                display: none;
            }
            .iconright2 {
                font-size: 40px;
                right: calc(30% + 20px);
                top: 35%;
                position: absolute;
                color: #aaa;
                cursor: pointer;
                z-index: 3;
                display: none;
            }
            &:hover .iconfont {
                display: block;
            }
        }
        .mui-slider-item {
            font-size: 14px;
            position: relative;
            display: inline-block;
            width: 100%;
            height: 100%;
            vertical-align: top;
            white-space: normal;
            user-select: none;
        }
        .preview,
        .main_video {
            max-width: 100%;
            max-height: 100%;
            position: absolute;
            top: 0;
            bottom: 0;
            left: 0;
            right: 0;
            margin: auto;
        }
        .review {
            width: 100%;
        }
        .gesture_video {
            position: absolute;
            width: 300px;
            right: 10px;
            top: 0px;
        }
        .loading_span {
            position: absolute !important;
            z-index: 9;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            width: 42px;
            height: 42px;
            .el-loading-mask {
                background: none;
            }
        }
        .thumb_wrap {
            height: 120px;
            padding: 0px 20px 4px;
            margin-top: 15px;
            position: relative;
            .thumb_loading {
                position: absolute;
                top: 0;
                width: calc(100% - 40px);
                height: 100%;
                background-color: #212121;
                z-index: 99;
            }

            .__rail-is-horizontal {
                height: 0 !important;
            }
            .thumb_scroll_wrap {
                width: 100%;
                height: 100%;
                user-select: none;
                .thumb_slide {
                    position: relative;
                    width: 100%;
                    height: 100%;
                    z-index: 1;
                    .thumb_item {
                        width: 156px;
                        height: 116px;
                        background: #000;
                        position: relative;
                        margin-right: 1px;
                        cursor: pointer;
                        &.current_thumb {
                            border: 3px solid #599592;
                        }
                        .image_tag {
                            color: #fff;
                            position: absolute;
                            bottom: 6px;
                            z-index: 9;
                            right: 4px;
                            background: #0030c6;
                            line-height: 1.5;
                            padding: 0 4px;
                            border-radius: 4px;
                            &.tag_2 {
                                background: #f90012;
                            }
                            &.tag_3 {
                                background: #00c626;
                            }
                        }
                        .preview {
                            max-width: 100%;
                            max-height: 100%;
                            position: absolute;
                            top: 0;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            margin: auto;
                        }
                        p {
                            font-size: 12px;
                            text-align: center;
                            color: #fff;
                            position: absolute;
                            white-space: nowrap;
                            top: 46%;
                            left: 50%;
                            z-index: 2;
                            transform: translate(-50%, -50%) scale(0.9);
                        }
                        .iconvideo_fill_light,
                        .iconpicture {
                            position: absolute;
                            bottom: 0;
                            color: #fff;
                            font-size: 24px;
                            line-height: 1;
                            z-index: 2;
                        }
                        .comment_number {
                            position: absolute;
                            left: 2px;
                            top: 2px;
                            color: #fff;
                            background: #56c7fd;
                            border: 1px solid white;
                            width: 20px;
                            border-radius: 50%;
                            height: 20px;
                            font-size: 12px;
                            line-height: 20px;
                            text-align: center;
                            z-index: 2;
                        }
                        .unread_tip {
                            position: absolute;
                            right: 0.2rem;
                            top: 0.2rem;
                            border-radius: 50%;
                            background-color: #f00;
                            width: 8px;
                            height: 8px;
                            z-index: 2;
                        }
                        .empty_thump {
                            width: 100%;
                            height: 100px;
                            position: absolute;
                            top: 50%;
                            left: 50%;
                            background-color: #2c2d2f;
                            transform: translate(-50%, -50%);
                        }
                        .view_name {
                            color: #fff;
                            position: absolute;
                            top: 6px;
                            z-index: 9;
                            left: 2px;
                            transform: none;
                            font-size: 14px;
                            white-space: normal;
                            text-align: left;
                        }
                    }
                    .__bar-is-horizontal,
                    .__bar-is-vertical {
                        display: none;
                    }
                }
            }
            .last_page {
                transform: rotate(90deg) scaleX(1.5);
                position: absolute;
                left: 0px;
                top: 50px;
                color: black;
                font-size: 18px;
                line-height: 16px;
                cursor: pointer;
            }
            .next_page {
                transform: rotate(270deg) scaleX(1.5);
                position: absolute;
                right: 0px;
                top: 50px;
                color: black;
                font-size: 18px;
                line-height: 16px;
                cursor: pointer;
            }
        }
    }
    .closebtn {
        height: 36px;
        opacity: 1;
        text-align: right;
        position: absolute;
        right: 0%;
        z-index: 2001;
        .el-icon {
            top: 0px;
            right: 4px;
            font-size: 28px;
            height: 36px;
        }
    }
    .right-gallery {
        width: 30%;
        height: calc(100%);
        height: 100%;
        display: flex;
        flex-direction: column;
        position: absolute;
        right: 0;
        bottom: 0;
        z-index: 10;
        overflow: auto;
        border-left: 1px solid #d9d9d9;

        .report {
            height: 100%;
        }
    }
}
</style>
