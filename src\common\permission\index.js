/**
 * 权限管理器入口文件
 * 提供统一的权限管理接口
 */

import permissionManager, { PermissionManager } from './PermissionManager.js';
import BasePermissionManager from './BasePermissionManager.js';
import RoutePermissionManager from './RoutePermissionManager.js';
import ComponentPermissionManager from './ComponentPermissionManager.js';
import FeaturePermissionManager from './FeaturePermissionManager.js';
import ConversationPermissionManager from './ConversationPermissionManager.js';

// 导出主要的权限管理器实例
export default permissionManager;

// 导出所有类，供需要自定义的场景使用
export {
    PermissionManager,
    BasePermissionManager,
    RoutePermissionManager,
    ComponentPermissionManager,
    FeaturePermissionManager,
    ConversationPermissionManager
};

// 便捷方法导出
export const {
    checkPermission,
    checkRoutePermission,
    checkComponentPermission,
    checkFeaturePermission,
    checkRegionPermission,
    checkConversationPermission,
    checkApiPermission,
    checkDataPermission,
    checkElementPermission,
    isComponentVisible,
    isComponentDisabled,
    getAccessibleRoutes,
    getAvailableActions,
    getRedirectRoute,
    batchCheckPermissions,
    updateUserInfo,
    clearCache,
    getUserInfo,
    getUserRole,
    getUserId,
    isAdmin,
    isSuperAdmin,
    isInitialized,
    initialize,
    initializeRegionPermissions,
    destroy,
    logoutCleanup,
    isRegionFunctionEnabled,
    getEnabledRegionFunctions,
    getCurrentRegion,
    isRegionFunctionAvailable,
    getRegionMappedPermissions,
    checkAllRegionMappedPermissions,
    getAllEnabledMappedPermissions,
    getRegionPermissionMappingSummary,
    // 会话权限相关方法
    setUserConversationRole,
    getUserConversationRole,
    setConversationMemberRoles,
    isConversationOwner,
    isConversationAdmin,
    getUserConversationPermissions
} = permissionManager;

/**
 * Vue插件安装函数
 * @param {Object} Vue - Vue构造函数
 * @param {Object} options - 插件选项
 */
export function install(Vue, options = {}) {
    // 将权限管理器添加到Vue原型
    Vue.prototype.$permission = permissionManager;

    // 添加权限检查方法到Vue原型，确保所有组件都能访问
    Vue.prototype.$checkPermission = function(permission, context = {}) {
        return permissionManager.checkPermission(permission, context);
    };

    Vue.prototype.$checkRoute = function(routePath, context = {}) {
        return permissionManager.checkRoutePermission(routePath, context);
    };

    Vue.prototype.$checkComponent = function(component, action = null, context = {}) {
        return permissionManager.checkComponentPermission(component, action, context);
    };

    Vue.prototype.$checkFeature = function(feature, action = null, context = {}) {
        return permissionManager.checkFeaturePermission(feature, action, context);
    };

    Vue.prototype.$checkRegionFunction = function(functionName, context = {}) {
        return permissionManager.checkRegionPermission(functionName, context);
    };

    Vue.prototype.$isRegionFunctionEnabled = function(functionName) {
        return permissionManager.isRegionFunctionEnabled(functionName);
    };

    Vue.prototype.$isRegionFunctionAvailable = function(functionName, options = {}) {
        return permissionManager.isRegionFunctionAvailable(functionName, options);
    };

    Vue.prototype.$getCurrentRegion = function() {
        return permissionManager.getCurrentRegion();
    };

    Vue.prototype.$getRegionMappedPermissions = function(regionFunction) {
        return permissionManager.getRegionMappedPermissions(regionFunction);
    };

    Vue.prototype.$checkAllRegionMappedPermissions = function(regionFunction) {
        return permissionManager.checkAllRegionMappedPermissions(regionFunction);
    };

    Vue.prototype.$getAllEnabledMappedPermissions = function() {
        return permissionManager.getAllEnabledMappedPermissions();
    };

    Vue.prototype.$getRegionPermissionMappingSummary = function() {
        return permissionManager.getRegionPermissionMappingSummary();
    };

    Vue.prototype.$isAdmin = function() {
        return permissionManager.isAdmin();
    };

    Vue.prototype.$isSuperAdmin = function() {
        return permissionManager.isSuperAdmin();
    };

    Vue.prototype.$getUserRole = function() {
        return permissionManager.getUserRole();
    };


    // 创建权限混入对象
    const permissionMixin = {
        data() {
            return {
                // 响应式的权限管理器初始化状态
                permissionInitialized: permissionManager.isInitialized(),
                regionPermissionInitialized: permissionManager.regionInitialized,
                // 权限版本号，用于强制更新权限相关的计算属性
                permissionVersion: 0,
                // 区域权限版本号
                regionPermissionVersion: 0,
                // 会话权限版本号
                conversationPermissionVersion: 0,
                // 用户角色信息，用于监听角色变化
                currentUserRole: permissionManager.getUserRole(),
                currentUserId: permissionManager.getUserId()
            };
        },

        created() {
            // 监听权限管理器初始化事件
            const updateInitStatus = () => {
                this.permissionInitialized = permissionManager.isInitialized();
                this.regionPermissionInitialized = permissionManager.regionInitialized;
            };

            // 监听权限变化事件
            const handlePermissionChange = (event) => {
                const changeInfo = event.detail || event;
                // 更新用户信息
                this.currentUserRole = permissionManager.getUserRole();
                this.currentUserId = permissionManager.getUserId();

                // 增加版本号，强制更新所有权限相关的计算属性
                this.permissionVersion++;

                // 触发组件的权限变化钩子（如果存在）
                if (typeof this.onPermissionChanged === 'function') {
                    this.onPermissionChanged(changeInfo);
                }
            };

            // 监听区域权限变化事件
            const handleRegionPermissionChange = (event) => {
                const changeInfo = event.detail || event;
                // 增加区域权限版本号
                this.regionPermissionVersion++;

                // 触发组件的区域权限变化钩子（如果存在）
                if (typeof this.onRegionPermissionChanged === 'function') {
                    this.onRegionPermissionChanged(changeInfo);
                }
            };

            // 监听会话权限变化事件
            const handleConversationPermissionChange = (event) => {
                const changeInfo = event.detail || event;
                // 增加会话权限版本号
                this.conversationPermissionVersion++;

                // 触发组件的会话权限变化钩子（如果存在）
                if (typeof this.onConversationPermissionChanged === 'function') {
                    this.onConversationPermissionChanged(changeInfo);
                }
            };

            // 只监听浏览器事件，避免重复监听
            window.addEventListener('permission:initialized', updateInitStatus);
            window.addEventListener('permission:regionInitialized', updateInitStatus);
            window.addEventListener('permission:changed', handlePermissionChange);
            window.addEventListener('permission:regionChanged', handleRegionPermissionChange);
            window.addEventListener('permission:conversationChanged', handleConversationPermissionChange);

            // 组件销毁时移除监听器
            this.$once('hook:beforeDestroy', () => {
                window.removeEventListener('permission:initialized', updateInitStatus);
                window.removeEventListener('permission:regionInitialized', updateInitStatus);
                window.removeEventListener('permission:changed', handlePermissionChange);
                window.removeEventListener('permission:regionChanged', handleRegionPermissionChange);
                window.removeEventListener('permission:conversationChanged', handleConversationPermissionChange);
            });
        }
    };

    // 检查组件是否需要权限混入
    const shouldApplyPermissionMixin = (componentOptions) => {
        // 检查组件选项中的权限标识
        if (componentOptions.permission === true) {
            return true;
        }

        if (componentOptions.permission && typeof componentOptions.permission === 'object') {
            return componentOptions.permission.reactive === true;
        }

        // 检查组件名称是否在白名单中
        const permissionComponents = options.components || [];
        if (permissionComponents.includes(componentOptions.name)) {
            return true;
        }
        return false;
    };

    // 应用选择性混入
    Vue.mixin({
        beforeCreate() {
            try {
                // 检查当前组件是否需要权限混入
                if (shouldApplyPermissionMixin(this.$options)) {
                    // 合并权限混入的data函数
                    const originalData = this.$options.data;
                    this.$options.data = function() {
                        try {
                            const componentData = originalData ? originalData.call(this) : {};
                            const permissionData = permissionMixin.data ? permissionMixin.data.call(this) : {};
                            return Object.assign({}, componentData, permissionData);
                        } catch (error) {
                            console.error('[Permission] Error in data merge:', error);
                            return originalData ? originalData.call(this) : {};
                        }
                    };

                    // 合并权限混入的created钩子
                    const originalCreated = this.$options.created;
                    this.$options.created = [].concat(originalCreated || [], permissionMixin.created || []);

                    // 标记组件已应用权限混入
                    this._hasPermissionMixin = true;
                }
            } catch (error) {
                console.error('[Permission] Error in beforeCreate hook:', error);
            }
        }
    });

    // 指令内部的权限检查辅助函数（避免在指令钩子中使用 this）
    const runPermissionCheck = (el, binding) => {
        const { value, modifiers } = binding;

        if (!value) {
            console.warn('v-permission directive requires a value');
            return;
        }

        let hasPermission = false;

        if (typeof value === 'string') {
            if (modifiers.route) {
                hasPermission = permissionManager.checkRoutePermission(value);
            } else if (modifiers.component) {
                hasPermission = permissionManager.checkComponentPermission(value);
            } else if (modifiers.feature) {
                hasPermission = permissionManager.checkPermission({
                    featurePermissionKey: value
                });
            } else if (modifiers.region) {
                hasPermission = permissionManager.checkPermission({
                    regionPermissionKey: value
                });
            } else if (modifiers.conversation) {
                // 会话权限需要从当前组件上下文获取 conversationId
                const context = el._vueComponent ? {
                    conversationId: el._vueComponent.cid || el._vueComponent.$route.params.cid,
                    userId: el._vueComponent.user?.uid
                } : {};
                hasPermission = permissionManager.checkPermission({
                    conversationPermissionKey: value
                }, context);
            } else {
                hasPermission = permissionManager.checkPermission(value);
            }
        } else if (typeof value === 'object') {
            const { type, permission, action, context, regionPermissionKey, featurePermissionKey, conversationPermissionKey } = value;

            if (regionPermissionKey || featurePermissionKey) {
                hasPermission = permissionManager.checkPermission({
                    regionPermissionKey,
                    featurePermissionKey
                }, context || {});
            } else if (conversationPermissionKey) {
                // 处理会话权限
                const conversationContext = context || {};
                if (!conversationContext.conversationId && el._vueComponent) {
                    conversationContext.conversationId = el._vueComponent.cid || el._vueComponent.$route.params.cid;
                }
                if (!conversationContext.userId && el._vueComponent) {
                    conversationContext.userId = el._vueComponent.user?.uid;
                }
                hasPermission = permissionManager.checkPermission({
                    conversationPermissionKey: conversationPermissionKey
                }, conversationContext);
            } else {
                switch (type) {
                case 'route':
                    hasPermission = permissionManager.checkRoutePermission(permission, context);
                    break;
                case 'component':
                    hasPermission = permissionManager.checkComponentPermission(permission, action, context);
                    break;
                case 'feature':
                    hasPermission = permissionManager.checkPermission({
                        featurePermissionKey: permission
                    }, context);
                    break;
                case 'region':
                    hasPermission = permissionManager.checkPermission({
                        regionPermissionKey: permission
                    }, context);
                    break;
                case 'conversation':
                    hasPermission = permissionManager.checkPermission({
                        conversationPermissionKey: permission
                    }, context);
                    break;
                default:
                    hasPermission = permissionManager.checkPermission({
                        featurePermissionKey: permission
                    }, context);
                }
            }
        }

        if (hasPermission) {
            if (modifiers.hide) {
                el.style.display = el._originalDisplay || '';
            } else if (modifiers.disable) {
                el.disabled = el._originalDisabled || false;
                el.className = el._originalClass || '';
            } else {
                if (!el.parentNode && el._parentNode) {
                    el._parentNode.appendChild(el);
                }
            }
        } else {
            if (modifiers.hide) {
                el.style.display = 'none';
            } else if (modifiers.disable) {
                el.disabled = true;
                el.classList.add('disabled');
            } else {
                if (el.parentNode) {
                    el._parentNode = el.parentNode;
                    el.parentNode.removeChild(el);
                }
            }
        }
    };

    // 添加全局指令 v-permission
    Vue.directive('permission', {
        bind(el, binding, vnode) {
            el._originalDisplay = el.style.display;
            el._originalDisabled = el.disabled;
            el._originalClass = el.className;
            el._permissionBinding = binding;
            el._vueComponent = vnode.context; // 保存组件实例引用

            runPermissionCheck(el, binding);

            const handlePermissionChange = () => {
                runPermissionCheck(el, binding);
            };

            el._permissionChangeHandler = handlePermissionChange;
            window.addEventListener('permission:changed', handlePermissionChange);
        },

        update(el, binding, vnode) {
            el._permissionBinding = binding;
            el._vueComponent = vnode.context; // 更新组件实例引用
            runPermissionCheck(el, binding);
        },

        unbind(el) {
            if (el._permissionChangeHandler) {
                window.removeEventListener('permission:changed', el._permissionChangeHandler);
                delete el._permissionChangeHandler;
            }
            delete el._permissionBinding;
        }
    });

    // 如果提供了初始化选项，自动初始化
    if (options.autoInit && options.userInfo) {
        permissionManager.initialize(options.userInfo, options.config || {});
    }

    // 将权限混入对象添加到Vue实例，供组件手动使用
    Vue.prototype.$permissionMixin = permissionMixin;
}

// 自动安装（如果在浏览器环境中且Vue可用）
if (typeof window !== 'undefined' && window.Vue) {
    install(window.Vue);
}

// 在install函数外部声明permissionMixin变量，以便导出
let permissionMixin;

// 重新运行install函数以获取permissionMixin
if (typeof window !== 'undefined' && window.Vue) {
    // 从Vue原型中获取permissionMixin
    permissionMixin = window.Vue.prototype.$permissionMixin;
}

// 导出权限混入对象
export { permissionMixin };
