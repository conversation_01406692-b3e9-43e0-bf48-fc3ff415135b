import CMainScreenController from "@/common/socket/CMainScreenController"
import CMainScreenGateway from "@/common/socket/CMainScreenGateway"
import CWorkstationCommunicationMng from "@/common/CommunicationMng/index"
import Tool from '@/common/tool.js';
import CConversation from "./CConversation"
import CAiAnalyze from "./CAiAnalyze"
import CFileTransferAssistant from "./CFileTransferAssistant"
import CCentralStation from "./CCentralStation"
import CFeedbackQuestionAssistant from "./CFeedbackQuestionAssistant"
import CMonitorWallPush from "@/common/CLiveConferenceNative/CMonitorWallPush"
import CScanRoom from "@/common/CScanRoom"
import ServiceConfig from '@/common/ServiceConfig.js';
import {cloneDeep} from 'lodash'
import { Logger } from "@/common/console";
import i18n from '@/common/i18n'
import permissionManager from '@/common/permission/PermissionManager.js';
import { GROUP_ROLE } from '@/common/permission/constant.js';
function CMainScreen(option){
    console.log("[event] CMainScreen.construct",Tool.triggerTime());
    this.uid=option.uid;
    this.url=option.url;
    this.client_uuid=option.client_uuid;
    this.client_type=option.client_type;
    this.conversation_list = {};
    this.deviceInfo = null; //设备信息

    // 使用全局权限管理器
    this.permissionManager = permissionManager;

    this.controller=new CMainScreenController({
        uid:this.uid
    });
    this.gateway=new CMainScreenGateway({
        uid:this.uid,
        url:this.url
    });
    this.initController();
    this.initGateway(option.client_uuid);
    this.is_bind_scan_room_user = false;
    // this.CMonitorWall=CMonitorWall;
    // this.CScanRoom=CScanRoom;
    // var that = this;
    // CMonitorWall.init({
    //     uid: that.uid,
    //     client_type: that.client_type,
    //     client_uuid: that.client_uuid,
    //     url: that.url,

    //     controller:that.controller,
    //     gateway:that.gateway,
    // });
    // CScanRoom.initialize({
    //     callbackToMainScreen: function (msg, data, callback) {
    //         that.callbackFromScanRoom(msg, data, callback);
    //     },
    //     callbackToMonitorWall:function (msg, data, callback) {
    //         CMonitorWall.callbackFromScanRoom(msg, data, callback);
    //     },
    //     getMonitorWallState: function () {
    //         return CMonitorWall.state;
    //     }
    // });
    // setTimeout(()=>{
    //     this.gateway.closeSocket
    // },1000)
}
CMainScreen.prototype.initMonitorWallPush=function(){
    var that=this;
    that.CMonitorWallPush = new CMonitorWallPush({
        uid: that.uid,
        client_type: that.client_type,
        client_uuid: that.client_uuid,
        controller:that.controller,
        gateway:that.gateway,
    })
    const oldInstance = window.CMonitorWallPush;
    const newInstance = that.CMonitorWallPush;
    if(oldInstance){
        Tool.rebindEvents(oldInstance, newInstance);
    }
    window.CMonitorWallPush = newInstance;
};
CMainScreen.prototype.callbackFromScanRoom = function (msg, data, callback) {
    console.log("[event] CMainScreen.callbackFromScanRoom");
    console.log(msg);
    console.log(data);
    var controller = this.conversation_list[data.conversation_id];
    if (controller) {
        if("notify_start_catch" == msg){
            controller.cRealtimeVideo.notifyStartCatch(data);//todo
        }else if("notify_stop_catch" == msg){
            controller.cRealtimeVideo.notifyStopUltrasoundDesktop(data);
        }
    }
};
CMainScreen.prototype.initController=function(){
    var that=this;
    this.controller.clearAllListener();
    that.addControllerEvent();
};
CMainScreen.prototype.addControllerEvent=function(){
    this.addControllerBaseEvent();
};
const controllerEventList = [
    'search',//主页搜索
    'query_clouds_statistics',//云端统计
    'query_clouds_statistics_chart_data',//云端统计图表
    'query_clouds_statistics_detail_data',//云端统计细节数据
    'query_hospital_user_info_for_clouds_statistics',
    'query_groupset_info_for_clouds_statistics',
    'get_qc_statistics_token',//启动质控监测
    'query_scan_room_info_list',//工作站管理
    'query_ultrasync_box_info_list',//迅影盒子管理
    'edit_scan_room',//修改工作站
    'delete_scan_room',//删除工作站
    'edit_ultrasync_box',//修改迅影盒子
    'delete_ultrasync_box',//删除迅影盒子
    'query_ultrasound_device_info_list',
    'edit_ultrasound_device',
    'get_user_info',//查询个人信息
    'get_version_info',
    'request_create_conversation',//请求创建会话
    'query_user_info_list',//查询用户
    'request_add_friend',//请求添加好友
    'response_add_friend',//处理添加好友请求
    'search_group',//搜索群
    'join_into_group_or_not',//审核加入群
    'add_user_favorites',//添加云收藏
    'query_user_favorites',//查询云收藏
    'delete_user_favorites',//删除云收藏
    'set_user_portrait_img',//修改头像
    'query_service_provider_list',//查询服务器提供者列表
    'set_user_other_info',//设置用户配置
    'get_common_report_template',
    'notify_device_event',
    'group_contains_attendee',
    'request_generate_referral_code',//请求生成推荐码
    'query_faq',//查询FAQ
    'search_exams',//搜索检查
    'new_exam',//新建检查
    'add_media_transfer_tasks',
    'delete_media_transfer_tasks',
    'query_media_transfer_tasks',
    'import_media_transfer_tasks',//入库裁剪图像
    'sendto_media_transfer_tasks',//转发裁剪图像
    'query_groupset_list',
    'add_groupset',
    'update_groupset',
    'set_groupset_portrait',
    'delete_groupset_list',
    'query_groupset_basic_info',
    'query_groupset_user_info',
    'query_groupset_user_exam_info',
    'open_conversation_wall',
    'close_conversation_wall',
    'get_groupset_exam_list',
    'get_groupset_exam_image_list',
    'request_get_thumbnail',
    'query_iworks_protocol_basic_info_list',
    'query_iworks_protocol_detail_info',
    'get_statistics_command',
    'query_user_basic_info',
    'query_group_basic_info',
    'request_ai_search',//AI搜图
    'get_qc_statistics_bi_url',//BI数据展示
    'AdminRegisterApplyQuery',//超管权限
    'AdminRegisterApplyVerify',
    'AdminUserManageQuery',
    'AdminUserManageResetUserPassword',
    'AdminUserManageSetUserStatus',
    'AdminUserManageSetUserRole',
    'AdminUserManageSetUserAccount',
    'AdminUserManageSetUserHospital',
    'AdminHospitalManageQuery',
    'AdminHospitalManageAddHospital',
    'AdminHospitalManageDeleteHospital',
    'AdminHospitalManageUpdateHospital',
    'AdminHospitalManageQueryHospitalAssociation',
    'AdminHospitalManageAddHospitalAssociation',
    'AdminHospitalManageDeleteHospitalAssociation',
];
CMainScreen.prototype.addControllerBaseEvent=function(){
    var that=this;
    for(let controllerEvent of controllerEventList){
        that.controller.on(controllerEvent,function(data,callback){
            if(typeof(data) == 'function'){
                that.gateway.emit(controllerEvent,data);
            }else{
                that.gateway.emit(controllerEvent,data,callback);
            }
        })
    }
    //MainScreen
    that.controller.on("bind_scan_room_user",function(data,callback){
        that.onBindScanRoomUser();
    });
    that.controller.on("unbind_scan_room_user",function(data,callback){
        that.onUnBindScanRoomUser();
    });
    that.controller.on("get_consultation_image_list",function(data,callback){
        that.onGetConsultationImageList(data,callback);
    });
    that.controller.on("get_all_hospital_name",function(callback){
        that.onGetAllHospitalName(callback);
    });
    that.controller.on("register_scan_room", function (data,callback){
        that.onRegisterScanRoom(data,callback);
    });
    //Conversation
    that.controller.on("request_start_conversation",function(data,start_type,callback){
        that.onRequestStartConversation(data,start_type,callback);
    });
    that.controller.on("request_start_single_chat_conversation",function(data,callback){
        that.onRequestStartSingleChatConversation(data,callback);
    });
    that.controller.on("notify_update_device_info", function (data) {
        that.NotifyUpdateDeviceInfo(data);
    });
    that.controller.on("notify_set_patient_info", function (data) {
        that.NotifySetPatientInfo(data);
    });
    that.controller.on("notify_patient_file_list", function (data) {
        that.NotifyPatientFileList(data);
    });
    that.controller.on("notify_get_device_info", function (data) {
        that.NotifyGetDeviceInfo(data);
    });
    that.controller.on("notify_save_single_frame", function (data) {
        that.NotifySaveSingleFrame(data);
    });
    that.controller.on("notify_save_multi_frame", function (data) {
        that.NotifySaveMultiFrame(data);
    });
    that.controller.on("notify_query_patient_information", function (data) {
        that.NotifyQueryPatientInformation(data);
    });
};
CMainScreen.prototype.onBindScanRoomUser=function(){
    //绑定扫查室
    var that=this;
    if (!Tool.ifAppWorkstationClientType(that.client_type)) {
        return;
    }
    if (that.is_bind_scan_room_user) {
        return;
    }
    if (that.gateway.check && window.app_info && window.app_info.local_mac) {
        that.is_bind_scan_room_user = true;
        that.gateway.emit("bind_scan_room_user", {mac_addr: window.app_info.local_mac}, function (is_succ, data) {
            if (!is_succ) {
                //提示注册工作站
                that.is_bind_scan_room_user = false;
                if ("no_scan_room" == data.err_code) {
                    //that.registerScanRoomUI();
                    that.onGetAllHospitalName(function(is_succ,data){
                        var hospital_list = [];
                        if(is_succ){
                            hospital_list = data;
                        }
                        that.controller.emit("open_register_scan_room_view",{
                            mac_addr: window.app_info.local_mac,
                            hospital_list: hospital_list
                        });
                    });
                }
            } else {
                if(Tool.ifDeviceClientType(that.client_type)){
                    that.deviceInfo = {}
                    //先填充this.deviceInfo字段
                    that.deviceInfo.name = data.scan_room.name;
                    that.deviceInfo.uuid = that.client_uuid;
                    that.deviceInfo.client_type = that.client_type;
                    //再从app中获取信息，然后把设备信息发送出去
                    that.GetDeviceInfoAndSend();
                }
                CScanRoom.setCurrentScanRoom({
                    uid: that.uid,
                    client_type: that.client_type,
                    client_uuid: that.client_uuid,
                    url: that.url,
                    scan_room:data.scan_room
                });
            }
        });
    }
};
CMainScreen.prototype.onUnBindScanRoomUser=function(){
    this.is_bind_scan_room_user = false;
};
CMainScreen.prototype.onGetConsultationImageList=function(oData,callback){
    //获取图像列表
    var that=this;
    that.gateway.emit("get_consultation_image_list",oData,function(is_succ,data){
        callback && callback(is_succ,data);
    });
};
CMainScreen.prototype.onRequestStartConversation=function(data,start_type,callback){
    //启动会话
    var that=this;
    if (that.gateway.check) {
        that.gateway.emit('request_start_conversation',data,start_type,callback);
    }else{
    };
};
CMainScreen.prototype.onRequestStartSingleChatConversation=function(data,callback){
    //请求启动会话
    // var that=this;
    this.gateway.emit("request_start_single_chat_conversation","single chat",data.list.join(),data.mode,data.type,data.start_type,callback);
};
CMainScreen.prototype.commitPasswordModify = function(data, callback){
    //修改密码
    var that=this;
    that.gateway.emit("commit_password_modify", data, callback);
};
CMainScreen.prototype.commitUserBasicInfoModify = function(data, callback){
    //修改密码
    var that=this;
    that.gateway.emit("set_user_info", data, callback);
};
CMainScreen.prototype.onGetAllHospitalName = function (callback) {
    //查询医院信息
    var that = this;
    that.gateway.emit("get_all_hospital_name", callback);
};
CMainScreen.prototype.onRegisterScanRoom = function (data,callback) {
    //注册工作站
    var that = this;
    that.gateway.emit("register_scan_room", data, function (is_succ,data) {
        if (is_succ) {
            that.onBindScanRoomUser();
        }
        callback(is_succ,data);
    });
};
CMainScreen.prototype.initGateway=function(client_uuid){
    var that=this;
    that.client_uuid = client_uuid;
    that.onUnBindScanRoomUser();
    that.gateway.connectSocket();
    this.initController();
    that.addGatewayEvent();
    this.initMonitorWallPush()
};
CMainScreen.prototype.addGatewayEvent=function(){
    this.addGatewayBaseEvent();
};
const gatewayEventList = {
    'recent_active_conversation_list':'recent_active_conversation_list',//设置最近会话列表
    'recent_active_conversation_list_last_message':'recent_active_conversation_list_last_message',//设置最近会话列表最后消息
    'friend_info_list':'friend_list',//设置好友列表
    'userAddLoginClient':'userAddLoginClient',//多端登录，置顶文件传输助手
    'conversation_list':'conversation_list',//设置会话列表
    'request_notifications':'group_applys',//设置入群申请
    'system_notify':'friend_applys',//设置好友申请
    'notify_add_friend':'notify_add_friend',//通知添加好友
    'update_friend_state':'update_friend_info',//更新好友信息
    'notify_friend_destroy':'notify_friend_destroy',//推送好友注销消息
    'user_info':'user_info',//更新个人信息
    'version_info':'version_info',//更新服务器信息
    'notify_delete_group':'notify_delete_group',//通知解散群
    'agora_live_start':'notify_agora_live_start',//通知启动直播
    'agora_live_stop':'notify_agora_live_stop',//通知关闭直播
    'notify_update_recording':'notify_update_recording',//通知回放编辑有更新
    'query_user_info_list_result':'query_user_info_list_result',//查询用户列表结果
    'request_add_friend_ack':'request_add_friend_ack',//请求添加好友确认
    'request_add_friend_fail_repeat_add':'request_add_friend_fail_repeat_add',//请求添加好友失败，重复添加
    'request_add_friend_fail_database_err':'request_add_friend_fail_database_err',//请求添加好友失败，数据库错误
    'notify_add_groupset':'notify_add_groupset',
    'notify_update_groupset':'notify_update_groupset',
    'notify_update_groupset_portrait':'notify_update_groupset_portrait',
    'notify_delete_groupset_list':'notify_delete_groupset_list',
    'notify_update_media_transfer_task':'notify_update_media_transfer_task',
    'notify_delete_media_transfer_task':'notify_delete_media_transfer_task',
    'notify_exception':'notify_exception',
    'receive_group_message':'receive_group_message',//会话未开启时，接受群消息
    'dopplerRemoteControlEvent':'dopplerRemoteControlEvent',
    'monitorWall.work.ready':'monitorWall.work.ready',
    'userResponseFriendApply':'userResponseFriendApply',
    'userAddFriend':'userAddFriend',
    'userApplyAddFriend':'userApplyAddFriend',
    'notify_update_announcement':'notify_update_announcement',//通知全局公告有更新
    'notify.group.resource.delete.exam':'notify.group.resource.delete.exam',//通知检查删除
    "notify.group.resource.delete.resource":"notify.group.resource.delete.resource",//通知资源被删除
    "notify_refresh_manager_groupset_list":'notify_refresh_manager_groupset_list',//通知刷新群落授权管理列表
    "notify_refresh_my_groupset_list":'notify_refresh_my_groupset_list',//通知我的群落列表
    "update_ai_analyze_report":'update_ai_analyze_report',  //主控-通知更新AI分析报告
    "student_answer_sheet_update":'student_answer_sheet_update',  //云作业待完成列表更新通知
    "teacher_answer_sheet_update":'teacher_answer_sheet_update',  //云作业待批改列表更新通知
    'notify_msg_from_owner':'notify_msg_from_owner',//接受自己给自己发的通知
    'equipment_server_device_alram_update':'equipment_server_device_alram_update',//通知新增设备故障
    'notify.user.device.sync.live':'notify.user.device.sync.live',//PC端发送远程开播指令给ULinker通知开播
}
CMainScreen.prototype.addGatewayBaseEvent=function(){
    var that=this;
    for(let gatewayKey in gatewayEventList){
        const controllerKey=gatewayEventList[gatewayKey]
        that.gateway.on(gatewayKey,function(is_succ,data){
            //部分接口并不返回is_succ只是返回一个data，部分接口不是返回is_succ而是返回err
            that.controller.emit(controllerKey, is_succ,data);
        });
    }
    //Gateway
    that.gateway.on('connect',function(){
        Logger.save({
            message:`mainScreen_connect`,
            eventType:'socket'
        })
        if(that.gateway.connected){
            return
        }
        var conversation_id_list = that.releaseConversationGatewayList();
        that.gateway.emit("check_main_screen", {
            client_uuid:that.client_uuid,
            user_id: that.uid,
            client_type: that.client_type
        }, function (is_succ,data){
            console.log('[socket event] callback of CMainScreenGateway check_main_screen_tag',Tool.triggerTime());
            console.log(is_succ,data);
            Logger.save({
                message:`mainScreen_check`,
                eventType:'socket',
                data:JSON.stringify([is_succ,data])
            })
            that.onGatewayConnect();
            that.releaseConversationGatewayList();
            if(is_succ) {

                //Init
                that.onBindScanRoomUser();
                //获取最近聊天
                that.gateway.emit("get_recent_active_conversation_list");
                //获取好友列表
                that.gateway.emit("get_friend_info_list");
                //获取群列表
                that.gateway.emit("get_conversation_list");
                //获取离线消息
                that.gateway.emit("get_offline_chat_message");
                //获取离线系统通知
                that.gateway.emit("get_offline_system_notify");
                //获取离线入群申请
                that.gateway.emit('get_request_notifications');
                //获取版本信息
                that.gateway.emit("get_version_info");
                //获取标签top信息
                // that.gateway.emit("get_tag_top_info", that.uid);
                //var current_cid = that.controller.currentConversationId();
                //if (current_cid) {
                //    that.onRequestStartConversation(current_cid);
                //}
                //获取服务器信息
                that.gateway.emit("get_server_info", window.clientType);
                // for (var i in conversation_id_list) {
                //     that.onRequestStartConversation(conversation_id_list[i]);
                // }
                let currentCid = window.vm.$route.params.cid
                if((currentCid!=='0') && currentCid){
                    that.onRequestStartConversation(currentCid);
                }
            } else {
                that.onGatewayReconnectFail();
            }
        });

    });
    that.gateway.on('error',function(e){
        console.log('error')
        that.onGatewayError(e)
    });
    that.gateway.on('disconnect',function(e){
        console.log('disconnect')
        that.onGatewayDisconnect(e);
    });
    that.gateway.on('reconnecting',function(){
        console.log('reconnecting')
        that.onGatewayReconnecting();
    });
    that.gateway.on('reconnect_failed',function(e){
        that.onGatewayReconnectFail(e);
    });
    that.gateway.on('reconnect',function(){
        console.log('reconnect')
        that.onGatewayReconnect();
    });
    that.gateway.on("notify_login_another",function(){
        //异地登录
        that.onNotifyLoginAnother();
    });
    that.gateway.on("notify_user_destroy",function(){
        //用户注销
        that.onNotifyUserDestroy();
    });
    that.gateway.on("server_info",function(data){
        //服务器信息
        that.onServerInfo(data);
    });
    that.gateway.on("version_info",function(data){
        //服务器版本信息
        that.serverVersionInfo = data;
    });
    //Conversation
    that.gateway.on("notify_start_conversation",function(is_succ,conversation,start_type,kickout_data){
        //通知启动会话
        that.onNotifyStartConversation(is_succ,conversation,start_type,kickout_data);
    });
    that.gateway.on('ctrl_device_event',function(data){
        that.onCtrlDeviceEvent(data);
    });
    that.gateway.on('get_device_info',function(){
        that.onGetDeviceInfo();
    });
    that.gateway.on('notify_switch_account',function(){
        that.onSwitchAccount();
    });
};
CMainScreen.prototype.onGatewayConnect=function(){
    //Gateway连接
    var that=this;
    that.gateway.connected=true;
    that.gateway.check = true
    that.controller.emit("gateway_connect");
    window.vm.$root.eventBus.$emit(`main_screen_gateway_connect`)
};
CMainScreen.prototype.onGatewayError=function(e){
    //Gateway错误
    var that=this;
    that.resetApp();
    that.CloseSocket()
    that.controller.emit("gateway_error",e);
    window.vm.$root.eventBus.$emit(`main_screen_gateway_error`)
    that.controller.closeResources();
    that.gateway.check = false
};
CMainScreen.prototype.CloseSocket = function(){
    this.gateway.connected=false;
    this.gateway.closeSocket();
    for(var i in this.conversation_list) {
        this.conversation_list[i].gateway.closeSocket();
    }

}
CMainScreen.prototype.onGatewayDisconnect=function(e){
    //Gateway断开
    var that=this;
    that.gateway.connected=false;
    that.gateway.check = false
    that.controller.emit("gateway_disconnect",e);
    window.vm.$root.eventBus.$emit(`main_screen_gateway_disconnect`)
};
CMainScreen.prototype.onGatewayReconnecting=function(){
    //Gateway重连中
    var that=this;
    that.controller.emit("gateway_reconnecting");
};
CMainScreen.prototype.onGatewayReconnectFail=function(e){
    //Gateway重连失败
    var that=this;
    that.resetApp();
    that.CloseSocket()
    that.controller.emit("gateway_reconnect_fail",e);
    window.vm.$root.eventBus.$emit(`main_screen_reconnect_fail`)
    that.controller.closeResources();
    that.gateway.check = false
};
/*
 * @description 断开后，重置App实时等相关信息
 */
CMainScreen.prototype.resetApp = function () {
    var that=this;
};
CMainScreen.prototype.onGatewayReconnect=function(){
    //Gateway重连成功
    var that=this;
    that.controller.emit("gateway_reconnect");
    window.vm.$root.eventBus.$emit(`main_screen_reconnect`)
};
CMainScreen.prototype.onNotifyLoginAnother=function(){
    //异地登录
    var that=this;
    that.CloseSocket()
    that.controller.emit("notify_login_another");
    that.controller.closeResources();
};
CMainScreen.prototype.onNotifyUserDestroy=function(){
    //注销
    var that=this;
    that.CloseSocket()
    that.controller.emit("notify_user_destroy");
    that.controller.closeResources();
};
CMainScreen.prototype.onServerInfo=function(data){
    //服务器信息
    var that=this;
    that.serverInfo=data;
    //通知APP初始化服务器的配置
    //CWorkstationCommunicationMng.initServerConfig(data);
    that.controller.emit("server_info",data);
    //修改 rtc.SERVER 的值
    //无论aliyun还是webrtc都运行下面代码，防止aliyun降级到webrtc
    var serverInfo = that.serverInfo;
    var find_ice_server = false;
    var new_turn_server = "turn:" + serverInfo.webrtc_coturn_addr + ":" + serverInfo.webrtc_coturn_port;
    for(var k in window.rtc.SERVER.iceServers){
        if(window.rtc.SERVER.iceServers[k].url == new_turn_server){
            find_ice_server = true;
            break;
        }
    }
    if(!find_ice_server){ //不存在则添加
        window.rtc.SERVER.iceServers.splice(0, 0, {
            'url': "turn:" + serverInfo.webrtc_coturn_addr + ":" + serverInfo.webrtc_coturn_port,
            'credential': serverInfo.webrtc_coturn_pwd,
            'username': serverInfo.webrtc_coturn_user,
        });
    }
    //修改rtc.audioParam.maxplaybackrate
    if(serverInfo.webrtc_maxplaybackrate){
        window.rtc.audioParam.maxplaybackrate = serverInfo.webrtc_maxplaybackrate;
    }
};
//Conversation
CMainScreen.prototype.onNotifyStartConversation=function(is_succ,conversation,start_type,kickout_data){
    //通知启动会话
    var that=this;
    console.log('onNotifyStartConversation',is_succ,conversation,start_type)
    if (is_succ&&conversation) {
        if (!that.conversation_list[conversation.id]) {
            var option = {
                uid:that.uid,
                client_type:that.client_type,
                client_uuid:that.client_uuid,
                url:that.url,
                conversation:conversation,
                start_type:start_type
            };
            var new_conversation = that.newConversation(option);
            that.conversation_list[conversation.id] = new_conversation;

            conversation.controller = that.conversation_list[conversation.id].controller;
            conversation.cLiveConference = that.conversation_list[conversation.id].cLiveConference;

            // 初始化设置当前用户的会话角色
            that.setUserConversationRole(conversation);

            that.controller.emit("notify_start_conversation",is_succ,conversation,start_type,kickout_data);
        }
    } else {
        that.controller.emit("notify_start_conversation",is_succ,conversation,start_type,kickout_data);
    }
};

CMainScreen.prototype.newConversation=function(option){
    var conversation = null;
    if (ServiceConfig.type.AiAnalyze == option.conversation.service_type||ServiceConfig.type.DrAiAnalyze == option.conversation.service_type) {
        conversation = new CAiAnalyze(option);
    } else if (ServiceConfig.type.FileTransferAssistant == option.conversation.service_type) {
        conversation = new CFileTransferAssistant(option);
    } else if (ServiceConfig.type.CentralStation == option.conversation.service_type) {
        conversation = new CCentralStation(option);
    } else if (ServiceConfig.type.FeedbackQuestionAssistant == option.conversation.service_type) {
        conversation = new CFeedbackQuestionAssistant(option);
    } else {
        conversation = new CConversation(option);
    }
    return conversation;
};

CMainScreen.prototype.setUserConversationRole=function(conversation){
    // 设置当前用户在会话中的角色
    var that = this;
    try {
        // 确保权限管理器已初始化
        if (that.permissionManager && that.permissionManager.isInitialized()) {
            var conversationId = conversation.id;
            var currentUserId = that.uid;
            var role = GROUP_ROLE.NORMAL; // 默认为普通成员

            // 检查是否为群主
            if (conversation.creator_id === currentUserId && conversation.is_single_chat === 0) {
                role = GROUP_ROLE.CREATOR;
            } else if (conversation.attendeeList) {
                // 从参与者列表中查找当前用户的角色
                for (var attendeeKey in conversation.attendeeList) {
                    var attendee = conversation.attendeeList[attendeeKey];
                    if (attendee && (attendee.uid === currentUserId || attendee.userid === currentUserId)) {
                        // 根据 groupRole 确定角色
                        if (attendee.role === GROUP_ROLE.CREATOR) { // groupRole.creator
                            role = GROUP_ROLE.CREATOR;
                        } else if (attendee.role === GROUP_ROLE.MANAGER) { // groupRole.manager
                            role = GROUP_ROLE.MANAGER;
                        } else if (attendee.role === GROUP_ROLE.NORMAL) { // groupRole.normal
                            role = GROUP_ROLE.NORMAL;
                        }
                        break;
                    }
                }
            }

            // 设置用户会话角色
            that.permissionManager.setUserConversationRole(conversationId, currentUserId, role);
            console.log('设置用户会话角色:', conversationId, currentUserId, role);
        } else {
            console.warn('权限管理器未初始化，无法设置用户会话角色');
        }
    } catch (error) {
        console.error('设置用户会话角色失败:', error);
    }
};

CMainScreen.prototype.releaseConversationGatewayList=function(){
    var conversation_id_list = [];
    for(var i in this.conversation_list) {
        this.conversation_list[i].release();
        delete this.conversation_list[i];
        conversation_id_list.push(String(i));
    }
    this.conversation_list = {};
    return conversation_id_list;
};
CMainScreen.prototype.onCtrlDeviceEvent = function(data){
    console.log("[event] ctrl_device_event");
    console.log(data);
    var that = this;
    var systemConfig = window.vm.$store.state.systemConfig;
    var DeviceInfoUpdateType = systemConfig.DeviceInfoUpdateType;
    var ctrl_device_type = systemConfig.ctrl_device_type
    var eventName = data.event_name;
    if ("ctrl_device_cur_session" == eventName) {
        /*CWorkstationCommunicationMng.UpdateDeviceInfo({
            type:DeviceInfoUpdateType.cur_session,
            cur_session_id: data.cur_session_id,
            cur_session_type: data.cur_session_type,
        });*/
        var auto_push_stream_params = {
            enable: 1,
            value: data.cur_session_id,
            record_mode: data.record_mode || 0,
            subject: data.subject
        }
        if(1 == data.cur_session_type){//uid
            auto_push_stream_params.value_type = "FriendId";
        }else{//群id
            auto_push_stream_params.value_type = "Conversation";
        }
        window.vm.$store.commit('globalParams/updateGlobalAutoPushStream', auto_push_stream_params);
        window.localStorage.setItem('auto_push_stream_' + this.uid, JSON.stringify(auto_push_stream_params));
        var json_data = {
            error:0,
            event_name:"notify_update_device_cur_session",
            type:DeviceInfoUpdateType.cur_session,
            cur_session_id: data.cur_session_id,
            cur_session_type: data.cur_session_type,
        };
        that.gateway.emit("notify_device_event", json_data);
    } else if ("ctrl_device_patient_info" == eventName) {
        CWorkstationCommunicationMng.SetPatientInfo({
            patient_id: data.patient_id,
            patient_name: data.patient_name,
        });
    }else if ("ctrl_device_ftp_info" == eventName) {
        CWorkstationCommunicationMng.UpdateDeviceInfo({
            type:DeviceInfoUpdateType.ftp_info,
            ftp_account: data.ftp_account,
            ftp_password: data.ftp_password,
            ftp_path: data.ftp_path,
            ftp_port: data.ftp_port,
            isAnonymous: data.isAnonymous,
        });
    } else if ("ctrl_device_save_single_frame" == eventName) {
        data.trigger = ctrl_device_type.Mobile;
        that.ctrlDeviceSaveSingleFrame(data);
    }else if("ctrl_device_save_multi_frame" == eventName){
        data.trigger = ctrl_device_type.Mobile;
        that.ctrlDeviceSaveMultiFrame(data);
    }else if("get_patient_exam_list" == eventName){
        CWorkstationCommunicationMng.getPatientExamList(data)
    }else if("ctrl_device_shutdown" == eventName){
        if(systemConfig.client_type.AppUltraSyncBox == that.client_type){
            CWorkstationCommunicationMng.ShutdownBox();
            that.gateway.emit("notify_device_event", {event_name:"notify_device_shutdown", error: 0});
        }else{
            that.gateway.emit("notify_device_event", {event_name:"notify_device_shutdown", error: 1, error_info:"device type error"});
        }
    }else{
        console.log("[error] unknown event name: " + eventName);
    }
};

CMainScreen.prototype.onGetDeviceInfo = function(){
    var that = this;
    console.log("[event] get_device_info");
    if(Tool.ifDeviceClientType(that.client_type) &&  that.deviceInfo){
        that.GetDeviceInfoAndSend();
    }
};

CMainScreen.prototype.isScanRoomReady = function() {
    var current_scan_room_controller = window.main_screen.CScanRoom.currentScanRoom();
    if (current_scan_room_controller && current_scan_room_controller.enableCatch()){
        return true;
    }else{
        return false;
    }
}

CMainScreen.prototype.ctrlDeviceSaveSingleFrame = function(data) {
    var that = this;
    if (!that.isScanRoomReady()) {//扫查室是否可以捕获
        that.NotifySaveSingleFrame({error:1, error_info:"scan room error", trigger:data.trigger});
        return;
    }
    var current_scan_room_controller = window.main_screen.CScanRoom.currentScanRoom();
    var device_info = that.deviceInfo;
    var file_name = Tool.formatDateTime2(new Date()) + ".jpg";
    if (device_info) {
        var json = {
            type: 1,
            scan_room_ip: current_scan_room_controller.ultrasync_box_ip_addr,
            scan_room_port: current_scan_room_controller.ultrasync_box_cmd_port,
            file_name: file_name,
        };
        CWorkstationCommunicationMng.SaveSingleFrame(json);
    } else {
        that.NotifySaveSingleFrame({error:1, error_info: "no device info", trigger:data.trigger});
    }
}

CMainScreen.prototype.ctrlDeviceSaveMultiFrame = function(data) {
    var that = this;
    if (!that.isScanRoomReady()) {//扫查室是否可以捕获
        that.NotifySaveMultiFrame({error:1, error_info:"scan room error",  is_start:data.is_start});
        return;
    }
    var current_scan_room_controller = window.main_screen.CScanRoom.currentScanRoom();
    var device_info = that.deviceInfo;
    var file_name = Tool.formatDateTime2(new Date()) + ".mp4";
    if(data.is_start != 1){
        file_name = "";
    }
    if (device_info) {
        //type=1:ftp存储; is_start:0为结束，1为开始
        var json = {
            type: 1,
            is_start: data.is_start,
            scan_room_ip: current_scan_room_controller.ultrasync_box_ip_addr,
            scan_room_port: current_scan_room_controller.ultrasync_box_cmd_port,
            file_name: file_name,
        };

        CWorkstationCommunicationMng.SaveMultiFrame(json);
    } else {
        that.NotifySaveMultiFrame({error:1, error_info:"device_info is null",  is_start:data.is_start});
    }
}

CMainScreen.prototype.NotifyUpdateDeviceInfo = function(json){
    var that = this;
    var DeviceInfoUpdateType = window.vm.$store.state.systemConfig.DeviceInfoUpdateType;
    if(json.type == DeviceInfoUpdateType.cur_session) {
        json.error = 0;
        json.event_name = "notify_update_device_cur_session";
        that.gateway.emit("notify_device_event", json);
    }else if(json.type == DeviceInfoUpdateType.ftp_info){
        json.event_name = "notify_update_device_ftp_info";
        that.gateway.emit("notify_device_event", json);
    }
};
CMainScreen.prototype.NotifySetPatientInfo = function(json){
    var that = this;
    var data = {
        patient_id: json.patient_id,
        patient_name: json.patient_name,
        event_name: "notify_update_device_patient_info"
    };
    that.gateway.emit("notify_device_event", data);
}
CMainScreen.prototype.NotifyPatientFileList = function(json){
    json.event_name = "notify_patient_file_list";
    this.gateway.emit("notify_device_event", json);
}

CMainScreen.prototype.GetDeviceInfoAndSend = function(json){
    CWorkstationCommunicationMng.GetDeviceInfo();
}
CMainScreen.prototype.NotifyGetDeviceInfo = function(json){
    var that = this;
    //填充信息，然后把设备信息发送出去
    var auto_push_stream = localStorage.getItem('auto_push_stream_' + this.uid);
    if(auto_push_stream && JSON.parse(auto_push_stream)) {
        if(1 == JSON.parse(auto_push_stream).enable){
            that.deviceInfo.cur_session_id = JSON.parse(auto_push_stream).value;
            that.deviceInfo.cur_session_type = (JSON.parse(auto_push_stream).value_type == "Conversation") ? 0:1;
        }else{
            that.deviceInfo.cur_session_id = 0;
            that.deviceInfo.cur_session_type = 0;
        }
    }
    that.deviceInfo.device_id = json.device_id;
    that.deviceInfo.ftp_isAnonymous = json.ftp_isAnonymous;
    that.deviceInfo.ftp_account = json.ftp_account;
    that.deviceInfo.ftp_password = json.ftp_password;
    that.deviceInfo.ftp_path = json.ftp_path;
    that.deviceInfo.ftp_port = json.ftp_port;
    console.log("------------------------------------------send notify_device_info ", that.deviceInfo);
    that.deviceInfo.event_name = "notify_device_info";
    that.gateway.emit("notify_device_event", that.deviceInfo, function(is_succ){
        if(is_succ){
            CWorkstationCommunicationMng.QueryPatientInformation({});//保证notify_device_info先发出去，设置device_id
        }
    });
}
CMainScreen.prototype.NotifySaveSingleFrame = function(json){
    if(window.vm.$store.state.systemConfig.ctrl_device_type.HandShank == json.trigger){
        CWorkstationCommunicationMng.NotifyHandShankSaveSingleFrame(json);
    }else {
        json.event_name = "notify_device_save_single_frame";
        console.log("send notify_device_save_single_frame ", json);

        this.gateway.emit("notify_device_event", json);
    }
}
CMainScreen.prototype.NotifySaveMultiFrame = function(json){
    if(window.vm.$store.state.systemConfig.ctrl_device_type.HandShank == json.trigger){
        CWorkstationCommunicationMng.NotifyHandShankSaveMultiFrame(json);
    }else {
        json.event_name = "notify_device_save_multi_frame";
        console.log("send notify_device_save_multi_frame ", json);

        this.gateway.emit("notify_device_event", json);
    }
};
CMainScreen.prototype.NotifyQueryPatientInformation = function(json) {
    var that = this;
    var data = {
        patient_id: json.patient_exam_id,
        patient_name: json.patient_name,
        event_name: "notify_update_device_patient_info"
    };
    that.gateway.emit("notify_device_event", data);
}
/*
 * @description 切换账户
 */
CMainScreen.prototype.onSwitchAccount = function () {
    var that = this;
    /*
    that.socket.removeAllListeners();
    that.resetApp();
    for(var i in conversationControllerMap) {
    if (conversationControllerMap[i] && conversationControllerMap[i].socket) {
        conversationControllerMap[i].socket.disconnect();
    }
    }*/
    that.CloseSocket()
    that.resetApp();
    that.releaseConversationGatewayList();
    //AutoLogin 清理掉自动登录相关的本地存储
    window.localStorage.removeItem('local_store_device_token');
    window.CWorkstationCommunicationMng.StopConference({})

    // 清理权限系统（保留区域权限）
    try {
        if (that.permissionManager && typeof that.permissionManager.logoutCleanup === 'function') {
            that.permissionManager.logoutCleanup();
            console.log('CMainScreen切换账户权限系统清理完成');
        }
    } catch (error) {
        console.error('CMainScreen切换账户权限系统清理失败:', error);
    }

    // 清除keep-alive缓存
    window.vm.$root.eventBus.$emit('clearKeepAliveCache');

    window.vm.$router.replace(`/login`)
    window.vm.$root.eventBus.$emit('reloadRouter')
};
CMainScreen.prototype.checkMainScreenSocket=function(){
    console.log('checkMainScreenSocket')
    return new Promise((resolve,reject)=>{
        let timeout = setTimeout(()=>{
            reject(false)
        },15000)//5s
        window.main_screen.custom_ping({requestTimeOut:15000},(res)=>{
            if(res.error_code === 0){
                resolve(true)
                clearTimeout(timeout)
                timeout = null
            }else{
                reject(false)
            }

        })
    })
}

CMainScreen.prototype.userEventV2 = function ({bizContent,method,showErrorToast=true,requestTimeOut = null},callback) {
    let defaultTimeout = requestTimeOut || 300000
    if((!window.main_screen.gateway.check)&&requestTimeOut===null){
        callback&&callback({
            error_code:-1,
            data:{},
            error_message:`${method} gateway uncheck`
        })
        console.error(`${method} gateway uncheck`)
        return
    }
    let timer = null
    timer = setTimeout(()=>{
        callback&&callback({
            error_code:-1,
            data:{},
            error_message:`${method} time out`
        })
        console.error(`${method} time out`)
        if(showErrorToast){
            window.vm.$message.error(`${method}time out`);
        }
    },defaultTimeout)
    this.gateway.emit("userEventV2",{bizContent,method}, (data)=>{
        if(timer){
            clearTimeout(timer)
            timer = null
        }
        if (!isNaN(data.error_code)&&data.error_code!=0) {
            let tip = '';
            if (data.key) {
                // 尝试翻译错误键
                tip = i18n.t(data.key) || i18n.t(`error.${data.key}`);
                // 如果翻译键不存在，返回原键值，则使用默认错误信息
                if (tip === data.key || tip === `error.${data.key}`) {
                    tip = i18n.t('unknown_error') + method;
                }
            } else {
                tip = i18n.t('unknown_error') + method;
            }
            if(showErrorToast){
                window.vm.$message.error(tip);
            }

        }
        callback&&callback(data)
    });

};
const v2InterfaceList = {
    'updateLiveBroadcast':'live.broadcast.update',//创建直播预约
    'createLiveBroadcast':'live.broadcast.create',//修改直播预约
    'getLiveCreatorList':'live.broadcast.creator.list',//获取自己创建的直播列表
    'getLiveAttendeeList':'live.broadcast.attendee.list',//获取可参与的直播列表
    'getLiveHistoryList':'live.broadcast.history.list',//获取直播回看列表
    'inviteOthersLive':'live.broadcast.invite.others',//转发邀请参加直播
    'checkJoinLiveStatus':'live.broadcast.check.status',//检测是否允许加入直播间
    'cancelLiveBroadcast':'live.broadcast.cancel',//取消直播
    'getBroadcastStatusCount':'live.broadcast.notify.count',//获取当前直播状态的数量
    'getLiveInfoById':'live.broadcast.get.info',//通过直播ID获取直播信息
    'setFriendRemark':'user.set.friend.alias',
    'sendAnalyzeClick':'ai.analyze.click.result.row',
    'remoteControlEvent':'remote.control.initiate',//发送远程控制请求
    'cancelControlEvent':'remote.control.cancel',//取消发送远程控制请求
    'successControlEvent':'remote.control.success',//远程控制请求成功，双方已完成建连
    'createCategoryByGroupResource':'group.resource.category.create',//新建标签
    'updateCategoryByGroupResource':'group.resource.category.rename',//修改标签
    'deleteCategoryByGroupResource':'group.resource.category.delete',//删除标签
    'getCategoryListByGroupResource':'group.resource.category.list',//获取标签列表
    'insertResourceItemToCategory':'group.resource.category.insert.item',//标签内插入资源
    'removeResourceItemFromCategory':'group.resource.category.remove.item',//标签内删除资源
    'checkJoinGroupStatus':'group.check.join.status',//检测群的加入状态
    'getGroupsetList':'groupset.list',//获取群落列表
    'getManagerGroupsetList':'groupset.list.by.manager',//获取授权管理的群落列表
    'getGroupsetManagerList':'groupset.manager.list',//获取群落中被授权管理员的列表
    'addGroupsetManager':'groupset.add.manager',//添加群落管理员
    'deleteGroupsetManager':'groupset.delete.manager',//删除群落管理员
    'createGroupset':'groupset.create',//创建群落
    'updateGroupset':'groupset.update',//修改群落
    'getGroupsetDetail':'groupset.detail',//获取群落详情
    'addGroupsetMember':'groupset.add.member',
    'removeGroupsetMember':'groupset.remove.member',
    'deleteGroupset':'groupset.delete',
    'getChannelIdByGroupId':'conference.get.channelId',//通过群id获取声网channelId
    'requestRemotePushDoppler':'monitorWall.enter.group',//发送通知给远方用户请求开启超声直播
    'notifyRemoteMonitorPushDopplerResult':'monitorWall.workstation.ready',//通知电视墙进入会话方 开启推流结果
    'deviceBinding':'device.binding.group',//设备入群
    'deviceUnBinding':'device.unbinding.group',//设备解绑
    'getDeviceList':'query.group.joined.device.list',//查询设备列表
    'requestOpenTvWall':'tvwall.open',//打开电视墙
    'requestCloseTvWall':'tvwall.close',//关闭电视墙
    'getTvWallList':'tvwall.get.list',//获取电视墙列表
    'getTvWallToken':'tvwall.get.token',//推流器端获取加入房间所需信息
    'getOSSToken':'oss.get.temporary.token',//获取OSS TOKEN
    'deviceRename':'device.binding.rename',//设备重命名
    'getDeviceNameById':'device.binding.get.name',//获取设备名
    'createHospitalByName':'hospital.create.by.name',//添加自定义医院
    'getTvWallGroupListByExistDevice':'tvwall.group.groupset.list',//获取存在设备的群或群落
    'getConferenceRefreshToken':'conference.refresh.token',//获取新的直播token
    'applyAddFriend':'user.add.friend',//请求添加好友
    'processAddFriend':'user.process.add.friend.apply',
    'editReviewRecording':'conference.update.recording',//编辑回放视频
    'getReviewDetail':'conference.get.live.statistic',//获取回放详情数据
    'insertResourceToCategoryByName':'group.resource.category.insert.item.by.categoryname',//将资源直接通过名字塞入群收藏中
    'reportReviewEvent':'conference.report.event',//上报直播回放时间
    'getPatientInfoById':'exam.get.patient.info',//获取病人信息
    'createExam':'exam.create.exam',//创建病人检查
    'bindMobile':'user.bind.mobile',//修改用户手机
    'getInviteInfo':'group.get.invite.info',//解析群二维码参数
    'applyJoinGroup':'group.apply.join',//申请入群
    'searchGroupList':'group.list',//搜索群列表
    'setUserIntroduction':'user.set.introduction',//设置个人简介
    'startAiAnalyze':'ai.analyze.start.analyze',//请求ai分析
    'custom_ping':'custom_ping',//ping一下服务器
    'importExamMeasureFiles':'exam.import.measure.files',//导入测量json文件
    'getOSSAk':'oss.get.ak',//内网环境下， 获取oss的ak
    'updateExamInfo':'exam.update.info',//更新单个病例信息
    "getDeviceDepartmentAndHospital": "device.binding.get.department", // 获取设备的科室
    'getShareLink':'group.resource.get.share.key',
    'shareLink':'group.resource.share',
    'sendMsgOwner':'user.send.msg.owner',//发消息给自己
    'getOwnClientInfo':'user.client.info',//获取当前自己账号在别的设备的登录信息
    'sendSyncAccountOrLiveToULinker':'user.device.sync.live',//
    'checkConferenceExamIn':'conference.exam.check.in',//检测是否满足开防作弊直播的条件
}
for(let key in v2InterfaceList){
    CMainScreen.prototype[key] = function (data,callback) {
        let bizContent = cloneDeep(data)
        let requestTimeOut = undefined
        let showErrorToast = undefined
        if(bizContent&&bizContent.hasOwnProperty('requestTimeOut')){
            requestTimeOut = bizContent.requestTimeOut
            delete bizContent.requestTimeOut
        }
        if(bizContent&&bizContent.hasOwnProperty('showErrorToast')){
            showErrorToast = bizContent.showErrorToast
            delete bizContent.showErrorToast
        }

        this.userEventV2({
            method:v2InterfaceList[key],
            bizContent,
            requestTimeOut,
            showErrorToast
        }, callback);
    }
}
export default CMainScreen
