<template>
    <van-popover
        v-model="isShow"
        trigger="click"
        :actions="actions"
        @select="onSelect"
        theme="dark"
        placement="bottom-end"
        :offset="[-3,-5]"
        class="consultationMenu"
    >
        <template #reference>
            <van-icon name="add-o" color="#fff" size="1rem" class="menu-icon" />
        </template>
    </van-popover>
</template>

<script>
import base from "../lib/base";
import { Toast } from "vant";
import { Icon, Popover } from "vant";
import Tool from "@/common/tool";

export default {
    mixins: [base],
    name: "ConsultationMenu",
    components: {
        VanPopover: Popover,
        VanIcon: Icon,
    },
    data() {
        return {
            isShow: false,
            actions: [
                {
                    text: '切换到普通模式',
                    className: 'menu-item',
                    icon: 'exchange',
                    functionName: 'switchToNormalMode'
                },
                {
                    text: '退出登录',
                    className: 'menu-item logout-item',
                    icon: 'sign-out',
                    functionName: 'logout'
                }
            ],
        };
    },
    methods: {
        onSelect(val) {
            this[val.functionName]();
        },

        switchToNormalMode() {
            // 关闭main_screen socket连接
            this.$emit('switchToNormalMode');
        },

        logout() {
            Tool.openMobileDialog({
                message: "确定要退出登录吗？",
                showRejectButton: true,
                confirm: () => {
                    this.performLogout();
                },
                reject: () => {
                    // 用户取消，不做任何操作
                }
            });
        },

        performLogout() {
            // 清理登录状态
            window.localStorage.setItem('loginToken', '');
            window.localStorage.setItem('password', '');
            this.$store.commit('user/updateUser', {
                new_token: ''
            });

            // 清理权限系统（保留区域权限）
            try {
                this.$permission.logoutCleanup();
                console.log('会诊菜单权限系统退出登录清理完成');
            } catch (error) {
                console.error('会诊菜单权限系统退出登录清理失败:', error);
            }

            // 清理其他store数据
            this.$store.commit("chatList/clearChatList");
            this.$store.commit("friendList/clearFriendList");
            this.$store.commit("consultationImageList/clearConsultationImages");
            this.$store.commit("relationship/clearRelationship");

            // 断开连接
            if (window.main_screen && window.main_screen.gateway) {
                window.main_screen.CloseSocket();
            }

            Toast("已退出登录");

            // 跳转到登录页面
            setTimeout(() => {
                this.$router.replace('/login');
            }, 1000);
        }
    },
};
</script>

<style lang="scss" scoped>
// 继承原有的menu样式
</style>

<style lang="scss">
.van-popover[data-popper-placement=bottom-end] .van-popover__arrow{
    right: 5px;
}
.van-popover__action{
    height: auto;
}
.van-popover__action-text{
    line-height: 1.6;
    padding: 10px 0;
    word-break: auto-phrase;
}
.van-popover__content{
    .menu-item{
        width: 9rem;
    }

    .logout-item {
        color: #ff4757;

        &:hover {
            background-color: rgba(255, 71, 87, 0.1);
        }
    }
}
.consultationMenu{
    line-height: 2.95rem;
    height: 2.95rem;
    width: 2.5rem;
}
</style>
