import ConversationConfig from '@/common/ConversationConfig.js';
import { Toast,Dialog } from 'vant';
import moment from 'moment'
import proxyConfig from '../../config/proxy'
import {openCommonDialog,getCommonDialogId,openMobileDialog,closeCommonDialog,checkMobileDialogShow,checkMobileCanCloseDialog,checkMobileCanCloseOnPopstate,closeMobileDialog,closeAllDialog} from './dialog'
import Vue from 'vue'
import requestManager from '@/common/CommunicationMng/requestManager';
import hostConfig from '../../config/hostConfig';
import { Loading } from 'element-ui';
import i18n from '@/common/i18n'
const loadedRouters = new Set();
var Tool={}
Tool.openCommonDialog = openCommonDialog
Tool.getCommonDialogId = getCommonDialogId
Tool.openMobileDialog = openMobileDialog
Tool.closeCommonDialog = closeCommonDialog
Tool.checkMobileCanCloseDialog = checkMobileCanCloseDialog
Tool.checkMobileDialogShow = checkMobileDialogShow
Tool.checkMobileCanCloseOnPopstate = checkMobileCanCloseOnPopstate
Tool.closeMobileDialog = closeMobileDialog
Tool.closeAllDialog = closeAllDialog
Tool.percentURLEncode = function(str) {
    str = str.replace(/\+/g, '%2B');
    str = str.replace(/ /g, '%20');
    str = str.replace(/\?/g, '%3F');
    str = str.replace(/#/g, '%23');
    str = str.replace(/&/g, '%26');
    str = str.replace(/=/g, '%3D');

    return str;
}

Tool.triggerTime=function(){
    return " at " + (new Date()).toLocaleString();
}

/* 拿不到this.$store.state.systemConfig.serverInfo
Tool.genRtmpVoiceServerUrl = function(cid){
    var serverInfo = this.$store.state.systemConfig.serverInfo;//this.systemConfig.serverInfo;
    var url = "";
    var timestamp = new Date().getTime();
    if (1 == serverInfo.network_environment){//内网  //rtmp://consult.mindray.com:8935/mrlive/mindray_sound_123
        url = "rtmp://" + serverInfo.VideoServerAddr + ":" + serverInfo.rtmp_video_server_port + "/" + serverInfo.RtmpVideoServerChannel + "/mindray_sound_" + cid  + "_" + timestamp;
    }else{
        url = "rtmp://" + serverInfo.RtmpVideoServerCDN + "/" + serverInfo.RtmpVideoServerChannel + "/mindray_sound_" + cid   + "_" + timestamp + "?vhost=" + serverInfo.RtmpVideoServer;
    }
    return url;
}*/

/**
 * @description  获取json长度
 */
Tool.getHsonLength = function(json){
    var jsonLength=0;
    for (var i in json) {
        jsonLength++;
    }
    return jsonLength;
}
Tool.getShowTime=function(msg,type=1){
    let ts=msg.send_ts||msg.timestamp;
    if(!ts){
        return ''
    }else{

        let time;
        if (typeof ts=='string') {
            ts = moment(ts).format("YYYY-MM-DD HH:mm::ss z");
            time=new Date(ts.replace(/-/g,"/"))
        }else{
            time=new Date(ts)
        }
        // let year=time.getFullYear();
        // let month=time.getMonth();
        // let day=time.getDate();
        let now=new Date();
        let n_year=now.getFullYear();
        let n_month=now.getMonth();
        let n_day=now.getDate();
        let today=new Date(n_year,n_month,n_day);
        if (time-today>0) {
            //时间在今天0点之后
            let hours=time.getHours()
            hours=hours>9?hours:'0'+hours
            let minutes=time.getMinutes()
            minutes=minutes>9?minutes:'0'+minutes
            return hours+':'+minutes
        }else if(today-time<=24*60*60*1000){
            //时间在昨天0点之后
            let hours=time.getHours()
            hours=hours>9?hours:'0'+hours
            let minutes=time.getMinutes()
            minutes=minutes>9?minutes:'0'+minutes
            return i18n.t('yesterday_text')+' '+hours+':'+minutes
        }else{
            //昨天以前
            if (type==1) {
                //只展示年月日
                return ts.split(" ")[0]
            }else{
                //展示年月日时分
                return ts.slice(0,16)
            }

        }
    }
}



Tool.checkSpeakPermission = function(cid, uid){
    var systemConfig = window.vm.$store.state.systemConfig;
    var conversation = window.vm.$store.state.conversationList[cid];
    if(conversation){
        if(systemConfig.groupPublicState.SemiPublic == conversation.is_public){//半公开群
            if(systemConfig.attendee_state.Temp == conversation.attendeeList["attendee_" + uid].attendeeState ||
            systemConfig.attendee_state.Applying == conversation.attendeeList["attendee_" + uid].attendeeState ){ //临时成员
                return false;
            }
        }
    }
    return true;
}

Tool.ifAppClientType = function (client_type) {
    var UserConfig = window.vm.$store.state.systemConfig;

    var ret = false;
    if (UserConfig.client_type.AppWorkstation == client_type
        || UserConfig.client_type.AppClient == client_type
        || UserConfig.client_type.AppMobile == client_type
        || UserConfig.client_type.Doppler == client_type
        || UserConfig.client_type.AppUltraSyncBox == client_type
        || UserConfig.client_type.AppPad == client_type) {
        ret = true;
    }

    return ret;
};

Tool.ifAppWorkstationClientType = function (client_type) {
    var UserConfig = window.vm.$store.state.systemConfig;

    var ret = false;
    if (UserConfig.client_type.AppWorkstation == client_type
        || UserConfig.client_type.Doppler == client_type
        || UserConfig.client_type.AppUltraSyncBox == client_type) {
        ret = true;
    }

    return ret;
};
Tool.ifPcClientType = function (client_type) { //是否包含PC端所有场景
    var UserConfig = window.vm.$store.state.systemConfig;
    var ret = false;
    if (UserConfig.client_type.AppWorkstation == client_type
        || UserConfig.client_type.AppClient == client_type
        || UserConfig.client_type.Doppler == client_type
        || UserConfig.client_type.AppUltraSyncBox == client_type) {
        ret = true;
    }
    return ret;
};
Tool.ifAppConsultationClientType = function (client_type) {
    var UserConfig = window.vm.$store.state.systemConfig;

    var ret = false;
    if (UserConfig.client_type.AppClient == client_type
        || UserConfig.client_type.AppMobile == client_type
        || UserConfig.client_type.AppPad == client_type) {
        ret = true;
    }

    return ret;
};

Tool.ifDeviceClientType = function (client_type) {
    var UserConfig = window.vm.$store.state.systemConfig;

    var ret = false;
    if (UserConfig.client_type.Doppler == client_type
        || UserConfig.client_type.AppUltraSyncBox == client_type) {
        ret = true;
    }

    return ret;
};

Tool.ifBrowserClientType = function (client_type) {
    var UserConfig = window.vm.$store.state.systemConfig;

    var ret = false;
    if (UserConfig.client_type.Client == client_type
        || UserConfig.client_type.MobileBrowser == client_type) {
        ret = true;
    }

    return ret;
};

Tool.ifMobileClientType = function (client_type) {
    var UserConfig = window.vm.$store.state.systemConfig;

    var ret = false;
    if (UserConfig.client_type.AppMobile == client_type) {
        ret = true;
    }

    return ret;
};

Tool.getAccessStreams = function (uid, client_type, client_uuid, start_catch_type, streams) {
    console.log("[event] Tool.getAccessStreams");
    console.log(uid);
    console.log(client_type);
    console.log(client_uuid);
    console.log(start_catch_type);
    console.log(streams);

    var json = {};
    if (streams) {
        for (var i in streams) {
            var stream = streams[i];
            if (ConversationConfig.start_catch_type.UltrasoundDesktop == start_catch_type
                || ConversationConfig.start_catch_type.LocalDesktop == start_catch_type) {
                if (uid == stream.sender_id && client_uuid == stream.client_uuid && this.ifAppWorkstationClientType(client_type)) {
                    if (11 == stream.type) {
                        json.ultrasound_video = stream.url;
                    } else if (12 == stream.type) {
                        json.gesture_video = stream.url;
                    }
                } else {
                    if (1 == stream.type) {
                        json.ultrasound_video = stream.url;
                    } else if (2 == stream.type) {
                        json.gesture_video = stream.url;
                    }
                }
            } else if (ConversationConfig.start_catch_type.MonitorWall == start_catch_type) {
                if (21 == stream.type) {
                    json.ultrasound_video = stream.url;
                } else if (22 == stream.type) {
                    json.gesture_video = stream.url;
                }
            } else if (ConversationConfig.start_catch_type.StorageConsultationFile == start_catch_type) {
                if (11 == stream.type) {
                    json.ultrasound_video = stream.url;
                } else if (12 == stream.type) {
                    json.gesture_video = stream.url;
                }
            }
        }
    }

    //console.log(json);

    if (!json.ultrasound_video) {
        json = null;
    }

    console.log(json);

    return json;
};

//计算字符串长度
String.prototype.strLen = function() {
    var len = 0;
    for (var i = 0; i < this.length; i++) {
        if (this.charCodeAt(i) > 255 || this.charCodeAt(i) < 0){
            len += 2;
        }else{
            len ++;
        }
    }
    return len;
}

//截取字符串（从start字节到end字节）
String.prototype.subCHString = function(start, end){
    var len = 0;
    var str = "";
    this.strToChars();
    for (var i = 0; i < this.length; i++) {
        if(this.charsArray[i][1]){
            len += 2;
        }else{
            len++;
        }
        if (end < len){
            return str;
        }else if (start < len){
            str += this.charsArray[i][0];
        }
    }
    return str;
}

//将字符串拆成字符，并存到数组中
String.prototype.strToChars = function(){
    var chars = new Array();
    for (var i = 0; i < this.length; i++){
        chars[i] = [this.substr(i, 1), this.isCHS(i)];
    }
    String.prototype.charsArray = chars;
    return chars;
}

//判断某个字符是否是汉字
String.prototype.isCHS = function(i){
    if (this.charCodeAt(i) > 255 || this.charCodeAt(i) < 0){
        return true;
    }else{
        return false;
    }
}
/**
 * @description  通过设备label获取设备名字，获取第一个左括号到匹配的右括号之间的内容
 * label：麦克风 (2- Logitech Wireless Headset) (046d:0a29)
 * name： 麦克风 (2- Logitech Wireless Headset)
 *
 * label："HP N246v (英特尔(R) 显示器音频)
 * name：英特尔(R) 显示器音频
 */
Tool.getNameForAudioDeviceLabel = function(label){
    if(label && ("" != label)){
        var last_bracket_content = "";
        var left_index =  label.lastIndexOf('(');
        var right_index = label.lastIndexOf(')');
        if((-1 != right_index) && (-1 != left_index) && (right_index > left_index)){
            last_bracket_content = label.substr(left_index + 1, right_index-left_index-1);
        }

        var reg=/^.{4}:.{4}$/;
        if(last_bracket_content.match(reg)) {
            var result = label.substr(0, left_index).trim();
            return result;
        }else{
            return label;
        }
    }
    return "";
};

Tool.createObj =  function (o){
    function F(){};
    F.prototype = o;
    return new F();
};

Tool.inherits = function prototype(child,parent){
    var prototype = Tool.createObj(parent.prototype);
    prototype.constructor = child;
    child.prototype = prototype;
};
Tool.formatCurrentTime = function() {
    var date = new Date();
    var time = date.getFullYear()
        + ''
        + ((date.getMonth() < 9) ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1))
        + ''
        + (date.getDate()  < 10 ? ('0' + date.getDate()) : date.getDate())
        + ''
        + (date.getHours()  < 10 ? ('0' + date.getHours()) : date.getHours())
        + ''
        + (date.getMinutes() < 10 ? ('0' + date.getMinutes()) : date.getMinutes())
        + ''
        + (date.getSeconds() < 10 ? ('0' + date.getSeconds()) : date.getSeconds())
        + ''
        + date.getMilliseconds();
    return time;
};
Tool.GetVolumeLevel = function(value, max){
    var level = 0;
    if(value){
        level = Math.round(value * 10 / max);
        if(level < 0){
            level = 0;
        }else if(level > 10){
            level = 10;
        }
    }
    return level;
};

Tool.cmpArrGetAddSet = function(old_conns, new_conns){
    var need_add_conns = [];
    for(var i = 0; i < new_conns.length; i++) {
        var new_conn = new_conns[i];
        var find_new_in_old = false;
        for(var j = 0; j < old_conns.length; j++) {
            if(old_conns[j] == new_conn){
                find_new_in_old = true;
                break;
            }
        }
        if(!find_new_in_old){
            need_add_conns.push(new_conn);
        }
    }

    return need_add_conns;
}

Tool.cmpArrGetDelSet = function(old_conns, new_conns){
    var need_del_conns = [];
    for(var i = 0; i < old_conns.length; i++) {
        var old_conn = old_conns[i];
        var find_old_in_new = false;
        for(var j = 0; j < new_conns.length; j++) {
            if(new_conns[j] == old_conn){
                find_old_in_new = true;
                break;
            }
        }
        if(!find_old_in_new){
            need_del_conns.push(old_conn);
        }
    }

    return need_del_conns;
}

Tool.getAudioDevice = function(fn){
    navigator.mediaDevices.enumerateDevices()
        .then(function(devices) {
            console.log("devices", devices);
            var audio_input_devices = [];
            var audio_output_devices = [];
            var default_audio_input_device = "";
            var default_audio_output_device = "";
            devices.forEach(function(device) {
                if(("default" != device.deviceId) && ("communications" != device.deviceId) ){
                    if ('audiooutput' === device.kind) {
                        audio_output_devices.push(device.label);
                    } else if ('audioinput' === device.kind) {
                        audio_input_devices.push(device.label);
                    }
                }else if(("default" == device.deviceId)){
                    if ('audioinput' === device.kind) {
                        default_audio_input_device = Tool.getNameForAudioDeviceLabel(device.label);
                    } else if ('audiooutput' === device.kind) {
                        default_audio_output_device = Tool.getNameForAudioDeviceLabel(device.label);
                    }
                }
            });

            var json = {
                error:0,
                audio_input_devices:audio_input_devices,
                audio_output_devices:audio_output_devices,
                default_audio_input_device:default_audio_input_device,
                default_audio_output_device:default_audio_output_device
            };
            fn(json);
        })
        .catch(function(err) {
            console.log("无法枚举输入输出设备");
            console.log(err.name + ": " + err.message);
            var json = {
                error:1,
                audio_input_devices:[],
                audio_output_devices:[],
                default_audio_input_device:"",
                default_audio_output_device:""
            };
            fn(json);
        });
}
Tool.getVideoDevice = function(){
    return new Promise((resolve,reject)=>{
        window.CWorkstationCommunicationMng.GetCameraDevice()
        setTimeout(()=>{
            const effectCameraList = window.vm.$store.state.device.camera.filter(item=>!item.exclude)
            resolve(effectCameraList)
        },500)
    })
}
/**
 * @description 格式化日期时间1
 * @returns {string}
 */
Tool.formatDateTime = function (ostr, type) {
    //type:1  yyyy-mm-dd
    //type:2  HH:MM:SS
    //type:3  yyyy-mm-dd HH:MM:SS
    //type:4  HH:MM
    //type:5 yyyy-mm
    // let str = ostr.replace(/-/g,"/") //兼容ios无法解析-时间
    let str = ''
    if(typeof ostr === 'string'){
        if(ostr.indexOf('T')>0){//包含时区时间
            str = ostr.replace(/(\+\d{2})(\d{2})$/, "$1:$2")
        }else {
            str = ostr.replace(/-/g,"/")
        }
    }else{
        str = ostr
    }

    var date = new Date(str)
    let time = ''
    let str1 = date.getFullYear() + '-' + (date.getMonth() < 9 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
    let str2 = '-' + (date.getDate() < 10 ? '0' + date.getDate() : date.getDate())
    let str3 = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':' + (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes())
    let str4 = ':' + (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds())
    if (type == 1) {
        time = str1 + str2
    } else if (type == 2) {
        time = str3 + str4
    } else if (type == 3) {
        time = str1 + str2 + ' ' + str3 + str4
    } else if (type == 4) {
        time = str3
    } else if (type == 5) {
        time = str1
    } else if (type == 6) {
        time = str1 + str2 + ' ' + str3
    }
    return time
}
/**
 * @description 格式化日期时间
 * @returns {string}
 */
Tool.formatDateTime2 = function(str) {
    if ("0000-00-00-00-00" == str) {
        return str;
    }

    var date = new Date(str);
    var time = date.getFullYear()
        + '-'
        + ((date.getMonth() < 9) ? ('0' + (date.getMonth() + 1)) : (date.getMonth() + 1))
        + '-'
        + (date.getDate()  < 10 ? ('0' + date.getDate()) : date.getDate())
        + '-'
        + (date.getHours()  < 10 ? ('0' + date.getHours()) : date.getHours())
        + '-'
        + (date.getMinutes() < 10 ? ('0' + date.getMinutes()) : date.getMinutes())
        + '-'
        + (date.getSeconds() < 10 ? ('0' + date.getSeconds()) : date.getSeconds());
    return time;
};

Tool.parseStartupOption = function (str) {
    var UserConfig = window.vm.$store.state.systemConfig;

    var option = {};
    var arr = str.split('&');
    for (var i in arr) {
        var index = arr[i].indexOf('=');
        if (index > 0) {
            var key = arr[i].substr(0, index);
            var value = arr[i].substr(index+1);
            option[key] = value;
        }
    }

    var user_info = {};
    if ("undefined" != option.UserId) {
        user_info.outer_id = option.UserId;
    }

    if ("undefined" != option.UserNickname) {
        user_info.nickname = option.UserNickname;
    }

    if ("undefined" != option.DomainId) {
        user_info.account_type = UserConfig.account_type[option.DomainId];
    }
    user_info.account_type = user_info.account_type || UserConfig.account_type.PACS;

    if ("undefined" != option.ConsultId) {
        user_info.cid = option.ConsultId;
    }

    if ("undefined" != option.Token) {
        user_info.token = option.Token;
    }

    if ("undefined" != option.Guid) {
        user_info.guid = option.Guid;
    }

    return user_info;
};

Tool.JulianToDateTimeStamp = function (num) {
    var x;
    var j = num - 1721119;

    var y = Math.round(Math.floor((j*4 - 1)/146097));
    j = j*4 - 146097*y - 1;
    var x = Math.round(Math.floor(j/4));
    j = Math.round(Math.floor((x*4 + 3)/1461));
    y = 100*y + j;
    x = (x*4) + 3 - 1461*j;
    x = Math.round(Math.floor((x + 4)/4));
    var m = Math.round(Math.floor((5*x - 3)/153));
    x = 5*x - 3 - 153*m;
    var d = Math.round(Math.floor((x + 5)/5));

    if ( m < 10 )    {
        m += 3;
    }    else    {
        m -= 9;
        y++;
    }

    var date = new Date();
    date.setFullYear(y,m-1,d);
    date.setHours(0,0,0);

    return date.getTime();
};

Tool.formatJulianOrGregorianToDate = function (str) {
    if (!str) {
        return str;
    }

    var numbers_reg = /^[0-9]+$/;
    if (numbers_reg.test(str)) {
        var number = parseInt(str);
        var timestamp = Tool.JulianToDateTimeStamp(number);
        str = Tool.formatDateTime(timestamp,1);
    }

    return str;
};
Tool.isMobile=function(){
    if(window.navigator.userAgent.match(/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrower|BrowerNG|WebOs|Symbian|Windows Phone)/i)){
        return true
    }else{
        return false
    }
};
Tool.genID = (function() {
    let counter = 0; // 定义一个计数器
    const MAX_COUNTER = 10000; // 设置计数器的上限，防止溢出

    return function(length) {
        counter = (counter + 1) % MAX_COUNTER; // 计数器超过上限后重置为 0

        // 生成随机数部分
        const randomPart = Math.random().toString().substr(3, length);

        // 获取当前时间戳部分
        const timePart = Date.now().toString(36);

        // 将计数器部分转为固定长度的字符串，避免过大时超出范围
        const counterPart = counter.toString(36).padStart(2, '0');

        return Number(randomPart) + timePart + counterPart;
    };
})();
Tool.genID2 = function (uid){ // 根据userID+日期生成36位 321_12xsa123
    return `${uid}_${Date.now().toString(36)}`
}
Tool.formatUrl = function (url){ // 格式化URL为json
    let queryString = url.slice(url.indexOf('?')+1)
    let temArr = queryString.split('&')
    let obj = {}
    temArr.forEach(item=>{
        let arr = item.split('=')
        obj[arr[0]] = arr[1]
    })
    return obj
}
Tool.isBase64 = function (str){ // 判断字符串是否被base64加密过
    if(!str){
        return false
    }
    let reg = /^([A-Za-z0-9+/]{4})*([A-Za-z0-9+/]{4}|[A-Za-z0-9+/]{3}=|[A-Za-z0-9+/]{2}==)$/
    if(reg.test(str)){
        return true
    }else{
        return false
    }

}
Tool.transferHeightToRem = function(height = 0){ //将拿到的高度转化为rem
    let systemFontSize = window.getComputedStyle(document.querySelector('body'),null).getPropertyValue('font-size')
    let systemFontSizeNum = Number(systemFontSize.replace('px',''))
    let remHeight = height/systemFontSizeNum.toFixed(2) + 'rem'
    return remHeight
}
Tool.transferRemToHeight = function(rem = 0){ //将拿到rem转化成实际height
    let systemFontSize = window.getComputedStyle(document.querySelector('body'),null).getPropertyValue('font-size')
    let systemFontSizeNum = Number(systemFontSize.replace('px',''))
    let height = (rem*systemFontSizeNum).toFixed(2)
    return Number(height)
}
Tool.restArguments=function(func, startIndex) {
    startIndex = startIndex == null ? func.length - 1 : +startIndex;
    return function() {
        var length = Math.max(arguments.length - startIndex, 0),
            rest = Array(length),
            index = 0;
        for (; index < length; index++) {
            rest[index] = arguments[index + startIndex];
        }
        switch (startIndex) {
        case 0: return func.call(this, rest);
        case 1: return func.call(this, arguments[0], rest);
        case 2: return func.call(this, arguments[0], arguments[1], rest);
        }
        var args = Array(startIndex + 1);
        for (index = 0; index < startIndex; index++) {
            args[index] = arguments[index];
        }
        args[startIndex] = rest;
        return func.apply(this, args);
    };
}
Tool.throttle = function(func, wait, options) {
    //函数节流，代码来自underscore
    var timeout, context, args, result;
    var previous = 0;
    if(!options){
        options = {}
    };
    var later = function() {
        previous = options.leading === false ? 0 : Date.now();
        timeout = null;
        result = func.apply(context, args);
        if(!timeout){
            context = args = null
        };
    };
    var throttled = function() {
        var now = Date.now();
        if(!previous && options.leading === false){
            previous = now
        };
        var remaining = wait - (now - previous);
        context = this;
        args = arguments;
        if(remaining <= 0 || remaining > wait) {
            if(timeout) {
                clearTimeout(timeout);
                timeout = null;
            }
            previous = now;
            result = func.apply(context, args);
            if(!timeout){
                context = args = null
            };
        } else if (!timeout && options.trailing !== false) {
            timeout = setTimeout(later, remaining);
        }
        return result;
    };
    throttled.cancel = function() {
        clearTimeout(timeout);
        previous = 0;
        timeout = context = args = null;
    };
    return throttled;
}
Tool.debounce = function(func, wait, immediate) {
    //函数防抖，代码来自underscore
    var timeout, result;
    var later = function(context, args) {
        timeout = null;
        if(args){
            result = func.apply(context, args)
        }
    };
    var delay = Tool.restArguments(function(func, wait, args) {
        return setTimeout(function() {
            return func.apply(null, args);
        }, wait);
    });
    var debounced = Tool.restArguments(function(args) {
        if (timeout) {
            clearTimeout(timeout)
        };
        if (immediate) {
            var callNow = !timeout;
            timeout = setTimeout(later, wait);
            if (callNow){
                result = func.apply(this, args)
            }
        } else {
            timeout = delay(later, wait, this, args);
        }
        return result;
    });
    debounced.cancel = function() {
        clearTimeout(timeout);
        timeout = null;
    };
    return debounced;
}
/**
 * @description 判断是否为IP
 * @param ip
 * @returns {boolean}
 */
Tool.isIP = function(ip) {
    const reg =
        /^(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])\.(\d{1,2}|1\d\d|2[0-4]\d|25[0-5])$/
    return reg.test(ip)
}
Tool.isEmail = function(str){
    const reg =/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/
    return reg.test(str)
}
/**
 * @description 病理结论格式化
 * @param pathologyInfo 病例基本信息
 * @param sliderKey 病例图片下标
 * @returns {String}
 */
Tool.getPathologyConclusion = function(pathologyInfo,sliderKey) {
    // console.log(sliderKey);
    // console.log(pathologyInfo);
    // console.log('getPathologyConclusion');
    if(Object.keys(pathologyInfo).length===0){
        return
    }
    var info=pathologyInfo.ultrasonic_diagnosis_list[sliderKey]
    //有无钙化
    var calcificationInfo=info.calcification_location+info.calcification_morphology
    if(info.calcification_location=='' && info.calcification_morphology==''){
        calcificationInfo="无钙化"
    }
    const edges=info.clear_edges===''?'边缘清晰':`边缘${info.clear_edges}`
    const pathologyResult=`${info.way},距乳头距离${info.dist_from_nipple}cm,最大径${info.max_diameter}cm,${info.shape},${edges},${info.echo_type},${info.posterior_echo},${calcificationInfo},${info['BI-RADS']}`
    // var pathologyResult=res
    return pathologyResult
}
Tool.replaceAppNameToLanguages = (languages) => {
    const replaceList=[
        'app_name',
        'about_ultrasync',
        // 'mindray_long_distance_consultation',
        'remote_ultrasonic_consultation_system',
        // 'notification_title',
        'privacy_statement_title',
        'please_agree_privacy_policy',
        'ultrasync_privacy_protocol',
        'privacy_welcome_title',
        'privacy_welcome_p1',
        'privacy_welcome_p2',
        // 'software_description',
        'ultrasync_live',
        'welcome_tip',
        'referral_introduce_A1',
        'referral_introduce_A2',
        'download_tip',
        'download_window',
        'download_app_tip',
        'log_off_waring',
        'weblive_download_app_tips',
        'weblive_download_client_tips',
    ]

    // 创建更新后的语言包
    const updatedMessages = {}

    // 处理CN语言包
    if(languages['CN']) {
        const cnMessages = {}
        replaceList.forEach(item=>{
            if(languages['CN'].hasOwnProperty(item)){
                cnMessages[item] = languages['CN'][item].replace(/瑞影云\+\+/g,'MiCo+')
            }
        })
        if(Object.keys(cnMessages).length > 0) {
            updatedMessages['CN'] = cnMessages
        }
    }

    // 使用新的i18n更新方法
    if(Object.keys(updatedMessages).length > 0 && i18n) {
        // 动态导入updateMessages函数
        import('@/common/i18n').then(({ updateMessages }) => {
            updateMessages(updatedMessages)
        }).catch(err => {
            console.error('Failed to import i18n updateMessages:', err)
        })
    }
}
Tool.addParamsToUrl=(url, params)=>{
    // 检查参数对象是否为空
    if (Object.keys(params).length === 0) {
        return url;
    }
    // 将参数对象转换为查询字符串
    const queryParams = new URLSearchParams(params).toString();
    // 检查 URL 是否已经存在查询参数
    const separator = url.includes('?') ? '&' : '?';
    // 将查询字符串添加到 URL 后面
    const updatedUrl = url + separator + queryParams;
    return updatedUrl;
}
Tool.decodeHTML = (str) => {
    str = str || '';

    // 1. 先解码常见的 HTML 实体
    str = str.replace(/&lt;/g, '<');
    str = str.replace(/&gt;/g, '>');
    str = str.replace(/&#x2F;/g, '/');
    str = str.replace(/&amp;/g, '&');

    // 2. 使用正则提取 <a> 标签的 data-url 属性并替换它
    const aTagRegex = /<a[^>]*data-url\s*=\s*['"]([^'"]+)['"][^>]*>.*?<\/a>/gi;

    // 3. 替换 <a> 标签为它的 data-url 属性值
    str = str.replace(aTagRegex, (match, dataUrl) => {
        return dataUrl; // 只保留 data-url 属性值
    });

    // 4. 返回替换后的字符串
    return str;
};
Tool.clearCWorkstationCommunicationMngEmitEvent = ({
    emitNames,
})=>{
    console.log('clearCWorkstationCommunicationMngEmitEvent',emitNames)
    if(Array.isArray(emitNames)){
        emitNames.forEach(emitName=>{
            window.vm.$root.eventBus.$off(emitName)
        })
    }
}
Tool.createCWorkstationCommunicationMng = ({
    name,
    emitName,
    params,
    timeout = 10000,
    callback,
    checkClientType = true,
    unique = false, // 默认为不使用 requestId
}) => {
    return new Promise((resolve, reject) => {
        if (Tool.checkAppClient('Browser') && checkClientType) {
            return reject(`window browser not allowed ${name} method`);
        }
        return requestManager.sendRequest({
            name,
            emitName,
            timeout,
            fn: ({ name, params }) => window.CWorkstationCommunicationMng[name](params),
            params,
            callback,
            unique, // 传递 useRequestId
        }).then(resolve).catch(reject);
    });
};
Tool.sleep = (delay=50)=>{
    return new Promise((resolve)=>{
        setTimeout(resolve,delay)
    })
}

Tool.loadScript=(arr,callback)=>{
    let src=arr.shift();
    if (/localhost/.test(window.location.host)||/file:\/\//.test(window.location.href)||Tool.isIP(window.location.hostname)) {
        // 本地开发环境切换到dev
        const serverType = window.vm.$store.state.systemConfig.server_type;
        src = `${serverType.protocol}${serverType.hostname}${src}`;
    }
    if (document.getElementById(src)) {
        if(arr.length==0){
            callback&&callback();
        }else{
            Tool.loadScript(arr,callback);
        }
        return ;
    }
    var script = document.createElement('script')
    script.type = 'text/javascript';
    script.id = src;
    script.onload =()=>{
        if(arr.length==0){
            callback&&callback();
        }else{
            Tool.loadScript(arr,callback);
        }
    }
    script.src=src;
    document.body.appendChild(script)
}
Tool.loadJS = (url,data={})=>{
    return new Promise((resolve,reject)=>{
        const d = document;
        let s = d.createElement('script');
        s.type = 'text/javascript';
        s.charset = 'utf-8';
        Object.keys(data).forEach(key=>{
            s[key] = data[key]
        })
        if (s.readyState) {
            // IE
            s.onreadystatechange = function () {
                if (s.readyState === 'loaded' || s.readyState === 'complete') {
                    s.onreadystatechange = null;
                    resolve()
                }
            };
        } else {
            s.onload = function () {
                resolve()
            };
            s.onerror = function(error){
                reject(error)
            }
        }
        s.src = url;
        d.getElementsByTagName('head')[0].appendChild(s);
    })
}
Tool.loadCSS = (url)=>{
    return new Promise((resolve,reject)=>{
        let l = document.createElement('link');
        l.rel = 'stylesheet';
        l.type = 'text/css';
        l.href = url;
        document.getElementsByTagName('head')[0].appendChild(l);
        if (l.readyState) {
            // IE
            l.onreadystatechange = function () {
                if (l.readyState === 'loaded' || l.readyState === 'complete') {
                    l.onreadystatechange = null;
                    resolve()
                }
            };
        } else {
            l.onload = function () {
                resolve()
            };
            l.onerror = function(error){
                reject(error)
            }
        }
    })

}
Tool.removeLinkCss = (url)=>{
    var filename = url;  //移除引入的文件名
    var targetelement = "link";
    var targetattr = "href";
    var allsuspects = document.getElementsByTagName(targetelement)
    for (var i = allsuspects.length; i>=0 ; i--){
        if (allsuspects[i] && allsuspects[i].getAttribute(targetattr) != null && allsuspects[i].getAttribute(targetattr).indexOf(filename) != -1) {
            allsuspects[i].parentNode.removeChild(allsuspects[i])
        }
    }
}
Tool.removeScript = (url)=>{
    var filename = url;  //移除引入的文件名
    var targetelement = "script";
    var targetattr = "src";
    var allsuspects = document.getElementsByTagName(targetelement)
    for (var i = allsuspects.length; i>=0 ; i--){
        if (allsuspects[i] && allsuspects[i].getAttribute(targetattr) != null && allsuspects[i].getAttribute(targetattr).indexOf(filename) != -1) {
            allsuspects[i].parentNode.removeChild(allsuspects[i])
        }
    }
}
Tool.dataURLtoBlob=(dataurl,datatype)=>{
    // let arr = dataurl.split(',');
    // let mime = arr[0].match(/:(.*?);/)[1];
    // let bstr = atob(arr[1]);
    let bstr = window.atob(dataurl)
    let  mime = datatype;
    let n = bstr.length;
    let u8arr = new Uint8Array(n);
    while (n--) {
        u8arr[n] = bstr.charCodeAt(n);
    }
    return new Blob([u8arr], { type: mime });
}

Tool.blobToFile=(theBlob,fileName)=>{
    theBlob.lastModifiedDate = new Date();  // 文件最后的修改日期
    theBlob.name = fileName;                // 文件名
    return new File([theBlob], fileName, {type: theBlob.type, lastModified: Date.now(),lastModifiedDate:new Date()});
}
Tool.base64ImageToBlob=(base64)=>{
    var base64Arr = base64.split(',');
    var imgtype = '';
    var base64String = '';
    if(base64Arr.length > 1){
        //如果是图片base64，去掉头信息
        base64String = base64Arr[1];
        imgtype = base64Arr[0].substring(base64Arr[0].indexOf(':')+1,base64Arr[0].indexOf(';'));
    }
    // 将base64解码
    var bytes = atob(base64String);
    //var bytes = base64;
    var bytesCode = new ArrayBuffer(bytes.length);
    // 转换为类型化数组
    var byteArray = new Uint8Array(bytesCode);

    // 将base64转换为ascii码
    for (var i = 0; i < bytes.length; i++) {
        byteArray[i] = bytes.charCodeAt(i);
    }

    // 生成Blob对象（文件对象）
    return new Blob( [bytesCode] , {type : imgtype});
}
Tool.showSize=(base64url)=>{
    let str = base64url
    let equalIndex = str.indexOf("=");
    if (str.indexOf("=") > 0) {
        str = str.substring(0, equalIndex);
    }
    let strLength = str.length;
    let fileLength = parseInt(strLength - (strLength / 8) * 2);
    let size = "";
    size = (fileLength / 1024).toFixed(2);
    let sizeStr = size + "";
    let index = sizeStr.indexOf(".");
    let dou = sizeStr.substr(index + 1, 2);
    if (dou == "00") {
        return sizeStr.substring(0, index) + sizeStr.substr(index + 3, 2);
    }
    return size;
}
Tool.isLegalForematForSearchImage=(msg)=>{
    // Image: 1,
    // File: 2,
    // Frame: 3,
    // Cine: 4,
    // VIDEO_CLIP:26,
    // EXAM_IMAGES:42,
    // OBAI: 10,m
    // console.error('msg.msg_type',msg.msg_type)
    if([1, 2, 3, 10].indexOf(msg.msg_type)>-1){
        let url = msg.url
        let realUrl = msg.realUrl
        if(typeof realUrl=='object'){
            realUrl = realUrl.serverRealUrl
        }
        if(msg.img_encode_type && msg.img_encode_type !='unknown'){
            return ['jpg','jpeg','png','bmp'].indexOf(msg.img_encode_type.toLowerCase()) >-1
        }
        if(realUrl && realUrl!=undefined && realUrl!=null){
            let file_type = realUrl.replace(/.+\./, "").toLowerCase();
            return ['jpg','jpeg','png','bmp'].indexOf(file_type) >-1
        }
        if(url && url!=undefined && url!=null){
            let file_type = url.replace(/.+\./, "").toLowerCase();
            return ['jpg','jpeg','png','bmp'].indexOf(file_type) >-1
        }else{
            return false
        }
    }else{
        return false
    }
}
Tool.getSocketServer=()=>{
    //socket.io不允许传递Localhost，开发环境会转化
    let url=window.location.origin;
    if (/localhost/.test(url)) {
        url=proxyConfig.env
    }
    return url;
}
//获取文件类型名称
Tool.getFileType = (filename)=>{
    if(filename.indexOf('.')>-1){
        var parts = filename.split('.');
        return parts[parts.length - 1].toLowerCase();
    }else{
        return ''
    }
}
//根据文件类型返回消息类型
Tool.getMsgType=(filename)=>{
    const type = Tool.getFileType(filename);
    if ('.jpg,.jpeg,.png,.bmp'.indexOf(type) > -1) {
        return 3
    }else{
        return 4
    }
}
Tool.getBase64Prefix = (oFileType)=>{
    let fileType = oFileType.toUpperCase()
    let prefix = ''
    switch (fileType) {
    case 'PNG':
        prefix = 'data:image/png;base64'
        break;
    case 'JPG':
    case 'JPEG':
        prefix = 'data:image/jpeg;base64'
        break;
    case 'MP4':
        prefix = 'data:video/mp4;base64'
        break;
    default:
        break;
    }
    return prefix
}
Tool.getFileName = (filename) => {
    var parts = filename.split('.');
    if(parts.length >1){
        parts.pop(); // 移除文件扩展名部分
        return parts.join('.'); // 将剩余部分合并为文件名
    }else{
        return parts[0]
    }

}
Tool.splitArray= (arr, size)=>{
    var result = [];
    for (var i = 0; i < arr.length; i += size) {
        result.push(arr.slice(i, i + size));
    }
    return result;
}
Tool.getAppClient = () => {
    const clientTypes = [
        'App',
        'Android',
        'IOS',
        'Cef',
        'UltraSoundMobile',
        'TEAir',
        'Browser',
        'PCBrowser',
        'MobileBrowser',
        'MiniProgram',
        'Huawei',
        'PCSafari',
        'Workstation'
    ];

    // 收集所有满足条件的客户端类型
    const matchedClients = clientTypes.filter(clientType => {
        return Tool.checkAppClient(clientType);
    });

    // 如果没有匹配的类型，返回包含 'Unknown' 的数组
    return matchedClients.length > 0 ? matchedClients : ['Unknown'];
};
Tool.checkAppClient = (clientName) => {
    const storeState = window.vm && window.vm.$store.state;
    const ua = navigator.userAgent
    if (!storeState) {
        return;
    }

    const {  systemConfig, device, globalParams } = storeState;
    const { client_type,clientType} = systemConfig;
    if ('App' === clientName) {
        if (client_type.AppMobile === clientType ||
          device.isUltraSoundMobile ||
          device.isTEAir
        ) {
            return true;
        }

    }else if ('Android' === clientName) {
        if (globalParams.osName === 'android'||device.isUltraSoundMobile) {
            return true;
        }

    } else if ('IOS' === clientName) {
        if (globalParams.osName === 'ios') {
            return true;
        }

    } else if ('Cef' === clientName) {
        if (globalParams.isCef) {
            return true;
        }

    }else if ('UltraSoundMobile' === clientName) {
        if (device.isUltraSoundMobile) {
            return true;
        }

    }else if ('TEAir' === clientName) {
        if (device.isTEAir) {
            return true;
        }

    }else if('PCBrowser' === clientName){
        if (window.clientType === 1 && window.browse_type.indexOf('BROWSER') > -1) {
            return true;
        }
    }else if('MobileBrowser' === clientName){
        if (window.clientType === 5 && window.browse_type.indexOf('BROWSER') > -1) {
            return true;
        }
    }else if('MiniProgram' === clientName){
        if (ua.includes('miniProgram') || ua.includes('MicroMessenger')) {
            return true;
        }
    } else if ('Browser' === clientName) {
        if (Tool.checkAppClient('PCBrowser') || Tool.checkAppClient('MobileBrowser') || window.browse_type === 'UNKNOW') {
            return true;
        }
    }else if('Huawei' === clientName){
        if (ua.includes('Huawei')) {
            return true;
        }
    }else if('PCSafari' === clientName){
        return /Safari/.test(ua) && !/Chrome/.test(ua) && !/Chromium/.test(ua) && /Macintosh/.test(ua);
    }else if('Workstation' === clientName){
        return Tool.ifAppWorkstationClientType(window.clientType)
    }
    return false;
};

Tool.copyToClipboard = async (content) => {
    // 辅助函数：从URL创建Image对象
    function createImageFromUrl(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.onload = () => resolve(img);
            img.onerror = (error) => reject(error);
            img.src = url;
        });
    }

    // 辅助函数：将图像转换为PNG格式的Blob
    async function convertToPngBlob(source) {
        if (!source) {
            return null;
        }

        try {
            // 统一处理图像来源，转换为Image对象
            let img = null;
            let needsRevoke = false;
            let imageUrl = null;

            if (source instanceof HTMLImageElement) {
                // 直接使用Image元素
                img = source;
            } else if (source instanceof Blob || source instanceof File) {
                // 如果已经是PNG类型的Blob，直接返回
                if (source.type === 'image/png') {
                    return source;
                }
                // 从Blob/File创建Image
                imageUrl = URL.createObjectURL(source);
                needsRevoke = true;
                img = await createImageFromUrl(imageUrl);
            } else if (typeof source === 'string' && (source.startsWith('http') || source.startsWith('data:'))) {
                // 从URL或Data URL创建Image
                try {
                    // 尝试使用fetch获取以避免跨域问题
                    const response = await fetch(source);
                    const blob = await response.blob();
                    if (blob.type === 'image/png') {
                        return blob;
                    }
                    imageUrl = URL.createObjectURL(blob);
                    needsRevoke = true;
                    img = await createImageFromUrl(imageUrl);
                } catch (e) {
                    // 如果fetch失败，直接尝试加载图像
                    console.warn("Fetch failed, trying direct image load:", e);
                    img = await createImageFromUrl(source);
                }
            } else {
                throw new Error("不支持的图像来源类型");
            }

            // 使用Canvas转换为PNG
            const canvas = document.createElement('canvas');
            canvas.width = img.naturalWidth || img.width;
            canvas.height = img.naturalHeight || img.height;

            const ctx = canvas.getContext('2d');
            ctx.drawImage(img, 0, 0);

            // 清理资源
            if (needsRevoke && imageUrl) {
                URL.revokeObjectURL(imageUrl);
            }

            // 返回PNG Blob
            return new Promise((resolve) => {
                canvas.toBlob(resolve, 'image/png');
            });
        } catch (error) {
            console.error("转换图像到PNG失败:", error);
            return null;
        }
    }

    // 判断是否为文本内容
    if (typeof content === 'string') {
        try {
            // 检查是否支持现代Clipboard API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                // 使用现代API写入剪贴板
                await navigator.clipboard.writeText(content);
                return true;
            } else {
                // 降级方案：使用旧的execCommand方式
                const tempInput = document.createElement("textarea");
                tempInput.value = content;
                document.body.appendChild(tempInput);
                tempInput.select();
                const success = document.execCommand("copy");
                document.body.removeChild(tempInput);
                return success;
            }
        } catch (error) {
            console.error("复制文本到剪贴板失败:", error);

            // 再次尝试使用旧方法作为降级方案
            try {
                const tempInput = document.createElement("textarea");
                tempInput.value = content;
                document.body.appendChild(tempInput);
                tempInput.select();
                const success = document.execCommand("copy");
                document.body.removeChild(tempInput);
                return success;
            } catch (fallbackError) {
                console.error("降级复制文本也失败:", fallbackError);
                return false;
            }
        }
    } else {   // 处理图片复制
        try {
            // 检查是否支持现代Clipboard API的write方法
            if (!navigator.clipboard || !navigator.clipboard.write) {
                throw new Error("当前浏览器不支持复制图片到剪贴板");
            }

            // 统一处理转换为PNG的Blob
            const pngBlob = await convertToPngBlob(content);

            // 如果无法获取有效的blob，抛出错误
            if (!pngBlob) {
                throw new Error("无效的图片数据");
            }

            // 使用ClipboardItem API复制图片
            if (typeof window.ClipboardItem === 'undefined') {
                throw new Error("当前浏览器不支持ClipboardItem API");
            }

            const clipboardItem = new window.ClipboardItem({
                [pngBlob.type]: pngBlob
            });

            await navigator.clipboard.write([clipboardItem]);
            return true;
        } catch (error) {
            console.error("复制图片到剪贴板失败:", error);
            return false;
        }
    }
}
Tool.checkMainScreenConnected = ()=>{
    if(window?.main_screen?.gateway?.check){
        return true
    }
    return false
}
Tool.checkConversationConnected = (cid)=>{
    if(Tool.checkMainScreenConnected()&&window?.main_screen?.conversation_list?.[cid]?.gateway?.check){
        return true
    }
    return false
}
Tool.getConnectedConversationList = ()=>{
    let list = []
    for(let cid in window?.main_screen?.conversation_list){
        if(Tool.checkConversationConnected(cid)){
            list.push(cid)
        }
    }
    return list
}
Tool.handleAfterConversationCreated = (cid) => {
    let isCreated = false;
    let timer = null;
    const interval = 50; // 定时器间隔 60ms
    const timeout = 20000; // 超时时间 20秒
    let elapsed = 0; // 已经过的时间

    const promise = new Promise((resolve, reject) => {
        timer = setInterval(() => {
            elapsed += interval;

            // 检查是否连接成功
            if (Tool.checkConversationConnected(cid)) {
                isCreated = true;
                clearInterval(timer); // 停止定时器
                timer = null;
                resolve(true); // 成功 resolve
            }

            // 超时处理
            if (elapsed >= timeout) {
                clearInterval(timer); // 停止定时器
                timer = null;
                if (!isCreated) {
                    reject(`Tool.handleAfterConversationCreated time out ${cid}`);
                }
            }
        }, interval);
    });

    return promise;
};
Tool.handleAfterMainScreenCreated = (ping = false) => {
    let isConnected = false;
    let timer = null;
    const interval = 50; // 定时器间隔 50ms
    const timeout = 20000; // 超时时间 20秒
    let elapsed = 0; // 已经过的时间

    const promise = new Promise((resolve, reject) => {
        if(!Tool.checkMainScreenConnected()){//如果一进来就是断网的，ping无用，直接不允许用ping
            ping = false
        }
        // 如果不需要主动 ping，或者本地缓存的连接状态已经连接，直接 resolve
        if (!ping) {
            if (Tool.checkMainScreenConnected()) {
                isConnected = true;
                resolve(true);
                return;
            }
            // 如果还未连接，则开始定时器检测连接状态
            if (!isConnected) {
                timer = setInterval(() => {
                    elapsed += interval;

                    // 检查连接状态
                    if (Tool.checkMainScreenConnected()) {
                        isConnected = true;
                        clearInterval(timer); // 停止定时器
                        resolve(true); // 连接成功
                    }

                    // 超时处理
                    if (elapsed >= timeout) {
                        clearInterval(timer); // 停止定时器
                        reject('Tool.handleMainScreenConnection timed out'); // 超时
                    }
                }, interval);
            }
        } else {
            // 如果需要 ping，先通过检查socket连接的方式确认状态
            window.main_screen.checkMainScreenSocket().then(() => {
                isConnected = true;
                resolve(true);
            }).catch((error) => {
                reject(error);
            });
        }


    });

    return promise;
};


Tool.getToken= ()=>{
    if(window.vm&&window.vm.$store.state.dynamicGlobalParams.token){
        return window.vm.$store.state.dynamicGlobalParams.token
    }else if(window.vm&&window.vm.$store.state.user.new_token){
        return window.vm.$store.state.user.new_token
    }else{
        return window.localStorage.getItem('loginToken')
    }
}
Tool.isInit=()=>{
    if(window.vm&&window.vm.$store.state.globalParams.init){
        return window.vm.$store.state.globalParams.init
    }else{
        return false
    }
}
Tool.transferLocationToCe = (str)=>{
    const isCE= process.env.VUE_APP_PROJECT_NOV==='CE';
    if (isCE) {
        str = str.replace('/pc/','/pc_ce/');
        str = str.replace('/mobile/','/mobile_ce/');
        str = str.replace('/activity/','/activity_ce/');
        str = str.replace('/whiteboard/','/whiteboard_ce/');
    }
    return str;
}
Tool.replaceHtmlTag = (text)=>{
    return text.replace(/<[^>]+>/g, '');
}
Tool.encodeURLPath = (url) => {
    var urlObj = new URL(url);

    return urlObj.href;
}
//交换节点里的子元素
Tool.swapChildren = (node1, node2)=> {
    const children1 = Array.from(node1.childNodes);
    const children2 = Array.from(node2.childNodes);

    children1.forEach(child => node1.removeChild(child));
    children2.forEach(child => node2.removeChild(child));

    children1.forEach(child => node2.appendChild(child));
    children2.forEach(child => node1.appendChild(child));
}

Tool.queryAppPermissions = (permission)=>{
    // const permissionMap = {
    //     'camera':'CAMERA',
    //     'microphone':'RECORD_AUDIO',
    //     'fileStorage':'WRITE_EXTERNAL_STORAGE',
    //     'location':'LOCATION'
    // }
    return new Promise(async(resolve,reject)=>{
        if(Tool.checkAppClient('Android')&&!Tool.checkAppClient('Browser')&&!Tool.checkAppClient('TEAir')){
            await Tool.createCWorkstationCommunicationMng({
                name: "queryAppPermissions",
                emitName: 'NotifyQueryAppPermissions',
                params:{permission},
                timeout:null
            }).then((res)=>{
                if(res.error_code === 0){
                    resolve(true)
                }else{
                    reject(`queryAppPermissions error:${res.error_msg}`)
                }

            })
        }else{
            resolve(true)
        }
    })
}

Tool.generateConsultationKeyForPcClient = (params={})=> {
    const serverInfo = window.vm.$store.state.systemConfig.serverInfo
    var json = {...params}
    if (serverInfo) {
        json.pa = serverInfo.pa
        json.pp = serverInfo.pp
        json.pu = serverInfo.pu
        json.pf = serverInfo.pf
        json.pd = serverInfo.pd
        json.ServerIP = serverInfo.ServerIP
        json.ServerPort = serverInfo.ServerPort
        json.ConsultationFileStorageType = serverInfo.consultation_file_storage_type
        json.ConsultationFileStorageOSSBucket = serverInfo.oss_consultation_file_storage_server.bucket
        json.ConsultationFileStorageOSSEndPoint = serverInfo.oss_consultation_file_storage_server.end_point
        json.ConsultationFileStorageOSSRecordSubDir = serverInfo.oss_consultation_file_storage_server.record_sub_dir
        json.soa = serverInfo.oss_consultation_file_storage_server.soa
        json.seo = serverInfo.oss_consultation_file_storage_server.seo
        json.vip = serverInfo.vip
    }
    return json
}
Tool.checkConversationConnect = (cid)=>{
    if(window.main_screen&&window.main_screen.conversation_list[cid]&&window.main_screen.conversation_list[cid].gateway.check){
        return true
    }else{
        return false
    }
}
Tool.getCurrentModuleName = ()=>{
    const pathname = window.location.pathname
    const moduleArr = ['ultrasync.html','ultrasync_pc.html','activity.html','whiteboard.html','audit.html']
    for(let i=0;i<moduleArr.length;i++){
        if(pathname.indexOf(moduleArr[i])>-1){
            return moduleArr[i].split('.')[0]
        }
    }
}
Tool.formatSize=(size)=> {
    if (size >= 1024 * 1024 * 1024) {
        return (size / (1024 * 1024 * 1024)).toFixed(2) + " GB";
    } else if (size >= 1024 * 1024) {
        return (size / (1024 * 1024)).toFixed(2) + " MB";
    } else if (size >= 1024) {
        return (size / 1024).toFixed(2) + " KB";
    } else {
        return size + " B";
    }
}

Tool.openLinkByDefaultBrowser=(url)=> {
    if(Tool.checkAppClient('Browser')){ //浏览器
        window.open(url, '_blank');
    }else{ //客户端
        window.CWorkstationCommunicationMng.openLinkByDefaultBrowser({url})
    }
}
Tool.downloadFileByBrowser=async (url, filename) => {
    const response = await fetch(url);
    const blob = await response.blob();
    const link = document.createElement("a");
    link.href = URL.createObjectURL(blob);
    link.download = filename;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
};
Tool.checkNetworkType = async()=>{
    return new Promise((resolve,reject)=>{
        Tool.createCWorkstationCommunicationMng({
            name: "getNetworkType",
            emitName: 'NotifyGetNetworkType',
            params:{},
            timeout:1000
        }).then((res)=>{
            if(res.error_code === 0){
                resolve(res.data.type)
            }else{
                reject(false)
            }

        })
    })
}
// 在需要暂时停止缓存的地方调用这个函数
Tool.stopServiceWorkerCaching = ()=> {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.getRegistrations().then(registrations => {
            registrations.forEach(registration => {
                registration.unregister().then(() => {
                    console.log('Service Worker unregistered temporarily.');
                });
            });
        });
    }
}

// 在需要恢复缓存的地方调用这个函数
Tool.resumeServiceWorkerCaching= ()=>  {
    if ('serviceWorker' in navigator) {
        navigator.serviceWorker.register('/service-worker.js').then(() => {
            console.log('Service Worker registered again.');
        });
    }
}
Tool.encodeSpecialChars = (url)=> { //替换掉当前允许上传的字符中，+ # &
    // 对 +、# 和 % 进行手动编码
    return url
        .replace(/\+/g, '_')
        .replace(/#/g, '_')
        .replace(/%/g, '_');
}
// 允许的字符：大小写字母，数字，下划线，点号，逗号，空格，加号，破折号（-），等号（=），圆括号（()），和符号（&），百分号（%），井号（#），方括号（[]），波浪号（~），单引号（'），反引号（`）
//中文字符，中文冒号，中文括号，中文中括号，中文书名号，中文感叹号，中文逗号，中文句号，中文问号
Tool.validateIllegalCharacters = (fileName) => {
    var pattern = /^[\u4e00-\u9fa5a-zA-Z0-9_.\s+\-=\(\)&%#\[\]~,'`!【】：《》（）！，。？]{1,255}$/;
    return pattern.test(fileName);
};
//乘法，解决浮点数问题
Tool.multiply = (num1,num2)=>{
    let result = (num1 * num2).toFixed(2);
    return result;
}
Tool.backToRoute = function(targetPath) {
    return new Promise((resolve, reject) => {
        let retries = 0;  // 记录重试次数
        const maxRetries = 10;  // 最大重试次数
        // 如果当前路径已经在index下，直接返回
        if (window.vm.$route.name === 'index') {
            if(Tool.checkMobileDialogShow()){ //如果有弹窗，直接关闭
                if(Tool.checkMobileCanCloseDialog()&&Tool.checkMobileCanCloseOnPopstate()){
                    Tool.closeMobileDialog()
                }
            }
            resolve(true);  // 不再继续回退，直接返回
            return;
        }
        if (window.vm.$route.path === targetPath) {
            resolve(true);  // 匹配成功，返回结果
            return
        }
        // 递归回退的函数
        async function tryBack() {
            if (retries >= maxRetries) {
                resolve(false);
                return;
            }

            // 执行回退操作
            await window.vm.$router.back();

            setTimeout(() => {
                // 检查路径是否匹配目标路径
                // 如果当前路径已经在/index下，直接返回
                if (window.vm.$route.name === 'index') {
                    if(Tool.checkMobileDialogShow()){ //如果有弹窗，直接关闭
                        if(Tool.checkMobileCanCloseDialog()&&Tool.checkMobileCanCloseOnPopstate()){
                            Tool.closeMobileDialog()
                        }
                    }
                    resolve(true);  // 不再继续回退，直接返回
                    return;
                }
                if (window.vm.$route.path === targetPath) {
                    resolve(true);  // 匹配成功，返回结果
                } else {
                    retries++;  // 增加重试次数
                    console.log(`回退第 ${retries} 次...`);

                    // 继续回退
                    tryBack();
                }
            }, 100);  // 使用0延时确保回退操作完成
        }

        // 初始调用递归回退函数
        tryBack();
    });
};
// 将秒数转换为时间格式（参照getDateDiff函数）
Tool.formatDurationFromSeconds = (seconds)=> {
    if (!seconds || seconds <= 0) {
        return `0${i18n.t('live_replay_second')}`;
    }
    const diffSecond = seconds % 60;
    const totalMinutes = Math.floor(seconds / 60);
    const diffMin = totalMinutes % 60;
    const allHours = Math.floor(totalMinutes / 60);

    let result = `${diffSecond}${i18n.t('live_replay_second')}`;

    if (diffMin > 0) {
        result = `${diffMin}${i18n.t('live_replay_minute')}${result}`;
    }

    if (allHours > 0) {
        result = `${allHours}${i18n.t('live_replay_hour')}${diffMin}${i18n.t('live_replay_minute')}`;
    }

    return result;
}



Tool.testPositiveInteger = (num) => {
    let result = /^[1-9]\d*$/.test(num);
    if (!result) {
        window.vm.$root.platformToast(i18n.t('enter_correct_number'),1)
    }
    return result;
}
Tool.replaceInternalNetworkEnvImageHost = (url = '') => { // 内网环境下，批量替换Img
    if (typeof url !== 'string' || !/^https?:\/\/.+/.test(url)||!url) {
        return url; // 如果 URL 无效，直接返回原始 URL
    }

    const storageReplaceInfo = window.vm.$store.state.systemConfig.serverInfo.storageReplaceInfo;
    const isReplace = storageReplaceInfo.replace;
    const nginx_address = storageReplaceInfo.nginx_address;
    if(!isReplace || !nginx_address){
        return url
    }
    const storage_address = new URL(storageReplaceInfo.storage_address);
    const avatar_port = storageReplaceInfo.avatar_port;
    const staticUrl = 'static/';
    const avatarUrl = 'CustomPortrait/'
    if (url.indexOf(staticUrl) === -1) {
        try {
            const parsedUrl = new URL(url);

            // 比较协议和主机部分（不包含端口号）
            if (parsedUrl.protocol === storage_address.protocol && parsedUrl.hostname === storage_address.hostname) {
                const containsAvatarParam = url.indexOf(avatarUrl)>-1;
                let newUrl = nginx_address + parsedUrl.pathname + parsedUrl.search + parsedUrl.hash;

                // 如果包含 'avatar=1' 参数，设置端口为 avatar_port
                if (containsAvatarParam) {
                    const newParsedUrl = new URL(newUrl);
                    newParsedUrl.port = avatar_port;
                    newUrl = newParsedUrl.toString();
                }

                return newUrl;
            }
        } catch (e) {
            console.log('Invalid URL:', url, e);
        }
    }
    return url; // 如果不需要替换或解析 URL 出错，返回原始 URL
}
Tool.getAgoraProxyInfo = ()=>{
    const systemConfig = window.vm.$store.state.systemConfig
    const storageReplaceInfo = systemConfig.serverInfo.storageReplaceInfo;
    const isReplace = storageReplaceInfo.replace;
    const network_environment = systemConfig.serverInfo.network_environment
    let localAgoraIP =  storageReplaceInfo.agora_ip;
    let tls_domain = storageReplaceInfo.tls_domain;
    let ca_domain = storageReplaceInfo.ca_domain
    return{
        isLocal:network_environment === 1&&!isReplace,
        localAgoraIP,
        tls_domain,
        ca_domain
    }
}
Tool.initNativeAgoraSdk = (appId)=>{
    return new Promise((resolve,reject)=>{
        Tool.createCWorkstationCommunicationMng({
            name: "initNativeAgoraSdk",
            emitName: "NotifyInitNativeAgoraSdk",
            params: {
                appId,
                networkLocation:Tool.getAgoraProxyInfo()
            },
            timeout: 5500,
        }).then(() => {
            resolve(true)
        }).catch(()=>{
            reject(false)
        });
    })

}
Tool.observeImageLoad = (fallbackImageUrl)=>{
    // 为 img 和 video 元素添加错误事件处理程序
    function addErrorHandler(media) {
        // 设置初始重试状态
        media.dataset.retry = 'original';
        media.addEventListener('error', function handleError() {
            const newSrc =Tool.replaceInternalNetworkEnvImageHost(this.src) ;
            if(media.src.includes(fallbackImageUrl)  || newSrc === this.src){
                return
            }
            try {
                if (this.dataset.retry === 'original') {
                    // 组合新的 URL
                    this.src = newSrc;
                    if(this.tagName === 'IMG'){
                        this.dataset.retry = 'fallback';
                    }else{
                        this.dataset.retry = 'final';
                        this.play().catch(error => console.error('Failed to play video:', error));
                    }

                } else if (this.dataset.retry === 'fallback') {
                    // 第二次失败，使用默认资源
                    if (this.tagName === 'IMG') {
                        this.src = fallbackImageUrl;
                    }
                    this.dataset.retry = 'final';
                } else {
                    // 第三次失败，移除监听器
                    this.removeEventListener('error', handleError);
                }
            } catch (e) {
                console.error(e);
                this.removeEventListener('error', handleError);
            }
        });
        const newSrc =Tool.replaceInternalNetworkEnvImageHost(media.src);
        if(newSrc){
            media.src = newSrc
        }

    }

    // 监听现有的 img 和 video 元素
    document.querySelectorAll('img, video').forEach((media) => {
        addErrorHandler(media);
    });

    // 使用 MutationObserver 监听新添加的 img 和 video 元素
    const observer = new MutationObserver((mutationsList) => {
        mutationsList.forEach((mutation) => {
            if (mutation.type === 'childList') {
                mutation.addedNodes.forEach((node) => {
                    if (node.tagName === 'IMG' || node.tagName === 'VIDEO') {
                        addErrorHandler(node);
                    } else if (node.nodeType === Node.ELEMENT_NODE) {
                        node.querySelectorAll('img, video').forEach((media) => {
                            addErrorHandler(media);
                        });
                    }
                });
            }
        });
    });

    // 配置 MutationObserver 监听所有子节点的添加
    const config = {
        childList: true,
        subtree: true
    };

    // 开始观察整个文档
    observer.observe(document.body, config);

}
Tool.checkIsElementVisible = (element, fullyVisible = false) => {
    if (!element) {
        throw new Error("Element is not provided or does not exist.");
    }

    // 提前计算视口尺寸
    const viewportHeight = window.innerHeight || document.documentElement.clientHeight;
    const viewportWidth = window.innerWidth || document.documentElement.clientWidth;
    const rect = element.getBoundingClientRect();

    // 根据参数选择不同的可见性判断逻辑
    const isVisibleInViewport = fullyVisible ? (
        // 完全可见模式：元素必须完整出现在视口中
        rect.top >= 0 &&
        rect.left >= 0 &&
        rect.bottom <= viewportHeight &&
        rect.right <= viewportWidth
    ) : (
        // 部分可见模式：元素任意部分出现在视口中
        rect.top < viewportHeight &&
        rect.bottom > 0 &&
        rect.left < viewportWidth &&
        rect.right > 0
    );

    // 检查元素的物理可见性（尺寸 + 样式）
    const hasValidDimensions = rect.width > 0 && rect.height > 0;
    const isStyleVisible = (elem) => {
        return !!(elem.offsetWidth || elem.offsetHeight || elem.getClientRects().length) &&
               window.getComputedStyle(elem).visibility !== 'hidden';
    };

    // 遍历祖先链检查可见性
    let currentElement = element;
    while (currentElement) {
        if (!isStyleVisible(currentElement)) {
            return false;
        }
        currentElement = currentElement.parentElement;
    }

    // 检查元素是否在滚动容器内可见
    const isInsideScrollableContainer = (elem) => {
        let currentElem = elem;
        while (currentElem) {
            // 判断当前元素是否是一个有滚动条的容器
            const overflowY = window.getComputedStyle(currentElem).overflowY;
            if (overflowY === 'auto' || overflowY === 'scroll') {
                const containerRect = currentElem.getBoundingClientRect();
                // 如果元素顶部位置已超出容器的可视区域，则不可见
                if (rect.top < containerRect.top || rect.bottom > containerRect.bottom) {
                    return false;
                }
            }
            currentElem = currentElem.parentElement;
        }
        return true; // 元素位于滚动容器内并且可见
    };

    // 返回是否符合可见性要求（同时考虑视口和滚动容器）
    return isVisibleInViewport && hasValidDimensions && isInsideScrollableContainer(element);
}

Tool.forceLeaveChannel = ()=>{
    window.CWorkstationCommunicationMng.forceLeaveChannelAux({});
    console.error('force leave channel')
}
// 保存之前的监听器，并在新实例上重新绑定所有监听器
Tool.rebindEvents = (oldInstance, newInstance)=>{
    if (!oldInstance || !newInstance) {
        return;
    }
    const events = oldInstance.event.eventNames(); // 获取所有事件名称
    events.forEach(event => {
        const listeners = oldInstance.event.listeners(event); // 获取所有监听器
        listeners.forEach(listener => {
            oldInstance.event.off(event, listener); // 解绑旧实例的监听器
            newInstance.event.on(event, listener); // 在新实例上重新绑定监听器
        });
    });
    // 清理旧实例
    oldInstance.event.offAll();
    oldInstance = null;
}
Tool.checkHttpsConnection = (address)=> {
    return new Promise(async(resolve,reject)=>{
        try {
            const response = await fetch(address, {
                method: 'HEAD',
                mode: 'no-cors'
            });
            // 如果 fetch 成功返回，这意味着连接可访问
            if (response.ok || response.type === 'opaque') {
                // 连接 WebSocket 或执行其他操作
                resolve(true)
            } else {
                // 如果不成功，跳转到同意页面
                resolve(false)
            }
        } catch (error) {
            console.error(error)
            // 捕获错误并跳转到同意页面
            resolve(false)
        }
    })
}
Tool.getAllVideoIds = (parentId)=> {
    // 获取父元素
    const parentElement = document.getElementById(parentId);

    // 如果父元素不存在，返回空数组
    if (!parentElement) {
        console.warn('Parent element not found');
        return [];
    }

    // 查找父元素下的所有 video 标签
    const videoElements = parentElement.getElementsByTagName('video');

    // 提取每个 video 标签的 id，并存入数组
    const videoIds = Array.from(videoElements)
        .map(video => video.id)
        .filter(id => id); // 过滤掉没有 id 的视频标签

    return videoIds;
}
Tool.deepReactive = (obj, onChangeCallback, currentPath = []) => {
    // 如果 obj 不是对象，直接返回
    if (typeof obj !== 'object' || obj === null) {
        return obj;
    }

    // 创建代理
    return new Proxy(obj, {
        get(target, key, receiver) {
            const value = Reflect.get(target, key, receiver);

            // 如果子属性也是对象或数组，则递归代理，同时更新路径
            if (typeof value === 'object' && value !== null) {
                return Tool.deepReactive(value, onChangeCallback, currentPath.concat(key));
            }

            return value;
        },
        set(target, key, value, receiver) {
            const oldValue = target[key];
            const result = Reflect.set(target, key, value, receiver);

            // 检查值是否真正发生变化，避免重复触发
            if (oldValue !== value) {
                // 调用回调函数，传递包括当前路径的信息
                onChangeCallback(target, key, value, 'set', currentPath.concat(key));
            }

            return result;
        },
        deleteProperty(target, key) {
            const hadKey = Object.prototype.hasOwnProperty.call(target, key);
            const result = Reflect.deleteProperty(target, key);

            // 仅当属性存在时才调用回调
            if (result && hadKey) {
                // 调用回调函数，传递包括当前路径的信息
                onChangeCallback(target, key, undefined, 'delete', currentPath.concat(key));
            }

            return result;
        }
    });
};


Tool.deepMerge = (target, source) => {
    // 删除 `target` 中不存在于 `source` 的属性
    for (const key in target) {
        if (target.hasOwnProperty(key) && !source.hasOwnProperty(key)) {
            Vue.delete(target, key);
        }
    }

    // 合并 `source` 到 `target`
    for (const key in source) {
        if (source.hasOwnProperty(key)) {
            if (Array.isArray(source[key])) {
                // 如果是数组，直接替换
                Vue.set(target, key, source[key].slice());
            } else if (typeof source[key] === 'object' && source[key] !== null) {
                if (!target[key] || typeof target[key] !== 'object') {
                    Vue.set(target, key, {});
                }
                Tool.deepMerge(target[key], source[key]);
            } else {
                // 如果是基础类型，直接替换
                Vue.set(target, key, source[key]);
            }
        }
    }
};
Tool.waitForElementCreated = (selector, timeout = 3000)=> {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();

        // 创建一个定时器，监控超时
        const timer = setInterval(() => {
            const element = document.querySelector(selector);

            // 如果找到元素，则清除定时器并解析Promise
            if (element) {
                clearInterval(timer);
                resolve(element);
            }

            // 如果超时，则拒绝Promise
            if (Date.now() - startTime > timeout) {
                clearInterval(timer);
                reject(new Error(`Element with selector "${selector}" not found within ${timeout}ms`));
            }
        }, 100); // 每100ms检查一次
    });
}
Tool.isDeepReactive = (obj)=> {
    // 如果对象不存在或者不是对象类型，直接返回 false
    if (obj === null || typeof obj !== 'object') {
        return false;
    }

    // 检查当前对象是否有响应式标志 __ob__
    if (!obj.__ob__) {
        console.warn('Object is not reactive:', obj);
        return false;
    }

    // 递归检查每一级属性是否也是响应式
    for (const key in obj) {
        if (obj.hasOwnProperty(key)) {
            const value = obj[key];

            // 如果子属性是对象，递归检查
            if (typeof value === 'object' && value !== null) {
                const isReactive = Tool.isDeepReactive(value);
                if (!isReactive) {
                    console.warn(`Property "${key}" is not reactive:`, value);
                    return false;
                }
            }
        }
    }

    // 如果当前对象及其所有子属性都通过检查，返回 true
    return true;
}
Tool.getBrowserUniqueId = ()=> {
    const uniqueIdKey = 'browser_unique_id';

    // 检查 localStorage 是否已经存储了唯一标识符
    let uniqueId = localStorage.getItem(uniqueIdKey);

    if (!uniqueId) {
        // 如果没有，生成一个新的唯一标识符
        uniqueId = 'browser-' + Math.random().toString(36).substr(2, 9);

        // 将唯一标识符存储在 localStorage 中，以便下次访问时可以重用
        localStorage.setItem(uniqueIdKey, uniqueId);
    }

    return uniqueId;
}
Tool.getCameraDefaultSetting = ()=>{
    const cameraDefaultSettingKey = 'camera_default_setting';
    const savedSetting = localStorage.getItem(cameraDefaultSettingKey);
    return savedSetting === 'true' ? true : false;
}
Tool.setCameraDefaultSetting = (setting)=>{
    const cameraDefaultSettingKey = 'camera_default_setting';
    localStorage.setItem(cameraDefaultSettingKey, setting);
}
Tool.getConsultationMode = ()=>{
    const consultationModeKey = 'consultation_mode';
    const savedMode = localStorage.getItem(consultationModeKey);
    // 返回 0: 普通模式, 1: 会诊模式，默认为普通模式
    return savedMode === '1' ? 1 : 0;
}
Tool.setConsultationMode = (mode)=>{
    const consultationModeKey = 'consultation_mode';
    localStorage.setItem(consultationModeKey, mode.toString());
}
Tool.closeElementMessageBoxByClass = (className)=>{
    // 获取指定 class 的弹窗
    const dialog = document.querySelector(`.${className}`);

    if (dialog) {
        // 获取弹窗的关闭按钮
        const closeButton = dialog.querySelector('.el-message-box__close');

        if (closeButton) {
            // 模拟点击关闭按钮
            closeButton.click();
        } else {
            console.error(`无法找到关闭按钮，弹窗可能没有关闭按钮。`);
        }
    } else {
        console.error(`没有找到 class 为 ${className} 的弹窗元素。`);
    }
}
Tool.isObject = (value)=>{
    return Object.prototype.toString.call(value) === '[object Object]'
}
Tool.isObjectError = (value)=>{
    return Object.prototype.toString.call(value) === '[object Error]'
}
Tool.getHostConfig = ()=>{
    return hostConfig
}
Tool.isDev = ()=>{
    // 开发环境：优先使用配置判断，生产环境使用域名判断
    if (process.env.NODE_ENV === 'development') {
        // 开发环境：使用配置判断
        return Tool.isCEDev() || proxyConfig.env.indexOf(hostConfig.dev) > -1
    } else {
        // 生产环境：使用域名判断
        return Tool.isCEDev() ||
               window.location.href.indexOf(hostConfig.dev) > -1 ||
               window.location.href.indexOf('consult-dev.mindray.com') > -1 ||
               window.location.href.indexOf('dev.huangweilong.com') > -1
    }
}
Tool.isBeta = ()=>{
    // 开发环境：优先使用配置判断，生产环境使用域名判断
    if (process.env.NODE_ENV === 'development') {
        // 开发环境：使用配置判断
        return proxyConfig.env.indexOf(hostConfig.beta) > -1
    } else {
        // 生产环境：使用域名判断
        return window.location.href.indexOf(hostConfig.beta) > -1 ||
               window.location.href.indexOf('consult-beta.mindray.com') > -1||
               window.location.href.indexOf('beta.huangweilong.com') > -1
    }
}
Tool.isCustomEnv = ()=>{
    if (process.env.NODE_ENV === 'development') {
        // 开发环境：使用配置判断
        return proxyConfig.env.indexOf('192.168.0') > -1
    }
    return false
}
Tool.isCEDev = ()=>{
    // 开发环境：优先使用配置判断，生产环境使用域名判断
    if (process.env.NODE_ENV === 'development') {
        // 开发环境：使用配置判断
        return proxyConfig.env.indexOf(hostConfig.ce_dev) > -1
    } else {
        // 生产环境：使用域名判断
        return window.location.href.indexOf(hostConfig.ce_dev) > -1 ||
               window.location.href.indexOf('micoplus-dev.mindray.com') > -1||
               window.location.href.indexOf('cedev.huangweilong.com') > -1
    }
}

Tool.getSupportVideoType = ()=>{
    const supportFileType = window.vm.$store.state.systemConfig.serverInfo.SupportFileType;
    const commonVideoTypes = ['mp4', 'avi', 'mov', 'wmv', 'flv', 'mkv', 'webm', '3gp', 'mpeg', 'mpg', 'm4v'];
    return commonVideoTypes.filter(type => supportFileType.includes(type.toLowerCase()));
}

Tool.getSupportImageType = ()=>{
    const supportFileType = window.vm.$store.state.systemConfig.serverInfo.SupportFileType;
    const commonImageTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp', 'svg', 'tiff', 'tif', 'ico', 'heic', 'dcm'];
    return commonImageTypes.filter(type => supportFileType.includes(type.toLowerCase()));
}
Tool.loadModuleRouter = async (router,type='push')=>{
    let loadingInstance = null
    let loadingTimer = null
    if(Tool.checkMainScreenConnected()){
        // 如果路由已加载过，直接跳转无需loading
        if(loadedRouters.has(router)){
            if(type === 'push'){
                await window.vm.$router.push(router);
            }else{
                await window.vm.$router.replace(router);
            }
            return;
        }
        // 判断客户端类型，选择合适的加载提示组件
        const isMobileClient = Tool.checkAppClient('App') || Tool.checkAppClient('MobileBrowser')
        try {
            // 移动端使用 vant Toast
            if (isMobileClient) {
                Toast.loading({
                    message: `${i18n.t('loading_module_text')}`,
                    forbidClick: true,
                    duration: 0
                });
            }else {
                loadingInstance = Loading.service({
                    text: i18n.t('loading_module_text'),
                    background: 'rgba(255, 255, 255, 0.8)'
                });
            }
            loadingTimer = setTimeout(()=>{
                if (isMobileClient){
                    Toast.clear();
                }else if (loadingInstance) {
                    loadingInstance.close();
                    loadingInstance = null;
                }
            },6000)
            if(type === 'push'){
                await window.vm.$router.push(router);
            }else{
                await window.vm.$router.replace(router);
            }
            // 加载成功后，将路由添加到已加载集合中
            loadedRouters.add(router);
        } finally {
            // 清除加载提示
            if (isMobileClient){
                Toast.clear();
            }else if (loadingInstance) {
                loadingInstance.close();
                loadingInstance = null;
            }
            clearTimeout(loadingTimer);
        }
    }else{
        window.vm.$root.platformToast(i18n.t('network_error_tip'))
    }
};
Tool.safeStringify = (obj) => {
    const seen = new WeakSet();

    const inspect = (value, depth = 0) => {
        if (depth > 6) {
            return '[Max Depth]';
        }

        if (value === null) {
            return 'null';
        }
        if (value === undefined) {
            return 'undefined';
        }

        const type = typeof value;

        if (type === 'string') {
            return `"${value}"`;
        }
        if (type === 'number' || type === 'boolean') {
            return String(value);
        }
        if (type === 'function') {
            return '[Function]';
        }
        if (type === 'symbol') {
            return value.toString();
        }

        if (type === 'object') {
            if (seen.has(value)) {
                return '[Circular]';
            }
            seen.add(value);

            if (value._isVue) {
                return '[Vue Instance]';
            }
            if (value instanceof Error) {
                return `[Error: ${value.message}]`;
            }
            if (value instanceof Element) {
                return '[DOM Element]';
            }
            if (value === window) {
                return '[Window]';
            }
            if (value.$router) {
                return '[Vue Router]';
            }

            if (Array.isArray(value)) {
                return `[${value.map(v => inspect(v, depth + 1)).join(', ')}]`;
            }

            const entries = Object.entries(value)
                .map(([k, v]) => `"${k}": ${inspect(v, depth + 1)}`)
                .join(', ');
            return `{${entries}}`;
        }

        return String(value);
    };

    try {
        return inspect(obj);
    } catch (error) {
        return `[Error: ${error.message}]`;
    }
};
/**
 * 从给定对象中提取数字类型的key及其对应的value，返回一个新的对象
 * @param {Object} obj - 源对象
 * @returns {Object} 包含数字key的新对象
 */
Tool.extractNumericKeys = (obj) => {
    const numericKeyObj = {};

    for (const [key, value] of Object.entries(obj)) {
        // 检查key是否为数字类型（包括数字字符串）
        if (!isNaN(key) && !isNaN(parseFloat(key))) {
            numericKeyObj[key] = value;
        }
    }

    return numericKeyObj;
}


Tool.translateAiReportDesc = (obj) => {
    let viewReason = []
    if (obj && obj.view_desc_key && obj.view_desc_key.length>0){
        for (let v of obj.view_desc_key){
            if(i18n && i18n.te(v)){
                // 使用i18n的te方法检查翻译键是否存在，使用t方法获取翻译
                viewReason.push(i18n.t(v))
            }
        }
    }
    if(viewReason.length === 0 && obj.view_desc && obj.view_desc.length>0) {
        viewReason = obj.view_desc.map(v => i18n.te(v) ? i18n.t(v) : v)
    }
    if(viewReason.length === 0){
        viewReason = ['--']
    }
    return viewReason.join(", ");

}
Tool.capitalizeFirstLetter = (str) => {
    if (!str) {
        return str;
    } // 处理空字符串或非字符串情况
    return str.charAt(0).toUpperCase() + str.slice(1);
}
window.Tool = Tool
export default Tool
