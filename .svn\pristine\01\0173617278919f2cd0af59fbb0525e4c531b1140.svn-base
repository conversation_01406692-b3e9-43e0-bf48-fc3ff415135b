import Tool from "@/common/tool.js";
import { cloneDeep } from "lodash";
import CEvent from "@/common/CEvent";
import Plyr from 'plyr';
import CReverseControl from "@/common/CLiveConferenceWeb/CReverseControlWeb";
import {Logger} from '@/common/console.js'
import i18n from '@/common/i18n'
let storeState = null;
let Toast;
let AgoraRTC = null;

setTimeout(() => {
    storeState = window.vm.$store.state;
    Toast = window.vm.$root.platformToast;
});
const LOCAL_TRACK_ERROR  = {
    NOT_SUPPORTED:'NOT_SUPPORTED',
    MEDIA_OPTION_INVALID:'MEDIA_OPTION_INVALID',
    DEVICE_NOT_FOUND:'DEVICE_NOT_FOUND',
    PERMISSION_DENIED:'PERMISSION_DENIED',
    CONSTRAINT_NOT_SATISFIED:'CONSTRAINT_NOT_SATISFIED',
    SHARE_AUDIO_NOT_ALLOWED:'SHARE_AUDIO_NOT_ALLOWED',
    NOT_READABLE:'NOT_READABLE',
    OTHERS:'OTHERS'
}

class liveLogger {
    constructor() {
        this.log = function ({ message, data }) {
            Logger.save({
                message,
                eventType: `live_log`,
                data
            });
        };
        this.error = function ({ message, data }) {
            Logger.saveError({
                message,
                eventType: `live_error`,
                data
            });
        };
    }
}
const logger = new liveLogger();
class CLiveRoomWeb {
    constructor(option) {
        this.ultrasync_uid = storeState.user.uid; //云++id
        this.is_single_chat = window.main_screen.conversation_list[option.cid].is_single_chat;
        this.event = new CEvent();
        this.agoraClient = null;
        this.agoraClientMain = null
        this.main_dom = option.main_dom;
        this.aux_dom_list = option.aux_dom_list||[];
        this.cid = option.cid;
        this.lostConnectAndOpenConfirmDialogTimer = null; //失去连接并弹窗延时器
        this.lostConnectAndLeaveChannelTimer = null; //失去连接并退出直播延时器
        this.lostConnectAndLeaveChannelTimer2 = null; //弹窗后无操作等待延时器
        this.maxVideoNum = 3//最大小视频路数量
        this.remotePublishedUser = {
            //远程发布了音视频的用户合集
        };
        this.volumeAnimation = null;
        this.microphonesList = []
        this.camerasList = []
        this.localTracks = {
            videoTrack: null,
            audioTrack: null,
            screenVideoTrack:null,
            screenAudioTrack:null,
        };
        // 专用于测试的轨道，不会被发布到直播间
        this.testTracks = {
            videoTrack: null,
            audioTrack: null,
        };
        this.playerObj={}//当前订阅辅流的播放器
        this.isJoinMainToShareScreen = false
        // 记录正在进行中的辅流订阅，防止并发导致重复订阅
        this.pendingSubscribeAux = new Set();
        this.data = {
            FreeSpeakLimitUserCount: storeState.systemConfig.serverInfo.voice_config.FreeSpeakLimitUserCount || 10, //可最多支持开麦人数
            isSender: 0, //是否发起身份
            joiningMain: false, //主流正在加入房间
            joiningAux: false, //辅流正在加入房间
            joinedMain: false, //本地主流是否在房间中
            joinedAux: false, //本地辅流是否在房间中
            channelId: 0, //当前房间号
            channelUUId: 0, //当前房间唯一编码
            localAuxUid: 0, //本地申请的辅流Uid
            localMainUid: 0, //本地申请的主流Uid
            currentMainUid: 0, //当前房间内应该去订阅的主流uid
            currentMainUltrasyncId: 0, //当前播放主流的云++用户id
            currentSubscribeMain: 0, //当前正在订阅的远端主流
            currentSubscribeAux: [], //当前正在订阅的远端辅流
            roomUserMap: {}, //所有成员uid map信息表
            from: "", //启动直播的业务场景/chat_window会话 live_management预约直播 tv_wall电视墙
            isRecording: false, //是否正在录制
            isLocalControl:false,//是否本地开启的远程控制
            losing_connect_server: false, //是否正在失去和自己服务器的连接
            lost_connect_server: false, //已失去连接
            hasWhiteBoard: false, //该房间内是否存在白板
            is_open_white_board: false, //是否打开了白板
            whiteBoardInfo: {}, //白板房间信息
            localVideoStream: -1,
            localAudioStream: -1,
            localScreenStream:0,
            isHost: 0, //是否主讲人
            isLastLocalMain: false, // 断网前本地是否推主流
            memberLength: 0, //成员人数
            cloud_record_auth: 0, //是否可以操作云录制功能
            currentTime: new Date().getTime(),
            living_time: 0, //直播的持续时间
            hasCameraDevice: false, //设备是否具具有摄像头
            hasMicroPhoneDevice: false, //设备是否具有麦克风
            currentLockAuxUid: 0, //当前服务器要求锁定观看的UID
            currentVideoList:[],//当前群内的摄像头开启顺序列表
            serviceKnowSelfJoinRoom:false,//服务器是否已经知道该用户加入了群聊
            showAuxVideoDomList:[],//当前正在播放的dom id列表
            isMutingLocalVideoStream:false,
            isMutingLocalAudioStream:false,
            isMutingShareScreen:false,
            currentVideoTrackId:null,
            currentAudioTrackId:null,
            remoteControlData:{
                linkToDopplerStatus:0,
                serviceRemoteControlStatus: 0,//远程调参状态
                isLocalControl:false,
                nickname:''
            }
        };
        this.CReverseControl = null;
        if (!window.CReverseControl) {
            window.CReverseControl = {};
        }
        window.CReverseControl[this.cid] = null;
    }

    async initAgoraRTC() {
        //创建本地客户端RTC
        if(this.agoraClient){
            return
        }
        // 确保AgoraRTC已加载
        if(!AgoraRTC){
            try {
                AgoraRTC = await import(/* webpackPrefetch: true */ "agora-rtc-sdk-ng").then(module => module.default);
                // 初始化AgoraRTC相关配置
                AgoraRTC.enableLogUpload();
                AgoraRTC.setLogLevel(0);
                this.initAgoraListeners();
            } catch (error) {
                logger.error({message:'Failed to load AgoraRTC',data:error})
                throw new Error('Failed to initialize AgoraRTC');
            }
        }

        this.agoraClient = AgoraRTC.createClient({ mode: "live", codec: "vp8" });
        this.agoraClient.setClientRole("host");

        const {isLocal,localAgoraIP} = Tool.getAgoraProxyInfo()
        if(isLocal){
            AgoraRTC.setParameter("JOIN_WITH_FALLBACK_SIGNAL_PROXY", false);
            AgoraRTC.setParameter("JOIN_WITH_FALLBACK_MEDIA_PROXY", false);
            AgoraRTC.setParameter('CONNECT_GATEWAY_WITHOUT_DOMAIN',true)
            AgoraRTC.setParameter("WEBCS_DOMAIN",[`${localAgoraIP}`]);
            AgoraRTC.setParameter("EVENT_REPORT_DOMAIN",`${localAgoraIP}:6443`);
            AgoraRTC.setParameter("LOG_UPLOAD_SERVER",[`${localAgoraIP}:6444`]);
        }

        this.agoraClient.on("user-published", (user, mediaType) => {
            this.handleUserPublished(user, mediaType);
        });
        this.agoraClient.on("user-unpublished", (user, mediaType) => {
            this.handleUserUnPublished(user, mediaType);
        });
    }

    // 将AgoraRTC事件监听器单独提取为一个方法
    initAgoraListeners() {
        AgoraRTC.onMicrophoneChanged = async (changedDevice) => {
            console.info('onMicrophoneChanged',changedDevice)
            if(changedDevice.state === "ACTIVE"){
                this.getMicrophonesList({showErrorTips:false})
            }
        };

        AgoraRTC.onCameraChanged = async (changedDevice) => {
            console.info('onCameraChanged',changedDevice)
            if (changedDevice.state === "ACTIVE") {
                this.getCamerasList({showErrorTips:false})
            }
        };
    }

    async initAgoraRTCMain(){
        //创建本地客户端推主流的RTC
        if(this.agoraClientMain){
            return
        }
        // 确保AgoraRTC已加载
        if(!AgoraRTC){
            try {
                AgoraRTC = await import(/* webpackPrefetch: true */ "agora-rtc-sdk-ng").then(module => module.default);
            } catch (error) {
                logger.error({message:'Failed to load AgoraRTC',data:error})
                throw new Error('Failed to initialize AgoraRTC');
            }
        }

        this.agoraClientMain = AgoraRTC.createClient({ mode: "live", codec: "vp8" });
        this.agoraClientMain.setClientRole("host");
    }

    // 修改JoinChannelAux方法
    async JoinChannelAux(odata) {
        const ultrasync_uid = storeState.user.uid;
        return new Promise(async (resolve, reject) => {
            try {
                // 确保AgoraRTC已加载并初始化
                await this.initAgoraRTC();
                if (!this.checkAgoraSystemRequirements()) {
                    Toast(i18n.t('no_support_browser_live'));
                    return reject(i18n.t('no_support_browser_live'));
                }
                if (this.data.joiningAux) {
                    Toast(i18n.t('processing_wait'));
                    return reject(i18n.t('processing_wait'));
                }
                this.clearStatus()
                const {isLocal,localAgoraIP} = Tool.getAgoraProxyInfo()
                if(isLocal){
                    const res = await Tool.checkHttpsConnection(`https://${localAgoraIP}`)
                    if(!res){
                        Tool.openCommonDialog({
                            buttons: [i18n.t('confirm_txt')],
                            message: i18n.t('certificate_agree_tips'),
                            confirm:()=>{
                                window.open(`https://${localAgoraIP}`, '_blank');
                            }
                        });
                        return reject('localAgoraIP disconnect')
                    }
                }
                this.data.joiningAux = true;
                this.data.channelId = odata.channelId;

                this.data.isSender = odata.isSender;
                this.data.isHost = odata.isSender; //默认认为发起者也是主讲人
                if (storeState.liveConference[this.cid] && storeState.liveConference[this.cid].senderUserId) {
                    this.data.isHost = storeState.liveConference[this.cid].senderUserId === ultrasync_uid ? 1 : 0;
                }
                this.data.from = odata.from;
                window.vm.$root.currentLiveCid = this.cid;
                let token = odata.token
                try {
                    await this.agoraClient.join(odata.appId, odata.channelId,token,odata.uid);
                    this.data.joinedAux = true;
                    this.data.joiningAux = false;
                    this.clearLostConnectTimer();
                    this.data.lost_connect_server = false;
                    let data = odata;
                    data.isOnline = 1;
                    data.user_id = ultrasync_uid;
                    data.isHost = this.data.isHost;
                    this.data.localAuxUid = odata.uid;
                    this.ServiceReportJoinChannel({
                        uid:data.uid,
                        status:1
                    })
                    if (Tool.checkAppClient('PCBrowser')) {
                        this.createCReverseControl();
                    }

                    this.event.emit('HandleNotifyJoinChannelAux')
                    try {
                        await this.checkSelfJoinRoomByService()
                        const channelDetail = await this.ServiceGetChannelCurrentStatus();
                        logger.log({message:'channelDetail',data:channelDetail})
                        this.data.channelUUId = channelDetail.uuid;
                        this.data.isHost = channelDetail.host_user_id === ultrasync_uid ? 1 : 0;
                        this.data.cloud_record_auth = this.checkCloudRecordAuth();
                        this.data.isRecording = channelDetail.recording_status;
                        this.data.remoteControlData.serviceRemoteControlStatus = channelDetail.remoteControlStatus;
                        this.data.remoteControlData.isLocalControl =
                        channelDetail.remote_control_pc_uid === this.ultrasync_uid;
                        this.data.living_time = channelDetail.living_time;
                        this.data.currentVideoList = channelDetail.video_agora_uid_list;
                        if(channelDetail.living_time === 0){
                            this.data.living_time = 0.1
                        }
                        this.data.currentLockAuxUid = channelDetail.auxStreamUid;
                        if (channelDetail.whiteboard_status) {
                            //是否开启白板
                            this.openWhiteBoard()
                        }
                        resolve(true);
                        await this.getCurrentUserList();
                        await this.judgeIfNeedMuteLocalStreamAfterJoinChannelAux(data);
                        console.error('join channel aux success')

                        this.checkSubscribeAux();

                    } catch (error) {
                        logger.error({message:'Failed to join channel',data:error})
                        return reject(error);
                    }
                } catch (error) {
                    Toast(error);
                    this.ServiceReportJoinChannel({
                        uid:odata.uid,
                        status:0
                    })
                    this.LeaveChannelAux()
                    this.clearStatus('Aux')
                    this.data.joiningAux = false;
                    return reject(error);
                }
            } catch (error) {
                logger.error({message:'Failed to join channel',data:error})
                reject(error);
            }
        });
    }

    // 修改JoinChannelMain方法
    async JoinChannelMain(odata){
        return new Promise(async(resolve,reject)=>{
            try {
                // 确保AgoraRTC已加载并初始化
                await this.initAgoraRTCMain();
                if (this.data.joiningMain) {
                    Toast(i18n.t('processing_wait'), 4);
                    return reject(false);
                }
                const ultrasync_uid = storeState.user.uid;
                this.data.joiningMain = true;
                this.data.channelId = odata.channelId;
                this.autoPushStream = odata.autoPushStream
                try {
                    await this.agoraClientMain.join(odata.appId, odata.channelId,odata.token,odata.uid);
                    this.StopSubscribeRemoteStreamMain();
                    this.data.joiningMain = false;
                    this.ServiceReportJoinChannel({
                        uid:odata.uid,
                        status:1
                    })
                    this.data.joinedMain = true;
                    this.data.localMainUid = odata.uid;
                    this.data.currentMainUid = odata.uid;
                    this.data.currentMainUltrasyncId = ultrasync_uid;
                    this.updateUidInfo(odata.uid, {
                        isOnline:1,
                        user_id:ultrasync_uid
                    });
                    if(!this.isJoinMainToShareScreen){
                        this.judgeIfNeedMuteLocalStreamAfterJoinChannelMain()
                    }
                    // this.MuteShareScreen({isMute:false})
                    return resolve(true);
                }catch(error){
                    logger.error({message:'Failed to join channel',data:error})
                    reject(error);
                }
            } catch (error) {
                logger.error({message:'Failed to join channel',data:error})
                reject(error);
            }
        })
    }

    socketNotify(name, data,callback) {
        if (name === "notify_forbid_audio") {
            logger.log({message:'notify_forbid_audio',data})
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            const ultrasync_uid = storeState.user.uid;
            if (this.data.joinedAux && data.requestUserId !== this.ultrasync_uid && !this.data.isHost) {
                //自己发起的不处理 或者自己为主持人不处理
                if (!data.userId) {
                    //没有指定userId 则为全体静音f
                    this.MuteLocalAudioStream({ uid: this.data.localAuxUid, isMute: true });
                } else {
                    if (data.userId === ultrasync_uid) {
                        this.MuteLocalAudioStream({ uid: this.data.localAuxUid, isMute: true });
                    }
                }
            }
        }
        if (name === "conference.host.request.close.channel") {
            logger.log({message:'conference.host.request.close.channel',data})
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            if (!this.data.joinedAux) {
                return;
            }
            Toast(i18n.t('live_ended'), 2);
            window.vm.$store.commit("liveConference/updateConferenceState", {
                cid: this.cid,
                obj: {
                    conferenceState: 0,
                },
            });
            if (this.data.localAuxUid) {
                this.LeaveChannelAux("nothing");
            }
        }
        if (name === "notify_conference_remote_stream_change") {
            logger.log({message:'notify_conference_remote_stream_change',data})
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            const ultrasync_uid = storeState.user.uid;
            if (data.user_id !== ultrasync_uid) {
                //不处理远端告知自己的音视频状态变更
                if (!this.data.roomUserMap.hasOwnProperty(data.uid)) {
                    //用户不存在则不处理更新
                    return;
                }
                let params = {};
                if (data.type === "audio") {
                    params.audioStream = data.status;
                } else {
                    params.videoStream = data.status;
                }
                this.updateUidInfo(data.uid, params);
                // if (data.status && data.type === "video"){
                //     this.checkSubscribe()
                // }
            }
        }
        if (name === "disconnect") {
            window.vm.$store.commit("liveConference/updateConferenceState", {
                cid: this.cid,
                obj: {
                    conferenceState: 0,
                },
            });
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            if (this.data.joinedAux) {
                this.LeaveChannelAux("nothing");
                Toast(i18n.t('lost_connect_with_server'));
            }
            this.data.losing_connect_server = true;
            this.event.emit("HandleDisconnectAux")
        }
        if (name === "connect") {
            this.clearLostConnectTimer();
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            const ultrasync_uid = storeState.user.uid;
            this.ServiceReportJoinChannel({uid:this.data.localAuxUid,status:1})
            setTimeout(async () => {
                this.data.losing_connect_server = false;
                const channelDetail = await this.ServiceGetChannelCurrentStatus();
                this.data.isHost = channelDetail.host_user_id === ultrasync_uid ? 1 : 0;
                this.data.cloud_record_auth = this.checkCloudRecordAuth();
                this.data.isRecording = channelDetail.recording_status;
                this.data.remoteControlData.serviceRemoteControlStatus= channelDetail.remoteControlStatus;
                this.data.remoteControlData.isLocalControl =
                channelDetail.remote_control_pc_uid === this.ultrasync_uid;
                this.data.currentVideoList = channelDetail.video_agora_uid_list;
                logger.log({message:'channelDetail',data:channelDetail})
                if(this.data.channelUUId&&channelDetail.uuid){
                    if (channelDetail.uuid !== this.data.channelUUId) {
                        // 非同一场直播，直接结束
                        Toast(i18n.t('live_expired_tips'))
                        this.LeaveChannelAux()
                        return
                    }
                }

                if (this.data.joinedAux) {
                    await this.getCurrentUserList();
                    await this.judgeIfNeedMuteLocalStreamAfterJoinChannelAux({ uid: this.data.localAuxUid });
                    setTimeout(async () => {
                        this.checkSubscribeAux();
                    }, 1000);

                }
            }, 300);
        }
        if (name === "notify_join_whiteboard") {
            logger.log({message:'notify_join_whiteboard',data})
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            if (data.senderUserId !== this.ultrasync_uid && this.data.joinedAux) {
                //非本人发起
                this.openWhiteBoard();
            }
        }
        if (name === "notify_conference_user_status_change") {
            logger.log({message:'notify_conference_user_status_change',data})
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            const ultrasync_uid = storeState.user.uid;
            if(data.user_id === ultrasync_uid){
                if(data.status){
                    this.data.serviceKnowSelfJoinRoom = true
                    this.event.emit('selfJoinRoom',data)
                }
            }
            if (this.data.joinedAux) {
                if(data.status === 0){ //用户离线了
                    if(data.user_id === ultrasync_uid){
                        return
                    }

                    if(this.data.from === 'tv_wall_push' || this.data.from === 'tv_wall_play'){ // 电视墙会话，另一端离开后，直接强制关闭会话
                        if (this.data.localAuxUid&&data.stream_type === 0) {
                            this.LeaveChannelAux("nothing", 2);
                            return
                        }
                    }
                    this.data.memberLength--
                    if (this.data.currentMainUid === data.agora_uid) {
                        //当离开的是主流
                        this.StopSubscribeRemoteStreamMain();
                    } else if (this.data.currentSubscribeAux.includes(data.agora_uid)) {
                        //当离开的是被订阅的辅流，则主动停止该订阅
                        this.StopSubscribeRemoteStreamAux(data.agora_uid);
                    }
                    this.updateUidInfo(data.agora_uid, { isOnline: 0 });
                }else{

                    this.updateUidInfo(
                        data.agora_uid,
                        {
                            videoStream: data.user_id === ultrasync_uid?this.data.localVideoStream:data.video,
                            audioStream: data.user_id === ultrasync_uid?this.data.localAudioStream:data.audio,
                            streamType: data.stream_type === 0 ? "aux" : "main",
                            isHost: data.isHost ? 1 : 0,
                            isOnline:data.status,
                            uid: data.agora_uid,
                            user_id: data.user_id, //对应云++用户id
                            nickname:data.nickname
                        },
                        true
                    );
                    if(data.user_id !== this.ultrasync_uid){
                        this.checkSubscribeMain();
                    }
                }
            }
        }
        if (name === "notify_cloud_recording_state_changed") {
            logger.log({message:'notify_cloud_recording_state_changed',data})
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            if (this.data.joinedAux) {
                if (data.status) {
                    this.data.isRecording = true;
                } else {
                    this.data.isRecording = false;
                }
            }
        }
        if (name === "remoteControlAvailableStatusChange") {
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            if (this.data.joinedAux) {
                logger.log({message:'remoteControlAvailableStatusChange',data})
                this.data.remoteControlData.serviceRemoteControlStatus = data.status;
                if(data.remote_control_pc_uid){
                    this.data.remoteControlData.isLocalControl = data.remote_control_pc_uid === this.ultrasync_uid;
                    const controlledUserInfo = this.getAttendUserInfo(data.remote_control_doppler_uid);
                    this.data.remoteControlData.nickname = controlledUserInfo.nickname
                }

            }
        }
        if (name === "conference.specify.agora.uid") {
            logger.log({message:'conference.specify.agora.uid',data})
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            if (this.data.joinedAux) {

                if(data.video_agora_uid_list.length>this.data.currentVideoList.length){ //可订阅摄像头有新增
                    logger.log({message:'data.video_agora_uid_list',data:{
                        video_agora_uid_list:data.video_agora_uid_list,
                        currentVideoList:this.data.currentVideoList
                    }})
                    this.data.currentVideoList = data.video_agora_uid_list
                    this.checkSubscribeAux(data.video_agora_uid_list[data.video_agora_uid_list.length-1])
                }else{//可订阅摄像头有减少
                    logger.log({message:'data.video_agora_uid_list',data:{
                        video_agora_uid_list:data.video_agora_uid_list,
                        currentVideoList:this.data.currentVideoList
                    }})
                    this.data.currentVideoList = data.video_agora_uid_list
                    this.checkStopSubscribeAux()
                }
            }
        }
        if (name === "conference_living_ping") {
            if (Number(window.vm.$root.currentLiveCid) !== Number(this.cid)) {
                return;
            }
            if(this.data.joinedAux){
                if(data.hasOwnProperty('uuid')&&this.data.channelUUId){
                    if(data.uuid!==this.data.channelUUId){
                        this.LeaveChannelAux()
                        return
                    }
                }
                if(this.data.localMainUid){
                    this.ServicePingChannel(this.data.localMainUid)
                }
                if(this.data.localAuxUid){
                    this.ServicePingChannel(this.data.localAuxUid)
                }
            }

        }
    }
    /**
     * @description  检测当前环境是否支持webRtc
     * @param {Object} params 参数说明
     */
    checkAgoraSystemRequirements() {
        let res = AgoraRTC.checkSystemRequirements();
        return res;
    }
    /**
     * @description  辅流申请离开房间
     * @param {Object} params 参数说明
     */
    async LeaveChannelAux(action = "normal") {
        try {
            if(this.data.joinedMain){
                await this.LeaveChannelMain()
            }
            await this.agoraClient.leave();

        } catch (error) {
            console.error(error)
        } finally{
            this.ServiceReportLeaveChannel({uid:this.data.localAuxUid,status:1})
            if (
                action === "sender" ||
                action === "groupOwner" ||
                (this.is_single_chat && action !== "nothing")
            ) {
                window.vm.$store.commit("liveConference/updateConferenceState", {
                    cid: this.cid,
                    obj: {
                        conferenceState: 0,
                    },
                });
                this.ServiceDestroyChannel(); //强制结束在场直播并通知所有人
            }
            this.event.emit('HandleNotifyLeaveChannelAux',{
                isHost: this.data.isHost,
                action,
                channelId: this.data.channelId,
                channelUUId: this.data.channelUUId,
            })
            this.clearStatus("Aux");
        }


    }
    /**
     * @description 主流申请离开房间
     * @param {Object} params 参数说明
     */
    async LeaveChannelMain() {
        try {
            await this.agoraClientMain.leave();
        } catch (error) {
            console.error(error)
        } finally{
            this.ServiceReportLeaveChannel({uid:this.data.localMainUid,status:1})
            this.clearStatus("Main");
        }
    }
    /**
     * @description  辅流判断加入房间后是否需要打开音视频流
     */
    async judgeIfNeedMuteLocalStreamAfterJoinChannelAux(data) {
        try{
            let isMuteVideo = true;
            let isMuteAudio = true;

            if (this.data.isSender === 1) {
                //发起者
                isMuteVideo = true;
                isMuteAudio = false;
            } else {
                isMuteVideo = true;
                isMuteAudio = true;
            }
            logger.log({message:'this.data.localAudioStream',data:this.data.localAudioStream})
            console.error(this.data.localVideoStream)
            if (this.data.localAudioStream > -1) {
                // 判断是否有上一次的麦克风摄像头开启记录
                isMuteAudio = !!!this.data.localAudioStream;
            }
            if (this.data.localVideoStream > -1) {
                // 判断是否有上一次的麦克风摄像头开启记录
                isMuteVideo = !!!this.data.localVideoStream;
            } else {
                // 检查用户的摄像头默认设置
                const cameraDefaultSetting = Tool.getCameraDefaultSetting();

                // 如果用户设置了默认开启摄像头，则默认打开摄像头
                if (cameraDefaultSetting) {
                    isMuteVideo = false;
                }
            }

            if (data.uid !== this.data.localMainUid) {
                //非主流，才默认打开声音
                await this.MuteLocalAudioStream({ isMute: isMuteAudio, showErrorTips:false });
            }
            // 增加对视频的控制
            await this.MuteLocalVideoStream({ isMute: isMuteVideo, showErrorTips:false });
        }catch(error){
            console.error(error)
        }finally{

        }


    }
    /**
     * @description  主流判断加入房间后是否需要打开音视频流
     */
    judgeIfNeedMuteLocalStreamAfterJoinChannelMain() {
        // let isMute = !!!this.data.localMainUid;
        // this.MuteLocalVideoStream({ isMute: isMute,showErrorTips:false  });
    }
    /**
     * @description  订阅远方主流
     * @param {Object} params 参数说明
     */
    async SubscribeRemoteStreamMain(uid) {
        if (!uid || !this.data.joinedAux) {
            return;
        }
        let user = this.remotePublishedUser[uid];
        if (!user) {
            logger.log({message:'该用户已不在房间内',data:uid})
            return;
        }
        try {

            await this.agoraClient.subscribe(user, "video");
            await Tool.waitForElementCreated(`#${this.main_dom}`,6000)
            user.videoTrack.play(this.main_dom, { fit: "contain" });
            if(Tool.checkAppClient('PCBrowser')){
                setTimeout(()=>{
                    const playerIds = Tool.getAllVideoIds(this.main_dom)
                    if(playerIds.length>0){
                        const player = new Plyr(`#${playerIds[0]}`, {
                            controls: ['fullscreen'],
                        });
                        this.playerObj[uid] = player
                    }
                },0)
            }
            this.data.currentSubscribeMain = uid;
        } catch (error) {
            logger.error({message:'Failed to subscribe remote stream main',data:error})
            setTimeout(() => {
                this.StopSubscribeRemoteStreamMain();
            });
        }
    }
    /**
     * @description  订阅远方辅流
     * @param {Object} params 参数说明
     */
    async SubscribeRemoteStreamAux(uid, action) {
        let renderDom = null;
        console.trace();
        // 并发保护：如果该 uid 已经在订阅流程中，直接返回
        if (this.pendingSubscribeAux.has(uid)) {
            return;
        }

        // 标记开始订阅
        this.pendingSubscribeAux.add(uid);
        try {
            const {canSubscribeAuxNum} = await this.checkAfterSubscribeAux()
            if(canSubscribeAuxNum===0){ //当前没有多余空位支持订阅
                return
            }
            if (!uid || !this.data.joinedAux) {
                return;
            }
            if (this.data.currentSubscribeAux.includes(uid)) {
                return;
            }
            let user = this.remotePublishedUser[uid];
            if (!user) {
                logger.log({message:'该用户已不在房间内',data:uid})
                await this.waitSdkPublished(uid)
                user = this.remotePublishedUser[uid];
            }

            await this.agoraClient.subscribe(user, "video");
            renderDom = this.assignDomToShowVideoDomList(uid)
            if(renderDom){
                await Tool.waitForElementCreated(`#${renderDom}`,6000)
                user.videoTrack.play(renderDom, { fit: "contain" });
                if(Tool.checkAppClient('PCBrowser')){
                    setTimeout(()=>{
                        const playerIds = Tool.getAllVideoIds(renderDom)
                        if(playerIds.length>0){
                            const player = new Plyr(`#${playerIds[0]}`, {
                                controls: ['fullscreen'],
                            });
                            this.playerObj[uid] = player
                        }
                    },0)
                }
                if(!this.data.currentSubscribeAux.includes(uid)){
                    this.data.currentSubscribeAux.push(uid);
                }
            }

        } catch (error) {
            logger.error({message:'Failed to subscribe remote stream aux',data:error})
            if(renderDom){
                this.deleteDomFromShowVideoDomList(uid)
            }
        } finally {
            // 无论成功或失败，都移除并发标记
            this.pendingSubscribeAux.delete(uid);
        }
    }
    waitSdkPublished(uid){
        let isPublished = false;
        let timer = null;
        const interval = 50; // 定时器间隔 60ms
        const timeout = 5000; // 超时时间 20秒
        let elapsed = 0; // 已经过的时间

        const promise = new Promise((resolve, reject) => {
            timer = setInterval(() => {
                elapsed += interval;

                // 检查是否连接成功
                if (this.remotePublishedUser[uid]) {
                    isPublished = true;
                    clearInterval(timer); // 停止定时器
                    timer = null;
                    resolve(true); // 成功 resolve
                }

                // 超时处理
                if (elapsed >= timeout) {
                    clearInterval(timer); // 停止定时器
                    timer = null;
                    if (!isPublished) {
                        reject(`check isPublished time out ${uid}`);
                    }
                }
            }, interval);
        });

        return promise;
    }
    /**
     * @description  停止订阅远端主流
     * @param {Object} params 参数说明
     */
    async StopSubscribeRemoteStreamMain() {
        if (!this.data.currentSubscribeMain) {
            return;
        }

        try {
            let uid = this.data.currentSubscribeMain;
            let user = this.remotePublishedUser[uid];
            this.data.currentSubscribeMain = 0;
            this.data.currentMainUid = 0;
            this.data.currentMainUltrasyncId = 0;
            if (!user) {
                logger.log({message:'该用户已不在房间内',data:uid})
                return;
            }
            await this.agoraClient.unsubscribe(user, "video");
            if(this.playerObj[user.uid]){
                this.playerObj[user.uid].destroy()
                this.playerObj[user.uid] = null
            }
            this.deleteDomFromShowVideoDomList(uid)
        } catch (error) {
            logger.error({message:'Failed to stop subscribe remote stream main',data:error})
        } finally {
        }
    }
    /**
     * @description  停止订阅远端流
     * @param {Object} params 参数说明
     */
    async StopSubscribeRemoteStreamAux(uid) {
        if (this.data.currentSubscribeAux.length === 0) {
            return;
        }
        let user = null
        if (!uid) {
            user = this.remotePublishedUser[this.data.currentSubscribeAux[0]];
            this.data.currentSubscribeAux = [];

        } else {
            if (!this.data.currentSubscribeAux.includes(uid)) {
                return;
            }
            user = this.remotePublishedUser[uid];
            this.data.currentSubscribeAux = this.data.currentSubscribeAux.filter((item) => item !== uid);
        }
        if (!user) {
            // console.error('该用户已不在房间内')
            return;
        }

        if(this.playerObj[user.uid]){
            this.playerObj[user.uid].destroy()
            this.playerObj[user.uid] = null
        }
        this.deleteDomFromShowVideoDomList(uid)
        await this.agoraClient.unsubscribe(user, "video");
    }
    /**
     * @description  有用户发布了音视频流
     * @param {Object} params 参数说明
     */
    async handleUserPublished(user, mediaType) {
        logger.log({message:'handleUserPublished',data:{user,mediaType}})
        const uid = user.uid;
        if(uid  === this.data.localAuxUid){
            return
        }
        if (mediaType === "audio" && uid !== this.data.localAuxUid) {
            //音频默认直接订阅
            await this.agoraClient.subscribe(user, mediaType);
            user.audioTrack.play();
        }
        if (mediaType === "video") {
            this.remotePublishedUser[uid] = user;
            logger.log({message:'handleUserPublished',data:{uid}})
            this.getCurrentUserList();
        }
    }
    /**
     * @description  有用户取消发布了音视频流
     * @param {Object} params 参数说明
     */
    async handleUserUnPublished(user, mediaType) {
        logger.log({message:'handleUserUnPublished',data:{user,mediaType}})
        if (mediaType === "video") {
            const uid = user.uid;
            if(uid  === this.data.localAuxUid){
                return
            }

            if (this.data.currentSubscribeAux.includes(uid)) {
                this.StopSubscribeRemoteStreamAux(uid);
            }
            if (uid === this.data.currentSubscribeMain) {
                this.StopSubscribeRemoteStreamMain();
            }
            delete this.remotePublishedUser[uid];
            this.deleteDomFromShowVideoDomList(uid)
        }
    }
    /**
     * @description  获取当前所有开麦的人数
     * @param {Object} params 参数说明
     */
    getCurrentAudioStreamCount() {
        let count = 0;
        Object.values(this.data.roomUserMap).forEach((value) => {
            if (value.audioStream) {
                count++;
            }
        });
        return count;
    }
    /**
     * @description  确认服务器层面，本端是否已经加入了直播间
     * @param {Object} params 参数说明
     */
    checkSelfJoinRoomByService(){
        return new Promise((resolve,reject)=>{
            if(this.data.serviceKnowSelfJoinRoom){
                resolve(true)
            }
            this.event.on('selfJoinRoom',()=>{
                resolve(true)
            })
            setTimeout(()=>{
                if(this.data.serviceKnowSelfJoinRoom){
                    resolve(true)
                }else{
                    resolve(true)
                    logger.error({message:'checkSelfJoinRoomByService timeout'})
                }
            },3000)
        })
    }
    /**
     * @description  是否在限制开麦范围内
     * @param {Object} params 参数说明
     */
    IsLimitAudioCount() {
        let maxMicLength =this.data.FreeSpeakLimitUserCount;
        let audioStreamCount = this.getCurrentAudioStreamCount();
        if (audioStreamCount >= maxMicLength) {
            // //超过最大开麦的人数范围
            // Toast(i18n.t('open_mic_to_many_tips'));
            return true;
        } else {
            return false;
        }
    }
    setMicrophoneSettingToLocalStorage(deviceInfo){
        localStorage.setItem('web_live_microphone_setting',JSON.stringify(deviceInfo))
    }
    getMicrophoneSettingFromLocalStorage(){
        let microphone_setting = localStorage.getItem('web_live_microphone_setting')
        if (microphone_setting) {
            try {
                // 尝试解析JSON
                microphone_setting = JSON.parse(microphone_setting);
                if(microphone_setting.deviceId){
                    return microphone_setting
                }
                microphone_setting = null

            } catch (e) {
                microphone_setting = null
                logger.error({message:'Error parsing JSON from localStorage',data:e})
            }
        }
        microphone_setting = null
        return microphone_setting
    }
    setCameraSettingToLocalStorage(deviceInfo){
        localStorage.setItem('web_live_camera_setting',JSON.stringify(deviceInfo))
    }
    getCameraSettingFromLocalStorage(){
        let camera_setting = localStorage.getItem('web_live_camera_setting')
        if (camera_setting) {
            try {
                // 尝试解析JSON
                camera_setting = JSON.parse(camera_setting);
                if(camera_setting.deviceId){
                    return camera_setting
                }
                camera_setting = null

            } catch (e) {
                camera_setting = null
                logger.error({message:'Error parsing JSON from localStorage',data:e})
            }
        }
        camera_setting = null
        return camera_setting
    }
    switchCamera(deviceId) {
        return new Promise(async(resolve,reject)=>{
            try {
                const deviceInfo = this.camerasList.find(item=>item.deviceId === deviceId)
                if(deviceInfo){
                    if(this.localTracks.videoTrack){
                        await this.localTracks.videoTrack.setDevice(deviceId);
                    }
                    this.setCameraSettingToLocalStorage(deviceInfo)
                    this.data.currentVideoTrackId = deviceId
                    const renderDom = this.getDomFromShowVideoDomList('local_dom')
                    if(Tool.checkAppClient('PCBrowser')&&renderDom){
                        setTimeout(()=>{
                            const playerIds = Tool.getAllVideoIds(renderDom)
                            if(playerIds.length>0){
                                const dom = document.getElementById(playerIds[0])
                                dom.style.transform = 'unset';
                            }
                        },0)
                    }
                }
                resolve(true)
            } catch (error) {

                reject(error)
            }

        })
    }
    switchMicrophone(deviceId) {
        return new Promise(async (resolve,reject)=>{
            try {
                const deviceInfo = this.microphonesList.find(item=>item.deviceId === deviceId)
                if(deviceInfo){
                    if(this.localTracks.audioTrack){
                        await this.localTracks.audioTrack.setDevice(deviceId);
                    }
                    this.setMicrophoneSettingToLocalStorage(deviceInfo)
                    this.data.currentAudioTrackId = deviceId
                }
                resolve(true)
            } catch (error) {
                this.handleTrackErrorToast(error,'microphone')
                reject(error)
            }

        })
    }
    /**
     * @description  获取麦克风列表
     * @param {Object} params 参数说明
     */
    getMicrophonesList({showErrorTips=true}={}){
        return new Promise(async (resolve,reject)=>{
            try {
                const micList = await AgoraRTC.getMicrophones()
                this.microphonesList = micList ||[]
                resolve(this.microphonesList)
            } catch (error) {
                if(showErrorTips){
                    this.handleTrackErrorToast(error,'microphone')
                }
                this.microphonesList = []
                reject(error)
            }
        })

    }
    /**
     * @description  获取麦克风列表
     * @param {Object} params 参数说明
     */
    getCamerasList({showErrorTips=true}={}){
        return new Promise(async (resolve,reject)=>{
            try {
                const camerasList = await AgoraRTC.getCameras()
                this.camerasList = camerasList||[]
                resolve(this.camerasList)
            } catch (error) {
                if(showErrorTips){
                    this.handleTrackErrorToast(error,'camera')
                }
                this.camerasList = []
                reject(error)
            }
        })

    }
    /**
     * @description  获取当前正在使用的麦克风id和label
     * @param {Object} params 参数说明
     */
    getCurrentMicrophoneInfo(){
        if(this.localTracks.audioTrack){
            const deviceId = this.data.currentAudioTrackId
            const info = this.microphonesList.find(item=>item.deviceId === deviceId)
            return info
        }
        return null
    }
    /**
     * @description  获取当前正在使用的摄像头id和label
     * @param {Object} params 参数说明
     */
    getCurrentCameraInfo(){
        if(this.localTracks.videoTrack){
            const deviceId = this.data.currentVideoTrackId
            const info = this.camerasList.find(item=>item.deviceId === deviceId)
            return info
        }
        return null
    }
    /**
     * @description  创建测试麦克风音轨
     * @param {Object} params 参数说明
     */
    async createMicrophoneAudioTrackTest(deviceId,callback){
        // 为测试创建独立的音频轨道，不影响正式的音频流
        try {
            // 如果已有测试轨道，先清理
            if(this.testTracks && this.testTracks.audioTrack){
                this.testTracks.audioTrack.stop();
                this.testTracks.audioTrack.close();
            }

            // 初始化测试轨道对象
            if(!this.testTracks){
                this.testTracks = {};
            }

            // 创建专用于测试的音频轨道
            this.testTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack({
                encoderConfig: "music_standard",
                microphoneId: deviceId,
            });

            // 启用轨道并播放音频让用户听到自己的声音
            this.testTracks.audioTrack.setEnabled(true);

            // 播放本地音频，让用户听到自己的声音进行测试
            this.testTracks.audioTrack.play();

            // 开始音量检测
            const setVolumeWave = ()=>{
                if(this.testTracks && this.testTracks.audioTrack){
                    callback&&callback({level:this.testTracks.audioTrack.getVolumeLevel() * 100})
                    this.volumeAnimation = requestAnimationFrame(setVolumeWave);
                }
            }
            this.volumeAnimation = requestAnimationFrame(setVolumeWave);
        } catch (error) {
            console.error('创建测试麦克风轨道失败:', error);
            this.handleTrackErrorToast(error,'microphone');
        }
    }
    /**
     * @description  移除测试麦克风音轨
     * @param {Object} params 参数说明
     */
    cancelMicrophoneAudioTrackTest(){
        // 停止音量检测动画
        cancelAnimationFrame(this.volumeAnimation);

        // 清理测试音频轨道
        if(this.testTracks && this.testTracks.audioTrack){
            // 停止播放音频
            try {
                this.testTracks.audioTrack.stop();
            } catch (error) {
                console.warn('停止音频播放失败:', error);
            }
            this.testTracks.audioTrack.close();
            this.testTracks.audioTrack = null;
        }
    }
    /**
     * @description  创建测试摄像头轨道
     * @param {Object} params 参数说明
     */
    async createCameraVideoTrackTest(deviceId,renderDom){
        try {
            // 如果已有测试轨道，先清理
            if(this.testTracks && this.testTracks.videoTrack){
                this.testTracks.videoTrack.stop();
                this.testTracks.videoTrack.close();
            }

            // 初始化测试轨道对象
            if(!this.testTracks){
                this.testTracks = {};
            }

            // 创建专用于测试的视频轨道
            this.testTracks.videoTrack = await AgoraRTC.createCameraVideoTrack({
                encoderConfig:'480p_1',
                cameraId: deviceId
            });

            // 启用轨道并播放预览
            this.testTracks.videoTrack.setEnabled(true);
            await Tool.waitForElementCreated(`#${renderDom}`,6000);
            this.testTracks.videoTrack.play(renderDom,{ fit: "contain" });
        } catch (error) {
            console.error('创建测试摄像头轨道失败:', error);
            this.handleTrackErrorToast(error,'camera');
        }
    }
    /**
     * @description  移除测试摄像头轨道
     * @param {Object} params 参数说明
     */
    cancelCameraVideoTrackTest(){
        // 清理测试视频轨道
        if(this.testTracks && this.testTracks.videoTrack){
            this.testTracks.videoTrack.stop();
            this.testTracks.videoTrack.close();
            this.testTracks.videoTrack = null;
        }
    }
    /**
     * @description  开/关音频流
     * @param {Object} params 参数说明
     */
    async MuteLocalAudioStream({ isMute,showErrorTips=true  }) {
        logger.log({message:'MuteLocalAudioStream',data:{isMute}})
        if (this.IsLimitAudioCount() && isMute === false) {
            //不在允许开麦的人数范围内
            Toast(i18n.t('open_mic_to_many_tips'));
            return;
        }
        if(!this.localTracks.audioTrack&&!isMute){
            if(this.data.isMutingLocalAudioStream){
                return
            }
            this.data.isMutingLocalAudioStream = true
            try {
                let lastSelectDeviceInfo = this.getMicrophoneSettingFromLocalStorage()
                let lastSelectDeviceId = ''
                if(lastSelectDeviceInfo){
                    lastSelectDeviceId = lastSelectDeviceInfo.deviceId
                }
                let micList =  await this.getMicrophonesList({showErrorTips})


                if(micList.length>0){
                    let useDeviceInfo = micList[0]
                    let index = micList.findIndex(item=>item.deviceId === lastSelectDeviceId)
                    if(index>-1){
                        useDeviceInfo = micList[index]
                    }
                    this.localTracks.audioTrack = await AgoraRTC.createMicrophoneAudioTrack({
                        microphoneId:useDeviceInfo.deviceId
                    });
                    this.data.currentAudioTrackId = useDeviceInfo.deviceId
                    this.localTracks.audioTrack.on("track-ended",()=>{
                        this.MuteLocalAudioStream({isMute:true})
                        Toast(i18n.t('mic_error'))
                    });
                    await this.agoraClient.publish(this.localTracks.audioTrack);
                    this.setMicrophoneSettingToLocalStorage(useDeviceInfo)
                }else{
                    Toast(i18n.t('no_microphone_be_detected'))
                    return
                }
            } catch (error) {
                if(showErrorTips){
                    this.handleTrackErrorToast(error,'microphone')
                }

                if(this.localTracks.audioTrack){
                    this.localTracks.audioTrack.stop();
                    this.localTracks.audioTrack.close();
                }
                return
            }finally{
                this.data.isMutingLocalAudioStream = false
            }

        }
        if (this.localTracks.audioTrack) {
            try {
                if(!isMute){
                    await this.localTracks.audioTrack.setEnabled(!isMute); //创建本地视频后 默认开启
                }else{
                    // await this.agoraClient.unpublish(this.localTracks.audioTrack);
                    await this.localTracks.audioTrack.setEnabled(!isMute); //创建本地视频后 默认开启
                }
                let audioStream = 0;
                if (isMute) {
                    audioStream = 0;
                } else {
                    audioStream = 1;
                }
                this.data.localAudioStream = audioStream;

                this.updateUidInfo(this.data.localAuxUid, { audioStream });
                this.ServiceReportLocalStreamStatus("audio", audioStream);
            } catch (error) {
                if(showErrorTips){
                    this.handleTrackErrorToast(error,'microphone')
                }
            }

        }

    }
    /**
     * @description  开/关本地摄像头视频流
     * @param {Object} params 参数说明
     */
    async MuteLocalVideoStream({ isMute,showErrorTips=true }) {
        console.log('MuteLocalVideoStream',{isMute,showErrorTips})
        console.trace()
        if(!this.localTracks.videoTrack&&!isMute){
            if(this.data.isMutingLocalVideoStream){
                return
            }
            this.data.isMutingLocalVideoStream = true
            try {
                let lastSelectDeviceInfo = this.getCameraSettingFromLocalStorage()
                let lastSelectDeviceId = ''
                if(lastSelectDeviceInfo){
                    lastSelectDeviceId = lastSelectDeviceInfo.deviceId
                }
                let cameraList =  await this.getCamerasList({showErrorTips})
                let useDeviceInfo = cameraList[0]
                let index = cameraList.findIndex(item=>item.deviceId === lastSelectDeviceId)
                if(index>-1){
                    useDeviceInfo = cameraList[index]
                }
                if(cameraList.length>0){
                    this.localTracks.videoTrack = await AgoraRTC.createCameraVideoTrack({
                        cameraId:useDeviceInfo.deviceId,
                        encoderConfig:'480p_1'
                    });
                    this.data.currentVideoTrackId = useDeviceInfo.deviceId
                    this.localTracks.videoTrack.on("track-ended",()=>{
                        this.MuteLocalVideoStream({isMute:true})
                        Toast(i18n.t('camera_error'))
                    });
                    await this.agoraClient.publish(this.localTracks.videoTrack);
                    this.setCameraSettingToLocalStorage(useDeviceInfo)
                }else{
                    Toast(i18n.t('no_videocamera_be_detected'))
                    return
                }
            } catch (error) {
                if(showErrorTips){
                    this.handleTrackErrorToast(error,'camera')
                }
                if(this.localTracks.videoTrack){
                    this.localTracks.videoTrack.stop();
                    this.localTracks.videoTrack.close();
                }
                return
            }finally{
                this.data.isMutingLocalVideoStream = false
            }

        }
        if (this.localTracks.videoTrack) {
            try {
                if(!isMute){
                    await this.handleCheckMuteVideo()
                    await this.localTracks.videoTrack.setEnabled(!isMute); //创建本地视频后 默认开启
                    const renderDom = this.assignDomToShowVideoDomList('local_dom')
                    await Tool.waitForElementCreated(`#${renderDom}`,6000)
                    this.localTracks.videoTrack.play(renderDom,{ fit: "contain" });
                    if(Tool.checkAppClient('PCBrowser')){
                        setTimeout(()=>{
                            const playerIds = Tool.getAllVideoIds(renderDom)
                            if(playerIds.length>0){
                                const dom = document.getElementById(playerIds[0])
                                dom.style.transform = 'unset';
                                const player = new Plyr(`#${playerIds[0]}`, {
                                    controls: ['fullscreen'],
                                });
                                this.playerObj['local_video'] = player
                            }
                        },0)
                    }

                }else{
                    // await this.agoraClient.unpublish(this.localTracks.videoTrack);
                    const renderDom = this.deleteDomFromShowVideoDomList('local_dom')
                    await this.localTracks.videoTrack.setEnabled(!isMute); //创建本地视频后 默认开启
                    this.localTracks.videoTrack.stop(renderDom);
                    if(this.playerObj['local_video']){
                        this.playerObj['local_video'].destroy()
                        this.playerObj['local_video'] = null
                    }
                }
                let videoStream = 0;
                if (isMute) {
                    videoStream = 0;
                } else {
                    videoStream = 1;
                }
                this.data.localVideoStream = videoStream;
                logger.log({message:'*******************:videoStream',data:videoStream})
                this.updateUidInfo(this.data.localAuxUid, { videoStream });
                this.ServiceReportLocalStreamStatus("video", videoStream);
            } catch (error) {
                this.deleteDomFromShowVideoDomList('local_dom')
                if(showErrorTips){
                    this.handleTrackErrorToast(error,'camera')
                }
            }

        }

    }
    handleTrackErrorToast(error,type){
        logger.log({message:'handleTrackErrorToast',data:{error,type}})
        if(Tool.isObjectError(error)&&error.hasOwnProperty('code')){
            if(type === 'camera'&&error.code === LOCAL_TRACK_ERROR.PERMISSION_DENIED){
                Toast( i18n.t('LOCAL_TRACK_ERROR.CAMERA_PERMISSION_DENIED'))
            }else if(type === 'microphone'&&error.code === LOCAL_TRACK_ERROR.PERMISSION_DENIED){
                Toast( i18n.t('LOCAL_TRACK_ERROR.MICROPHONE_PERMISSION_DENIED'))
            }else{
                if(i18n.t(`LOCAL_TRACK_ERROR.${error.code}`)){
                    Toast( i18n.t(`LOCAL_TRACK_ERROR.${error.code}`))
                }else{
                    Toast( i18n.t('LOCAL_TRACK_ERROR.OTHERS'))
                }

            }

        }
    }
    assignDomToShowVideoDomList(id) {
        // 检查是否已经存在相同的 id
        const existingItem = this.data.showAuxVideoDomList.find(obj => obj.id === id);
        if (existingItem) {
            return existingItem.dom; // 如果存在，直接返回对应的 dom
        }

        // 遍历 aux_dom_list，找到第一个不存在于 showAuxVideoDomList 的元素
        for (let item of this.aux_dom_list) {
            const exists = this.data.showAuxVideoDomList.some(obj => obj.dom === item);
            if (!exists) {
                // 将该元素插入 showAuxVideoDomList 中
                this.data.showAuxVideoDomList.push({ id: id, dom: item });
                return item; // 返回插入的元素
            }
        }

        return null; // 如果没有可插入的元素，返回 null
    }

    deleteDomFromShowVideoDomList(id) {
        // 查找 showVideoDomList 中 id 匹配的对象及其索引
        const index = this.data.showAuxVideoDomList.findIndex(item => item.id === id);

        if (index !== -1) {
            const foundItem = this.data.showAuxVideoDomList[index];
            // 删除找到的对象
            this.data.showAuxVideoDomList.splice(index, 1);
            return foundItem.dom; // 返回已删除对象的 dom
        }

        return null; // 如果未找到匹配项，返回 null
    }
    getDomFromShowVideoDomList(id) {
        // 查找 showAuxVideoDomList 中 id 匹配的对象
        const foundItem = this.data.showAuxVideoDomList.find(item => item.id === id);

        // 如果找到，返回该对象的 dom；否则返回 null
        return foundItem ? foundItem.dom : null;
    }
    /**
     * @description  检测是否可以打开摄像头
     * @param {Object} params 参数说明
     */
    handleCheckMuteVideo(){
        return new Promise((resolve,reject)=>{
            console.log('handleCheckMuteVideo',{getCurrentShowVideoNum:this.getCurrentShowVideoNum(),maxVideoNum:this.maxVideoNum})
            if(this.getCurrentShowVideoNum()<this.maxVideoNum){
                resolve(true)
            }else{
                if(this.data.localVideoStream === 1){ //video has open
                    resolve(true)
                    return
                }else{
                    Tool.openCommonDialog({
                        buttons: [i18n.t('confirm_txt'), i18n.t('cancel_btn')],
                        message: i18n.t('auto_cancel_video_tips'),
                        confirm: () => {
                            if(this.getCurrentShowVideoNum()<this.maxVideoNum){
                                this.MuteLocalVideoStream({ uid: this.data.localAuxUid, isMute: false })
                            }else{
                                this.data.currentSubscribeAux[0]&&this.StopSubscribeRemoteStreamAux(this.data.currentSubscribeAux[0])
                                this.MuteLocalVideoStream({ uid: this.data.localAuxUid, isMute: false })
                            }
                        },
                        reject: () => {
                            logger.log({message:'cancel'})
                            reject(new Error('cancel'))
                        },
                    });
                }

            }
        })
    }
    startJoinRoom({main=0,aux=0,isSender=0,videoSource='desktop',cid=this.cid}) {
        return new Promise((resolve, reject) => {
            window.vm.$root.eventBus.$emit(
                "startJoinRoom",
                {
                    main,
                    aux,
                    isSender,
                    videoSource,
                    cid
                },
                (is_suc, data) => {
                    let message = "";
                    if (!is_suc && typeof data === "string") {
                        message = data;
                    }
                    if (is_suc) {
                        resolve(data);
                    } else {
                        reject(new Error(message || "Failed to join room"));
                    }
                }
            );
        });
    }
    /**
     * @description 开关分享桌面
     * @param {Object} params 参数说明
     */
    async MuteShareScreen({isMute=false,checkSafari=true}){
        if(!isMute){
            if(!this.data.joinedMain){
                this.isJoinMainToShareScreen = true
                await this.startJoinRoom({main:1,aux:0,videoSource:'desktop',cid:this.cid})
                this.isJoinMainToShareScreen = false
                return
            }
            try {
                if(this.data.isMutingShareScreen){
                    return
                }
                this.data.isMutingShareScreen = true
                let screenTrack = null
                if(Tool.checkAppClient('PCSafari')&&checkSafari){
                    Tool.openCommonDialog({
                        buttons: [i18n.t('confirm_txt'), i18n.t('cancel_btn')],
                        message: '即将为你打开屏幕分享，是否继续',
                        id:'muteShareScreen',
                        confirm: () => {
                            this.MuteShareScreen({isMute:false,checkSafari:false})
                        },
                        reject: () => {
                            logger.log({message:'reject'})
                            this.data.isMutingShareScreen = true
                            if(this.joinedMain){
                                this.LeaveChannelMain()
                            }
                        },
                        cancel:()=>{
                            logger.log({message:'cancel'})
                            this.data.isMutingShareScreen = true
                            if(this.joinedMain){
                                this.LeaveChannelMain()
                            }
                        }
                    });
                    return
                }else{
                    screenTrack = await AgoraRTC.createScreenVideoTrack({

                    }, "auto");
                }
                // const screenTrack = await AgoraRTC.createScreenVideoTrack({
                //     encoderConfig: "1080p_1"
                // }, "auto");
                if (screenTrack instanceof Array) {
                    this.localTracks.screenVideoTrack = screenTrack[0]
                    this.localTracks.screenAudioTrack = screenTrack[1]
                    this.agoraClient.publish(this.localTracks.screenAudioTrack);
                } else {
                    this.localTracks.screenVideoTrack = screenTrack
                }
                this.localTracks.screenVideoTrack.on("track-ended",()=>{
                    this.handleScreenTrackEnd()
                });


                await this.agoraClientMain.publish(this.localTracks.screenVideoTrack);
                this.data.localScreenStream = 1

                setTimeout(()=>{
                    this.localTracks.screenVideoTrack.play(this.main_dom, { fit: "contain" });
                    if(Tool.checkAppClient('PCBrowser')){
                        setTimeout(()=>{
                            const playerIds = Tool.getAllVideoIds(this.main_dom)
                            if(playerIds.length>0){
                                const player = new Plyr(`#${playerIds[0]}`, {
                                    controls: ['fullscreen'],
                                });
                                this.playerObj[this.data.localMainUid] = player
                            }
                        },0)
                    }
                },0)
            } catch (error) {
                if(this.data.joinedMain){
                    this.LeaveChannelMain()
                }else{
                    if(this.localTracks.screenVideoTrack){
                        this.localTracks.screenVideoTrack.off("track-ended",this.handleScreenTrackEnd);
                    }
                    this.handleScreenTrackEnd()
                }
            } finally{
                this.data.isMutingShareScreen = false
            }

        }else{
            if(this.localTracks.screenVideoTrack){
                this.localTracks.screenVideoTrack.off("track-ended",this.handleScreenTrackEnd);
            }
            this.handleScreenTrackEnd()
        }
    }
    handleScreenTrackEnd() {
        logger.log({message:`Screen-share track ended, stop sharing screen ` + this.localTracks.screenVideoTrack.getTrackId()})
        this.localTracks.screenVideoTrack &&this.localTracks.screenVideoTrack.stop();
        this.localTracks.screenAudioTrack &&this.localTracks.screenAudioTrack.stop()
        this.localTracks.screenVideoTrack && this.localTracks.screenVideoTrack.close();
        this.localTracks.screenAudioTrack && this.localTracks.screenAudioTrack.close();
        if(this.playerObj[this.data.localMainUid]){
            this.playerObj[this.data.localMainUid].destroy()
            this.playerObj[this.data.localMainUid] = null
        }
        this.data.localScreenStream = 0
        if(this.data.joinedMain){
            this.LeaveChannelMain()
        }
    }
    /**
     * @description  打开白板
     * @param {Boolean} force 是否强制走开启白板流程
     *
     *
     */
    async openWhiteBoard(force = false) {
        if (!force && this.data.is_open_white_board) {
            return;
        }
        const data = await this.ServiceGetWhiteBoardToken();
        let params = {
            appIdentifier: data.appId,
            region: data.region,
            uuid: data.uuid,
            uid: data.uid,
            roomToken: data.token,
            groupId: this.cid,
            loginToken: window.localStorage.getItem("loginToken"),
            isShowToolBar: force,
        };
        this.data.whiteBoardInfo = params;
        this.event.emit('openWhiteBoard',params)
    }
    /**
     * @description  清空房间状态
     */
    clearStatus(type = "Aux") {
        if (type === "Main") {
            // 主流退出
            this.data.joiningMain = false;
            this.data.joinedMain = false;
            this.data.localMainUid = 0;
            this.data.currentMainUid = 0;
            this.data.currentMainUltrasyncId = 0;
            delete this.data.roomUserMap[this.data.localMainUid];
            this.data.agoraClientMain = null
            this.isJoinMainToShareScreen = false
        }
        if (type === "Aux") {
            // 辅流退出则全部状态重置
            this.data.isSender = 0;
            this.data.isHost = 0;
            this.data.joiningAux = false;
            this.data.joinedAux = false;
            this.data.localAuxUid = 0;
            this.data.roomUserMap = {};
            this.data.currentMainUid = 0;
            this.data.currentMainUltrasyncId = 0;
            this.data.currentSubscribeMain = 0;
            this.data.currentSubscribeAux = [];

            this.data.joiningMain = false;
            this.data.joinedMain = false;
            this.data.localMainUid = 0;
            this.data.losing_connect_server = false;
            this.data.lost_connect_server = false;
            this.data.is_open_white_board = false;
            this.data.hasWhiteBoard = false;
            this.data.localAudioStream = -1;
            this.data.localVideoStream = -1;
            this.data.localScreenStream = 0;
            this.data.isLastLocalMain = false;
            this.data.isRecording = false;
            this.data.memberLength = 0;
            this.data.cloud_record_auth = 0;
            this.remotePublishedUser = {};
            this.data.from = "";
            window.vm.$root.currentLiveCid = 0;
            window.livingStatus = 0;
            this.data.currentLockAuxUid = 0;
            this.data.currentVideoList = []
            this.data.serviceKnowSelfJoinRoom = false
            this.data.channelUUId = 0
            for (let trackName in this.localTracks) {
                var track = this.localTracks[trackName];
                if (track) {
                    track.stop();
                    track.close();
                    this.localTracks[trackName] = null;
                }
            }
            // 清理测试轨道
            for (let trackName in this.testTracks) {
                var track = this.testTracks[trackName];
                if (track) {
                    track.stop();
                    track.close();
                    this.testTracks[trackName] = null;
                }
            }
            // 停止音量检测动画
            cancelAnimationFrame(this.volumeAnimation);
            this.clearLostConnectTimer();
            this.data.agoraClient = null
            this.data.showAuxVideoDomList = []
            this.data.isMutingLocalVideoStream = false
            this.data.isMutingLocalAudioStream = false
            this.data.isMutingShareScreen = false
            this.isJoinMainToShareScreen = false
            this.data.currentAudioTrackId = null
            this.data.currentVideoTrackId = null
            this.destroyCWhiteBoard()
            if(Tool.checkAppClient('PCSafari')){
                Tool.closeElementMessageBoxByClass('muteShareScreen')
            }
            if (this.CReverseControl && Tool.checkAppClient('PCBrowser')) {
                this.CReverseControl.destroy();
                this.CReverseControl.data = cloneDeep(this.CReverseControl.data);
                this.CReverseControl = null;
                window.CReverseControl[this.cid] = null;
            }
            this.data.remoteControlData={
                linkToDopplerStatus:0,
                serviceRemoteControlStatus: 0,
                isLocalControl:false,
                nickname:''
            }
            // 清空正在订阅的辅流集合
            if (this.pendingSubscribeAux) {
                this.pendingSubscribeAux.clear();
            }
        }
    }
    clearLostConnectTimer() {
        clearTimeout(this.lostConnectAndOpenConfirmDialogTimer);
        clearTimeout(this.lostConnectAndLeaveChannelTimer);
        clearTimeout(this.lostConnectAndLeaveChannelTimer2);
        this.lostConnectAndOpenConfirmDialogTimer = null;
        this.lostConnectAndLeaveChannelTimer = null;
        this.lostConnectAndLeaveChannelTimer2 = null;
        Tool.closeCommonDialog();
    }
    destroyCWhiteBoard(){
        if(this.CWhiteBoard){
            this.CWhiteBoard.destroy()
            this.CWhiteBoard = null
        }
    }
    /**
     * @description  检测云录制权限
     * @param {Object} params 参数说明
     */
    checkCloudRecordAuth() {
        let condition1 = this.data.isHost;
        let condition2 = this.getIsManager();
        let condition3 = this.is_single_chat;
        return condition1 || condition2 || condition3 ? 1 : 0; //单聊，群主，主讲人时 都可以操作云录制
    }
    /**
     * @description  检测是否有对齐摄像头按钮权限
     * @param {Object} params 参数说明
     */
    checkLockAuxAuth() {
        let condition1 = this.data.isHost;
        let condition2 = this.getIsManager();
        let condition3 = this.data.localMainUid;
        const auth = condition1 || condition2 || condition3 ? 1 : 0; //群主，主讲人时 主流都可以操作对齐摄像头
        return auth;
    }
    checkManagerAuth() {
        //全体静音权限
        let condition1 = this.data.isHost;
        let condition2 = this.getIsManager();
        const auth = condition1 || condition2 ? 1 : 0;
        return auth;
    }
    /**
     * @description  获取当前用户列表
     * @param {Object} params 参数说明
     */
    async getCurrentUserList() {
        if (!this.data.joinedAux) {
            return;
        }
        let list = await this.ServiceGetAgoraUidList();
        logger.log({message:'getCurrentUserList',data:list})

        let mainInfo = null;
        let uidList = [];
        let memberLength = 0;
        let leaveUserArr = [];

        for (let item of list) {
            uidList.push(item.agora_uid);

            if (item.stream_type === 1) {
                mainInfo = item;
            } else {
                memberLength++;
            }
            logger.log({message:'this.ultrasync_uid',data:this.ultrasync_uid})
            if (item.user_id === this.ultrasync_uid) {
                item.video = this.data.localVideoStream;
                item.audio = this.data.localAudioStream;
            }
        }

        this.data.memberLength = memberLength;
        this.updateAgoraUidList(list);

        for (let item of Object.values(this.data.roomUserMap)) {
            if (!uidList.includes(item.uid)) {
                leaveUserArr.push(item.uid);
            }
        }

        for (let uid of leaveUserArr) {
            if (this.data.currentMainUid === uid) {
                this.StopSubscribeRemoteStreamMain();
            } else if (this.data.currentSubscribeAux.includes(uid)) {
                this.StopSubscribeRemoteStreamAux(uid);
            }

            this.updateUidInfo(uid, { isOnline: 0 });
        }
        this.checkSubscribeMain();

        return {
            mainInfo,
        };
    }
    /**
     * @description  确认当前房间需要订阅的主流
     * @param {Object} params 参数说明
     */
    checkSubscribeMain() {
        Object.keys(this.data.roomUserMap).forEach((key) => {
            if (this.data.roomUserMap[key].user_id !== this.ultrasync_uid) {
                //非本人
                if (this.data.roomUserMap[key].streamType === "main") {
                    if (!this.data.currentSubscribeMain) {
                        this.SubscribeRemoteStreamMain(this.data.roomUserMap[key].uid);
                    }
                }
            }
        });
    }
    resetObjectValue(oObj) {
        let obj = cloneDeep(oObj);
        if (typeof obj === "object") {
            if (obj === null || obj === undefined) {
                obj = {};
            }
            for (let key in obj) {
                if (typeof obj[key] === "object") {
                    obj[key] = this.resetObjectValue(obj[key]);
                }
            }
        }
        return obj;
    }
    /**
     * @description  更新本地流状态
     * @param {Object} params 参数说明
     */
    updateAgoraUidList(list) {
        if (Array.isArray(list)) {
            list.forEach((item) => {
                if (item.status) {
                    //只写入在线用户
                    this.updateUidInfo(
                        item.agora_uid,
                        {
                            videoStream: item.video,
                            audioStream: item.audio,
                            streamType: item.stream_type === 0 ? "aux" : "main",
                            isHost: item.isHost ? 1 : 0,
                            uid: item.agora_uid,
                            user_id: item.user_id, //对应云++用户id
                            auxInfo: item.auxInfo ? item.auxInfo : {},
                            nickname: item.nickname,
                            introduction:item.introduction
                        },
                        true
                    );
                }
            });
        }
    }
    /**
     * @description  更新uid详细信息
     * @param {Object} params 参数说明
     */
    updateUidInfo(uid, oInfo = {}, force = false) {
        let info = oInfo;
        logger.log({message:'updateUidInfo',data:info})
        if (!this.data.joinedAux && !force) {
            // 退出房间后不再更新参数信息
            return;
        }
        if (info.isOnline === 0) {
            //离线直接踢出成员数组
            delete this.data.roomUserMap[uid];
        } else {
            if (!this.data.roomUserMap[uid]) {
                let streamType = "aux";
                if (this.data.currentMainUid === uid) {
                    streamType = "main";
                }

                this.data.roomUserMap[uid] = {
                    videoStream: 0,
                    videoStreamDetail: {},
                    audioStream: 0,
                    audioStreamDetail: {},
                    streamType,
                    token: "",
                    isHost: 0,
                    isOnline: 1,
                    uid: uid,
                    user_id: "", //对应云++用户id
                    auxInfo: {},
                    nickname: "",
                    introduction:""
                };
            }
            Object.keys(info).map((key) => {
                if (this.data.roomUserMap[uid].hasOwnProperty(key)) {
                    this.data.roomUserMap[uid][key] = info[key];
                }
            });

            if (info.streamType === "main") {
                this.data.currentMainUid = info.uid;
                this.data.currentMainUltrasyncId = info.user_id;
            } else {
            }
        }
        this.event.emit('roomUserMapDataChange',{...this.data.roomUserMap})
    }
    /**
     * @description  判断当前已经有多少路小视频流
     * @param {Object} params 参数说明
     */
    getCurrentShowVideoNum(){
        let videoNum = 0
        if(this.data.localVideoStream === 1){
            videoNum++
        }
        videoNum+=this.data.currentSubscribeAux.length
        return videoNum
    }
    /**
     * @description  检测是否可以订阅辅流
     * @param {Object} params 参数说明
     */
    checkAfterSubscribeAux({showTips=true}={}){
        return new Promise((resolve,reject)=>{
            if(this.getCurrentShowVideoNum()<this.maxVideoNum){
                resolve({canSubscribeAuxNum:this.maxVideoNum-this.getCurrentShowVideoNum()})
            }else{
                showTips&&Tool.openCommonDialog({
                    buttons: [i18n.t('confirm_txt')],
                    message: i18n.t('video_path_full_tips')
                });
                resolve({canSubscribeAuxNum:0})
            }
        })
    }
    /**
     * @description  取消订阅当前房间不存在的辅流
     * @param {Object} params 参数说明
     */
    checkStopSubscribeAux(){
        this.data.currentSubscribeAux.forEach(uid=>{ //退出已经关闭得摄像头订阅
            if(!this.data.currentVideoList.includes(uid)){
                this.StopSubscribeRemoteStreamAux(uid)
            }
        })
    }
    /**
     * @description  确认当前房间需要订阅的辅流
     * @param {Object} params 参数说明
     */
    async checkSubscribeAux(targetUid){

        this.checkStopSubscribeAux()
        const {canSubscribeAuxNum} = await this.checkAfterSubscribeAux({showTips:false})
        if(canSubscribeAuxNum === 0){
            return
        }
        let uidList = []
        if(targetUid&&this.data.roomUserMap[targetUid]&&targetUid!==this.data.localAuxUid){
            uidList.push(targetUid)
        }else{
            this.data.currentVideoList.forEach(uid=>{
                if(uid!==this.data.localAuxUid&&!this.data.currentSubscribeAux.includes(uid)&&uidList.length<canSubscribeAuxNum&&this.data.roomUserMap[uid]){ //非本人辅流
                    uidList.push(uid)
                }
            })
        }
        for (let i = 0; i < uidList.length; i++) {
            await this.SubscribeRemoteStreamAux(uidList[i])
        }

    }
    /**
     * @description  主讲人禁止发言
     * @param {Object} params 参数说明
     */
    async forbiddenSpeak(data) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].forbiddenSpeak(data, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res);
                } else {
                    console.error(res);
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  判断自己是否为最后一个用户 是的话通知服务器强制结束房间
     * @param {Object} params 参数说明
     */
    ifLastChildLeaveRoom() {
        let members = [];
        Object.values(this.data.roomUserMap).forEach((value) => {
            if (!members.includes(value.user_id)) {
                members.push(value.user_id);
            }
        });
        logger.log({message:'ifLastChildLeaveRoom',data:members})
        const ultrasync_uid = storeState.user.uid;
        if (members.length === 1) {
            if (members[0] === ultrasync_uid) {
                //只剩下自己本人
                return true;
            } else {
                return false;
            }
        } else if (members.length === 0) {
            //一个人都没了
            return true;
        } else {
            return false;
        }
    }
    getIsManager() {
        let attendeeList = window.main_screen.conversation_list[this.cid].attendeeList;
        let isManager = false;
        let isAdmin = false;
        for (let key in attendeeList) {
            let item = attendeeList[key];
            if (item.role == storeState.systemConfig.groupRole.manager && item.userid == storeState.user.uid) {
                isManager = true;
                continue;
            } else if (item.role == storeState.systemConfig.groupRole.creator && item.userid == storeState.user.uid) {
                isAdmin = true;
                continue;
            }
        }

        return isManager || isAdmin;
    }
    /**
     * @description  通知声网房间状态变化
     * @param {Object} params 参数说明
     */
    checkShowLeaveConferenceBtn() {
        let isShowLeaveConferenceBtn = this.is_single_chat ? 0 : 1; //除了单聊都有离开直播按钮
        let isShowCloseConferenceBtn = this.data.isHost || this.is_single_chat || this.getIsManager() ? 1 : 0;
        logger.log({message:'checkShowLeaveConferenceBtn',data:{isShowLeaveConferenceBtn,isShowCloseConferenceBtn}})
        return {
            isShowLeaveConferenceBtn,
            isShowCloseConferenceBtn,
        };
    }
    /**
     * @description  js-->PC 关闭直播时前端的窗口
     * @param {Object} params 参数说明
     */
    closeConferenceWebWindow (){
        this.LeaveChannelAux()
    }
    createCReverseControl() { //远程控制
        // 创建
        if (!this.CReverseControl) {
            this.CReverseControl = new CReverseControl({
                uid: this.ultrasync_uid,
                cid: this.cid,
                channelId: this.data.channelId,
            });
        } else {
            this.CReverseControl.destroy();
            this.CReverseControl = new CReverseControl({
                uid: this.ultrasync_uid,
                cid: this.cid,
                channelId: this.data.channelId,
            });
        }
        window.CReverseControl[this.cid] = this.CReverseControl;
        const originData = cloneDeep(this.CReverseControl.data);
        this.CReverseControl.data = Tool.deepReactive(originData, (target, key, value, action, path) => {
            // 检测是否是 roomUserMap 的更新
            Object.keys(this.CReverseControl.data).forEach(key=>{
                this.data.remoteControlData[key] = this.CReverseControl.data[key]
            })

        });
        this.CReverseControl.event.on("checkIsLocalMainStream", (callback) => {
            if (this.data.localMainUid) {
                callback(true);
            } else {
                callback(false);
            }
        });
        this.CReverseControl.event.on('dopplerSocketConnect',()=>{
            this.event.emit('dopplerSocketConnect')
        })
    }
    getAttendUserInfo(uid) {
        const attendeeList = window.main_screen.conversation_list[this.cid].attendeeList;
        if (attendeeList[`attendee_${uid}`]) {
            return attendeeList[`attendee_${uid}`];
        } else {
            return null;
        }
    }
    handleRequestRemoteControl(){
        if(!this.CReverseControl){
            return
        }
        this.CReverseControl.requestRemoteControl()
    }
    handleCancelRemoteControl(){
        if(!this.CReverseControl){
            return
        }
        this.CReverseControl.cancelRemoteControl()
    }
    sendRemoteControlCommand(data){
        if(!this.CReverseControl){
            return
        }
        this.CReverseControl.sendRemoteControlCommand(data)
    }
    /**
     * @description  获取当前房间的状态
     * @param {Object} params 参数说明
     */
    async ServiceGetChannelCurrentStatus(data) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].getChannelCurrentStatus(data, (res) => {
                if (res.error_code === 0 && res.data) {
                    resolve(res.data);
                } else {
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  js-->service 发起者退出房间，通知服务器并通知所有人
     * @param {Object} params 参数说明
     */
    ServiceDestroyChannel({ channelId, channelUUId } = {}) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].destroyChannel(
                {
                    channelId: channelId || this.data.channelId,
                    uuid: channelUUId || this.data.channelUUId,
                },
                (res) => {
                    logger.log({message:'ServiceDestroyChannel',data:res})
                    if (res.error_code === 0) {
                        resolve(res.data);
                    } else {
                        // Toast(i18n.t('cannot_operate_last_live'))
                        logger.error({message:'ServiceDestroyChannel',data:res})
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  js-->service 声网成员列表状态数据，确定当前主流uid
     * @param {Object} params 参数说明
     */
    ServiceGetAgoraUidList() {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].getAgoraUidList({}, (res) => {
                if (res.error_code === 0) {
                    // console.error("ServiceGetAgoraUidList", res.data);
                    // this.updateAgoraUidList(res.data)
                    resolve(res.data);
                } else {
                    reject(res);
                }
            });
        });
    }
    /**
     * @description  上报本地音视频状态
     * @param {Object} params 参数说明
     */
    async ServiceReportLocalStreamStatus(type, status) {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].reportLocalStreamStatus(
                {
                    type,
                    status,
                    channelId: this.data.channelId,
                    uid: this.data.localAuxUid,
                },
                (res) => {
                    if (res.error_code === 0) {
                        logger.log({message:'ServiceReportLocalStreamStatus',data:{type,status,res}})
                        resolve(res.data);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  开启云录制
     * @param {Object} params 参数说明
     */
    async ServiceStartConferenceRecording(uids=[]) {
        return new Promise((resolve, reject) => {
            let params = {
                channelId: this.data.channelId,
            }
            if(uids.length > 0){
                params.agora_uids = uids
            }
            window.main_screen.conversation_list[this.cid].startConferenceRecording(
                params,
                (res) => {
                    logger.log({message:'startRecord',data:res})
                    if (res.error_code === 0) {
                        // this.data.isRecording = true
                        // window.CWorkstationCommunicationMng.CallRecordingStatus({status:1,auth:this.data.isHost})
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  停止云录制
     * @param {Object} params 参数说明
     */
    async ServiceStopConferenceRecording() {
        return new Promise((resolve, reject) => {
            window.main_screen.conversation_list[this.cid].stopConferenceRecording(
                { channelId: this.data.channelId },
                (res) => {
                    logger.log({message:'stopRecord',data:res})
                    if (res.error_code === 0) {
                        // this.data.isRecording = false
                        // window.CWorkstationCommunicationMng.CallRecordingStatus({status:0})
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  上报服务器，用户进入房间
     * @param {Object} params 参数说明
     */
    async ServiceReportJoinChannel({uid,status}) {
        return new Promise((resolve, reject) => {
            if(!uid){
                resolve('uid=0');
                return
            }
            window.main_screen.conversation_list[this.cid].reportUserJoinChannel(
                {
                    channelId: this.data.channelId,
                    clientSeq:new Date().getTime(),
                    uid,
                    status
                },
                (res) => {
                    logger.log({message:'reportUserJoinChannel',data:res})
                    if (res.is_success) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  上报服务器，用户离开房间
     * @param {Object} params 参数说明
     */
    async ServiceReportLeaveChannel({uid,status}) {
        return new Promise((resolve, reject) => {
            if(!uid){
                resolve('uid=0');
                return
            }
            window.main_screen.conversation_list[this.cid].reportUserLeaveChannel(
                {
                    channelId: this.data.channelId,
                    clientSeq:new Date().getTime(),
                    uid,
                    status
                },
                (res) => {
                    logger.log({message:'reportUserLeaveChannel',data:res})
                    if (res.error_code === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  用于上报状态，让服务器依然知道用户在房间
     * @param {Object} params 参数说明
     */
    async ServicePingChannel(uid) {
        return new Promise((resolve, reject) => {
            if(!uid){
                resolve('uid=0');
                return
            }
            window.main_screen.conversation_list[this.cid].pingChannel(
                {
                    channelId: this.data.channelId,
                    clientSeq:new Date().getTime(),
                    uid,
                },
                (res) => {
                    logger.log({message:'pingChannel',data:res})
                    if (res.error_code === 0) {
                        resolve(res);
                    } else {
                        reject(res);
                    }
                }
            );
        });
    }
    /**
     * @description  获取白板房间Token
     * @param {Object} params 参数说明
     */
    async ServiceGetWhiteBoardToken() {
        return new Promise((resolve, reject) => {
            if (!window.main_screen.conversation_list[this.cid]) {
                return;
            }
            window.main_screen.conversation_list[this.cid].getWhiteBoardToken(
                {
                    groupId: this.cid,
                },
                (res) => {
                    if (res.error_code === 0 && res.data) {
                        resolve(res.data);
                    } else {
                        logger.error({message:'ServiceGetWhiteBoardToken',data:res})
                        reject(false);
                    }
                }
            );
        });
    }
}
//
CLiveRoomWeb.instances = {}
export default CLiveRoomWeb;
