import BasePermissionManager from './BasePermissionManager.js';
import { USER_ROLE } from './constant.js';

/**
 * 功能权限管理器
 * 负责具体功能操作的权限控制
 */
class FeaturePermissionManager extends BasePermissionManager {
    constructor() {
        super();
        this.featurePermissions = new Map(); // 功能权限配置
        this.loadFeaturePermissions();
    }

    /**
     * 加载权限数据
     */
    async loadPermissions() {
        this.loadFeaturePermissions();
    }

    /**
     * 加载功能权限配置
     */
    loadFeaturePermissions() {
        // 定义功能权限配置
        const featurePermissions = {
            // 后台管理功能
            'backgroundManage': {
                'access': { roles: [USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN] }
            },
            // 电视墙功能
            'tvWall': {
                'playWall': { roles: [USER_ROLE.DIRECTOR, USER_ROLE.ADMIN, USER_ROLE.SUPER_ADMIN] },
            },
        };

        Object.entries(featurePermissions).forEach(([feature, permissions]) => {
            this.featurePermissions.set(feature, permissions);
        });
    }

    /**
     * 检查功能权限
     * @param {string} permission - 权限标识 (格式: feature.action 或 feature)
     * @param {string} action - 操作名称 (可选，如果第一个参数包含点号则忽略此参数)
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    hasPermission(permission, action = null, context = {}) {
        if (!this.isInitialized()) {
            console.warn('FeaturePermissionManager not initialized');
            return false;
        }

        let feature, actionName;

        // 检查是否使用 feature.action 格式
        if (typeof permission === 'string' && permission.includes('.')) {
            [feature, actionName] = permission.split('.');
        } else {
            feature = permission;
            actionName = action;
        }
        // 检查功能的特定操作权限
        return this.checkFeatureActionPermission(feature, actionName, context);
    }


    /**
     * 检查功能操作权限
     * @param {string} feature - 功能名称
     * @param {string} action - 操作名称
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkFeatureActionPermission(feature, action, context = {}) {
        const featureConfig = this.featurePermissions.get(feature);
        if (!featureConfig) {
            return false; // 没有配置默认不允许
        }

        const actionConfig = featureConfig[action];
        if (!actionConfig) {
            return false; // 没有配置默认不允许
        }

        return this.checkPermissionConfig(actionConfig, context);
    }



    /**
     * 检查权限配置
     * @param {Object} config - 权限配置
     * @param {Object} context - 上下文信息
     * @returns {boolean} 是否有权限
     */
    checkPermissionConfig(config, context = {}) {

        // 检查角色权限
        if (config.roles && config.roles.length > 0) {
            const userRole = this.getUserRole();
            return config.roles.includes(userRole);
        }

        return false;
    }



    /**
     * 获取用户可执行的功能操作
     * @param {string} feature - 功能名称
     * @returns {Array<string>} 可执行的操作列表
     */
    getAvailableActions(feature) {
        const featureConfig = this.featurePermissions.get(feature);

        if (!featureConfig) {
            return [];
        }

        const availableActions = [];
        for (let [action, config] of Object.entries(featureConfig)) {
            if (this.checkPermissionConfig(config)) {
                availableActions.push(action);
            }
        }

        return availableActions;
    }

    /**
     * 批量检查功能权限
     * @param {Array} features - 功能配置数组 [{feature, action, context}]
     * @returns {Object} 权限检查结果
     */
    batchCheckPermissions(features) {
        const results = {};

        features.forEach(({ feature, action, context = {}, key }) => {
            const permissionKey = key || `${feature}${action ? '_' + action : ''}`;
            results[permissionKey] = this.hasPermission(feature, action, context);
        });

        return results;
    }

    /**
     * 添加功能权限配置
     * @param {string} feature - 功能名称
     * @param {Object} permissions - 权限配置
     */
    addFeaturePermission(feature, permissions) {
        this.featurePermissions.set(feature, permissions);
    }



    /**
     * 销毁功能权限管理器
     */
    destroy() {
        this.featurePermissions.clear();
        super.destroy();
    }
}

export default FeaturePermissionManager;
