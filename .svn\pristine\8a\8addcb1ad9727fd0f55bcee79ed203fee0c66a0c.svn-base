<template>
    <div>
        <FooterDialog
            :title="$t('tip_title')"
            :show.sync="visible"
            v-if="visible"
            @cancel="visible = false"
            @submit="submit"
            :isSubmitting="isSubmitting"
            :submitText="$t('join_live_streaming')"
            append-to-body
            :modal="false"
        >
            <div v-if="livingGroupInfo.id" :key="livingGroupInfo.id" class="liveInfo">
                <mr-avatar :url="getLocalAvatar(livingGroupInfo)" :showOnlineState="false"></mr-avatar>
                <div class="subject">
                    <template v-if="!livingGroupInfo.service_type">
                        <template v-if="livingGroupInfo.type===1">{{
                            remarkMap[livingGroupInfo.fid] || livingGroupInfo.nickname || livingGroupInfo.subject
                        }}</template>
                        <template v-else>{{ livingGroupInfo.subject }}</template>
                    </template>
                    <span>{{ $t('initiated_live') }}</span>
                    <!-- <template v-if="livingGroupInfo.clamped">
                        <el-popover placement="top-start" :title="livingGroupInfo.subject" width="200" trigger="hover">
                            <div slot="reference" class="subject_tip"></div>
                        </el-popover>
                    </template> -->
                </div>
                <div class="soundSetting">
                    <i class="icon iconfont iconjinzhilingsheng" @click="enableSound" v-if="!canPlayAudio"></i>
                    <i class="icon iconfont iconlingshengtixing" @click="disableSound" v-else></i>
                </div>

            </div>
        </FooterDialog>
        <audio id="living_notify" src="static/resource_pc/audio/living.mp3" loop></audio>
    </div>
</template>
<script>
import base from "../../lib/base";
import FooterDialog from "../../MRComponents/footerDialog.vue";
import moment from "moment";
import { getLocalAvatar } from "../../lib/common_base";
export default {
    mixins: [base],
    name: "LivingNotifyDialog",
    permission: true,
    model: {
        prop: "value",
        event: "change",
    },
    props: {
        livingGroupInfo: {
            type: Object,
            default: () => {
                return {};
            },
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        FooterDialog,
    },
    data() {
        return {
            getLocalAvatar,
            visible: false,
            isSubmitting: false,
            closeDialogInterval: null,
            audio: null,
            canPlayAudio: false,
            // 需要屏蔽直播通知的路由名称数组
            blockedRoutes: [
                'SmartTechTrainingExamProject_Exam'
            ],
        };
    },
    computed: {
        remarkMap() {
            return this.$store.state.friendList.remarkMap;
        },
        // 判断当前路由是否需要屏蔽直播通知
        shouldBlockNotification() {
            return this.$route && this.blockedRoutes.includes(this.$route.name);
        },
    },
    watch: {
        value: {
            handler(val) {
                // 如果当前路由需要屏蔽直播通知，不显示弹窗
                if (val && this.shouldBlockNotification) {
                    this.visible = false;
                    return;
                }
                this.visible = val;
                if(!val){
                    this.handleClose()
                }
            },
            immediate: true,
        },
        visible: {
            handler(val) {
                this.$emit("change", val);
            },
        },
        "livingGroupInfo.id": {
            handler(val) {
                if(!val){
                    return
                }
                this.handlerLivingChange(val);
            },
        },
        '$root.currentLiveCid':{
            handler(val) {
                if(!val){
                    return
                }
                if(Number(val) === Number(this.livingGroupInfo.id)){
                    this.handleClose();
                }
                console.log('$root.currentLiveCid',val,this.livingGroupInfo.id)
            },
        }
    },
    methods: {
        handleClose() {
            this.visible = false;
            if (this.closeDialogInterval) {
                clearInterval(this.closeDialogInterval);
                this.closeDialogInterval = null;
            }
            if (this.audio) {
                this.audio.pause();
            }
            this.$emit("close");
        },
        async submit() {
            const cid = this.livingGroupInfo.id
            this.openConversation(cid,1,{},(is_success,data)=>{
                if(!is_success){
                    return
                }
                if(this.$checkPermission({regionPermissionKey: 'live'})&&this.$store.state.liveConference[cid]&&this.$store.state.liveConference[cid].conferenceState){//该群有直播，进群自动接起
                    setTimeout(()=>{
                        this.$root.eventBus.$emit('acceptLiveConference')
                    },600)

                }
            });
            this.handleClose();
        },
        handlerLivingChange(id) {
            this.resetInterval(id);
            if(this.$root.currentLiveCid){ //已经在直播中的，不处理了
                this.handleClose()
                return
            }
            // 如果当前路由需要屏蔽直播通知，不处理直播变化
            if (this.shouldBlockNotification) {
                return;
            }
            if (!this.audio) {
                this.audio = document.getElementById("living_notify");
            }
            // 判断音频是否正在播放
            if (this.audio.paused) {
                if (this.canPlayAudio) {
                    this.audio.play();
                }
            } else {
                console.log("音频正在播放");
            }
        },
        resetInterval(id) {
            if (this.closeDialogInterval) {
                clearInterval(this.closeDialogInterval);
                this.closeDialogInterval = null;
            }
            if (!id) {
                return;
            }
            this.closeDialogInterval = setInterval(() => {
                this.handleClose();
            }, 30000);
        },
        enableSound(){
            this.canPlayAudio = true
            if (this.audio) {
                this.audio.play();
            }
        },
        disableSound(){
            this.canPlayAudio = false
            if (this.audio) {
                this.audio.pause();
            }

        }
    },
};
</script>
<style lang="scss">
.footer_dialog {
    background: unset;
    top: auto;
    bottom: 10px;
    right: 0;
    left: auto;
    height: auto;
    position: fixed;
    overflow: unset;
    .liveInfo {
        display: flex;
        align-items: center;
        position: relative;
        .subject {
            font-size: 18px;
            margin-left: 16px;
        }
        .soundSetting{
            position: absolute;
            top: -18px;
            right:-10px;
            i{
                font-size: 26px;
                cursor: pointer;
                &.iconjinzhilingsheng{
                    color: red;
                }
                &.iconlingshengtixing{
                    color: green;
                }
            }
        }
    }
}
</style>
