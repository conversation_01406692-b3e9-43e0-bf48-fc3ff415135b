<template>
    <div>
        <CommonDialog
            class="favorites"
            :title="$t('cloud_favorites')"
            :show.sync="visible"
            :close-on-click-modal="false"
            width="660px"
            :modal="false"
            @closed="handleClose"
            :footShow="false"
            :append-to-body="false"
        >
            <div class="favorites_container" v-loading="!loadedFavorites">
                <div
                    v-for="(group, g_index) of favoritesGroups"
                    class="favorites_group_item clearfix"
                    :key="group.favorite_id"
                >
                    <p class="group_subject">
                        {{ formatTime(group.send_ts) }}
                        <i
                            class="iconfont iconunlock"
                            v-show="group.public_status > 0"
                            @click="changePublicStatus(group)"
                        ></i>
                        <i
                            class="iconfont iconlock"
                            v-show="group.public_status == 0"
                            @click="changePublicStatus(group)"
                        ></i>
                    </p>

                    <div class="clearfix">
                        <gallery-file-list
                            v-if="group.list"
                            :galleryList="group.list"
                            :span="4"
                            :from="`personalFavorite`"
                            @contextmenu="favoritesMenu($event, g_index)"
                            @openGallery="openGallery($event, g_index)"
                        ></gallery-file-list>
                    </div>
                </div>
            </div>
            <!-- 只在画廊路由时显示 router-view -->
            <router-view v-if="$route.path.includes('/gallery')"></router-view>
        </CommonDialog>

        <!-- 引入 baseGallery 组件 -->
        <BaseGallery
            ref="baseGallery"
            :loading="galleryLoading"
        >
        </BaseGallery>
    </div>
</template>
<script>
import base from "../lib/base";
import { parseImageListToLocal, cancelFavoriteCommit, getRealUrl, checkResourceType } from "../lib/common_base";
import GalleryFileList from "../components/galleryFileList.vue";
import service from "../service/service";
import moment from "moment";
import CommonDialog from "../MRComponents/commonDialog.vue";
import BaseGallery from "../MRComponents/baseGallery.vue";

export default {
    mixins: [base],
    name: "FavoritesPC",
    components: {
        GalleryFileList,
        CommonDialog,
        BaseGallery,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            favorites: [],
            loadedFavorites: false,
            sortedFavorites: [],
            galleryLoading: false,
        };
    },
    computed: {
        visible: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit("input", val);
            },
        },
        favoritesGroups() {
            return this.$store.state.userFavorites;
        },
    },
    mounted() {
        this.$nextTick(() => {
            this.$root.eventBus.$off("deleteFavoritesImage").$on("deleteFavoritesImage", this.deleteFavoritesImage);
            this.getFavorites();
        });
    },
    methods: {
        getFavorites() {
            let controller = window.main_screen.controller;
            var that = this;
            controller.emit("query_user_favorites", null, function (is_succ, data) {
                if (is_succ) {
                    that.loadedFavorites = true;
                    that.favorites = data;
                    parseImageListToLocal(that.favorites, "url");
                    that.parseFavorites();
                } else {
                    that.$message.error("get favorites error");
                }
            });
        },
        parseFavorites() {
            let groups = {};
            for (let item of this.favorites) {
                let cid = item.conversation_list[0].group_id;
                item.group_id = item.conversation_list[0].group_id;
                let public_status = item.public_status || 0;
                let send_ts = item.send_ts;
                item.send_ts = send_ts;
                item.is_private = true;
                item.resource_id = item.favorite_id;
                if (groups[send_ts]) {
                    groups[send_ts].list.push(item);
                } else {
                    groups[send_ts] = {
                        cid: cid,
                        send_ts: item.send_ts,
                        list: [item],
                        public_status: public_status,
                    };
                }
            }
            var tempArr = [];
            this.sortedFavorites = [];
            for (let key in groups) {
                tempArr.push(groups[key]);
                this.sortedFavorites = this.sortedFavorites.concat(groups[key].list);
            }
            this.$store.commit("userFavorites/setFavoritesGroups", tempArr);
        },
        openGallery(event, g_index) {
            console.log(event, g_index, event.index);
            let index = this.getIndex(g_index, event.index);
            let file = this.sortedFavorites[index];
            console.log(file);

            this.galleryLoading = true;

            try {
                // 转换数据格式以适配 baseGallery 组件
                let galleryFiles = [];
                this.sortedFavorites.map(item => {
                    // 根据不同的msg_type处理缩略图和真实URL
                    const thumbnailUrl = this.getThumbnailUrl(item);
                    const realUrl = this.getRealUrlForGallery(item);
                    galleryFiles.push({
                        url: realUrl,
                        thumbnail: thumbnailUrl,
                    })
                });
                console.log(galleryFiles);
                // 使用 baseGallery 组件打开画廊
                this.$refs.baseGallery.openGallery(galleryFiles, index);
                this.galleryLoading = false;

            } catch (error) {
                console.error('打开画廊失败:', error);
                this.galleryLoading = false;
                this.$message.error('打开画廊失败');
            }
        },
        getIndex(g_index, f_index) {
            let index = 0;
            for (var i = 0; i < g_index; i++) {
                index += this.favoritesGroups[i].list.length;
            }
            index += parseInt(f_index);
            return index;
        },
        getFileTypeFromUrl(url) {
            if (!url) {
                return 'image';
            }

            const extension = url.split('.').pop().toLowerCase();

            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(extension)) {
                return 'image';
            } else if (['mp4', 'webm', 'ogg', 'mov', 'avi', 'flv', 'm3u8'].includes(extension)) {
                return 'video';
            } else if (extension === 'pdf') {
                return 'pdf';
            } else if (extension === 'dcm') {
                return 'dcm';
            } else {
                return 'image'; // 默认为图片
            }
        },
        deleteFavoritesImage(file) {
            console.log(file);
            let del_index_list = [];
            let g_index = file.g_index;
            let f_index = file.f_index;
            let target = this.favoritesGroups[g_index].list[f_index];
            let index = 0;
            for (var i = 0; i < g_index; i++) {
                index += this.favoritesGroups[i].list.length;
            }
            index += parseInt(f_index);
            del_index_list.push(index);
            this.loadedFavorites = true;
            cancelFavoriteCommit(
                {
                    resource_id: target.resource_copied_from,
                    cid: target.group_id,
                },
                () => {
                    this.removeFavorites(del_index_list);
                }
            );
        },
        removeFavorites(index_list) {
            let removeNum = 0;
            index_list = index_list.sort(function (a, b) {
                return a - b;
            });
            for (let index of index_list) {
                let file = this.sortedFavorites[index - removeNum];
                console.log("delete", file.favorite_id);
                this.sortedFavorites.splice(index - removeNum, 1);

                removeNum++;
            }
            let log = [];
            for (let file of this.sortedFavorites) {
                log.push(file.favorite_id);
            }
            console.log("exist id", log);
            this.favorites = this.sortedFavorites;
            this.parseFavorites();
            this.loadedFavorites = true;
        },
        favoritesMenu($event, g_index) {
            console.log($event, g_index, this.chatType);
            $event.file.g_index = g_index;
            $event.file.f_index = $event.file.index;

            this.callImageMenu($event.event, $event.file, "user_favorites");
        },
        changePublicStatus(group) {
            let tip = "";
            let targetStatus = 0;
            if (group.public_status == 0) {
                tip = this.$t("favorites_public_tip");
                targetStatus = 1;
            } else if (group.public_status > 0) {
                tip = this.$t("favorites_private_tip");
                targetStatus = 0;
            }
            let userFavoriteIdList = [];
            for (let item of group.list) {
                userFavoriteIdList.push(item.favorite_id);
            }
            this.$confirm(tip, this.$t("tip_title"), {
                confirmButtonText: this.$t("confirm_button_text"),
                cancelButtonText: this.$t("cancel_button_text"),
                type: "info",
            })
                .then(() => {
                    service
                        .updatePublicStatus({
                            userFavoriteIdList: userFavoriteIdList,
                            publicStatus: targetStatus,
                        })
                        .then((res) => {
                            if (res.data.error_code == 0) {
                                group.public_status = targetStatus;
                                for (let item of group.list) {
                                    item.public_status = targetStatus;
                                }
                            }
                        });
                })
                .catch(() => {});
        },
        handleClose() {
            // 关闭弹窗，通过设置 visible 为 false
            this.visible = false;
        },
        // 根据不同的msg_type获取缩略图URL
        getThumbnailUrl(item) {
            const resourceType = checkResourceType(item);
            const systemConfig = this.$store.state.systemConfig;

            // 对于不同类型的资源，使用不同的缩略图处理方式
            if (resourceType === 'image') {
                // 图片类型：直接使用url作为缩略图
                return item.error_image || item.url || 'static/resource_pc/images/default.png';
            } else if (resourceType === 'video') {
                // 视频类型：使用url作为缩略图（通常是视频的封面图）
                return item.url;
            } else if (resourceType === 'review_video') {
                // 实时视频回放：不使用缩略图，显示文本信息
                return item.coverUrl;
            } else if (item.msg_type === systemConfig.msg_type.File) {
                // 文件类型：使用文件类型图标
                return `static/resource_pc/images/file_icon/${item.file_type}.png`;
            } else {
                // 默认情况
                return item.url;
            }
        },
        // 根据不同的msg_type获取真实URL
        getRealUrlForGallery(item) {
            const resourceType = checkResourceType(item);
            const systemConfig = this.$store.state.systemConfig;

            // 对于不同类型的资源，使用不同的真实URL处理方式
            if (resourceType === 'image' || resourceType === 'video') {
                // 图片和视频类型：使用getRealUrl函数处理
                return getRealUrl(item);
            } else if (resourceType === 'review_video') {
                // 实时视频回放：使用ultrasound_url
                return item.ultrasound_url;
            } else if (item.msg_type === systemConfig.msg_type.File) {
                // 文件类型：使用url
                return item.url;
            } else {
                // 默认情况：使用getRealUrl函数处理
                return getRealUrl(item);
            }
        },
    },
};
</script>
<style lang="scss">
.favorites {
    .favorites_container {
        height: 100%;
        overflow: auto;
        .favorites_group_item {
            margin: 10px 0;
            .group_subject {
                font-size: 16px;
                line-height: 2;
                .iconunlock,
                .iconlock {
                    margin-left: 6px;
                    color: #5f92f6;
                    cursor: pointer;
                }
            }
        }
    }
}
</style>
