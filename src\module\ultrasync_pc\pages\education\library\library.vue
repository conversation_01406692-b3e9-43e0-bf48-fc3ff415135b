<template>
    <div class="education-library" v-loading="isLoading">
        <div class="search_container">
            <el-input ref="search_input"
                clearable
                @keyup.native.enter="searchInput"
                v-model="filters.searchInputKey"/>
            <el-button @click="searchInput" type="primary">{{$t('search')}}</el-button>
        </div>
        <div class="swipe_container">
            <el-carousel trigger="click" height="300px">
              <el-carousel-item v-for="(item, i) in filters.swipe_posts"
                                :key="item.id" >
                <img :src="item.url" @click="viewRepositoryPost({ id: item.pid })" />
              </el-carousel-item>
            </el-carousel>
        </div>
        <div class="hot_types_container">
            <div
                class="hot_type_item"
                v-for="(hot_type,index) in filters.hot_types"
                :key="hot_type.id"
                :class="[get_hot_type(index, 'is_active')]"
                @click="changeHotTypeByIndex(get_hot_type(index))"
            >
                <svgLoader
                    :url="get_hot_type(index, 'icon')"
                    customClass="library_icon_img"
                ></svgLoader>
                <div class="hot_type_name">
                    {{ get_hot_type(index, "name") }}
                </div>
            </div>
        </div>
        <div class="filter_type_container">
            <div class="filter_type_list">
                <span
                    class="filter_type_item"
                    v-for="item in (currentTag.length > 0 ? currentTag : filters.condition.tag).filter(
                        (v) => v.in_pop
                    )"
                    :class="{ 'is-active': item.is_checked }"
                    :key="'tag' + item.id"
                    @click="filedChange(item, 'tag')"
                >
                    {{ getTagName(item) }}
                </span>
                <div class="result_type_shuaxin" @click="debounceHandleReset()">
                    <i class="icon iconfont iconshuaxin"></i>
                    {{ $t('register_reset_btn') }}
                </div>
            </div>
        </div>
        <div class="posts_container" v-loading="!isLoading && filters.isSearchLoading">
            <div v-if="posts.length == 0 && !filters.isSearchLoading" class="current_post_empty">
                <div>
                    {{ $t('sorry_no_post_dat_with_tag') }}
                </div>
            </div>
            <template v-else>
                <div class="result_num_tips">
                    {{
                        $t('total_count_of_post').replace("${name}", total > 99 ? "99+" : total)
                    }}
                </div>
                <ul  class="posts_list infinite-list">
                    <li
                        class="current_post_item infinite-list-item"
                        v-for="(post, i) in posts"
                        :key="'post' + i"
                        @click="viewRepositoryPost(post)"
                    >
                        <div class="post_image">
                            <img
                                :src="getPostImage(post)"
                                :class="{ default_image: getPostImage(post) == defaultImage }"
                            />
                        </div>
                        <div class="post_title" v-html="markTitle(post.title)"></div>
                        <div class="post_like">
                            <i class="iconfont iconyanjing post_like_item">{{
                                post.post_more_details.postviews > 99
                                    ? "99+"
                                    : post.post_more_details.postviews
                            }}</i>
                            <i class="iconfont iconzan post_like_item">{{
                                post.post_more_details.postlikes > 99
                                    ? "99+"
                                    : post.post_more_details.postlikes
                            }}</i>
                        </div>
                    </li>
                    <p class="no_more_text" v-if="posts.length === total && posts.length>0">{{ $t('no_more_text')}}</p>
                    <p class="no_more_text click_load_more" v-loading="filters.isLoadingMore" v-else @click="loadMorePosts">{{ $t('click_more_text')}}</p>
                </ul>
            </template>
                
        </div>
        <router-view></router-view>
    </div>
</template>

<script>
import base from "../../../lib/base";
import library from "@/common/library.js";
import libraryService from "@/common/service/libraryService.js";
import Tool from "@/common/tool.js";
import { cloneDeep } from 'lodash'
import svgLoader from "@/module/ultrasync/components/svgLoader.vue";
import { getLanguage } from "@/common/i18n";
export default {
    mixins: [base, library],
    name: "repository_page",
    components: {
        svgLoader,
    },
    data() {
        return {
            isLoading: false,
            isRefresh: false,
            defaultImage: "",
            currentActiveIndex: 0,
            currentTag: [],
            debounceHandleReset: Tool.debounce(this.handleReset, 500),
        };
    },
    beforeDestroy() {
        this.$store.commit("libraryData/updateLibraryData", { posts: [], total: 0 });
    },
    computed: {
        posts() {
            return this.$store.state.libraryData.posts;
        },
        total() {
            return this.$store.state.libraryData.total;
        },
    },
    created() {
        let library_server = this.$store.state.systemConfig.serverInfo.library_server;
        // let ajaxServer = library_server.protocol + library_server.addr + ":" + library_server.port + "/library/";
        let ajaxServer = libraryService.getBaseUrl();
        this.defaultImage = ajaxServer + "wp-content/themes/library_fliter/assets/img/default-thumbnail.png";
        this.iconPref = ajaxServer + "wp-content/plugins/wp_m_post_statistical/assets/image/";
    },
    mounted() {
        this.isLoading = true;
        this.filters.isSearchLoading = true;
        setTimeout(async () => {
            if (this.$store.state.libraryData.library_token) {
                this.startRender(false);
            } else {
                let external_token = await this.getExternalToken();
                if (external_token) {
                    await this.getLibraryToken(external_token);
                    this.startRender(false);
                } else {
                    this.isLoading = false;
                }
            }
        }, 0);
    },
    methods: {
        onRefresh() {
            this.startRender(true);
        },
        handleSwipeChange(index) {
            this.currentActiveIndex = index;
        },
        startRender(reload) {
            let that = this;
            (async (reload) => {
                let function_list = [];
                if (that.getListDataFromStore("swipe_posts") && !reload) {
                    that.filters.swipe_posts = that.getListDataFromStore("swipe_posts");
                } else {
                    const fc_get_swipe_posts = async () => {
                        that.filters.swipe_posts = await that.getPostSlideImages();
                        that.$store.commit("libraryData/updateLibraryData", { swipe_posts: that.filters.swipe_posts });
                        return true;
                    };
                    function_list.push(fc_get_swipe_posts());
                }

                if (that.getListDataFromStore("tags") && !reload) {
                    that.filters.condition["tag"] = that.getListDataFromStore("tags");
                    that.filters.hot_types = that.getListDataFromStore("hot_tags");
                    that.setActiveHotType(0);
                } else {
                    const fc_get_all_tags = async (resolve) => {
                        let tags = await that.getPostTags();
                        let parentTags = tags.filter(
                            (item) => item.meta && item.meta.is_parent && item.meta.is_parent[0] === "1"
                        );
                        let normalTags = tags.filter(
                            (item) => !(item.meta && item.meta.is_parent && item.meta.is_parent[0] === "1")
                        );
                        parentTags.forEach((item) => {
                            item.children = normalTags.filter(
                                (child) => child.meta && child.meta.parent_tag && child.meta.parent_tag[0] === item.name
                            );
                        });
                        that.filters.condition["tag"] = cloneDeep(tags);
                        let hot_tags = cloneDeep(parentTags).reduce((h, v) => {
                            const item = {
                                ...v,
                                cn: v.name,
                                en: v.meta && v.meta.tag_en ? v.meta.tag_en[0] : v.name,
                                key: "",
                                icon: v.meta && v.meta.tag_icon ? v.meta.tag_icon[0] : "",
                                sort_key: v.meta && v.meta.tag_sort ? parseInt(v.meta.tag_sort[0]) : 1000000,
                                is_active: "",
                            };
                            h.push(item);
                            return h;
                        }, []);
                        hot_tags.sort((a, b) => a.sort_key - b.sort_key);
                        that.filters.hot_types = hot_tags;
                        if (that.filters.hot_types.length > 0) {
                            that.filters.hot_types[0].is_active = true;
                        }
                        that.$store.commit("libraryData/updateLibraryData", { tags: normalTags, hot_tags });
                        that.setActiveHotType(0);
                        return true;
                    };
                    function_list.push(fc_get_all_tags());
                }

                // if(this.getListDataFromStore('categories')&&!reload){
                //     this.filters.condition["category"] = this.getListDataFromStore('categories')
                // }else{
                //     this.filters.condition["category"] = await this.getAllCategories();
                //     this.$store.commit('libraryData/updateLibraryData',{categories:this.filters.condition["category"] })
                // }
                that.isRefresh = false;

                if (function_list.length > 0) {
                    const result = await Promise.all(function_list);
                }
                if (that.getListDataFromStore("init_posts") && !reload) {
                    let init_posts = that.getListDataFromStore("init_posts");
                    that.$store.commit("libraryData/updateLibraryData", {
                        posts: init_posts.posts,
                        total: init_posts.total,
                    });
                } else {
                    const fc_get_posts = async (resolve) => {
                        let new_hot_posts = await that.search(true);
                        that.$store.commit("libraryData/updateLibraryData", {
                            init_posts: { posts: that.posts, total: that.total },
                        });
                        return true;
                    };
                    function_list.push(fc_get_posts());
                }
                that.isLoading = false;
            })(reload);
        },
        viewRepositoryPost(post) {
            this.$router.push(`/main/education/library/post/${post.id}`);
        },
        async searchInput() {
            this.search(true);
        },

        async loadMorePosts() {
            if (this.posts.length >= this.total && this.posts.length > 0) {
                this.filters.isSearchLoading = false;
                return;
            }
            if (!this.filters.isShowCondition) {
                this.filters.isLoadingMore = true;
                await this.search(false);
                this.filters.isLoadingMore = false;
            }
        },
        getPostImage(post) {
            let url = this.defaultImage;
            // if (post._embedded && post._embedded["wp:featuredmedia"]) {
            //     url = post._embedded["wp:featuredmedia"][0]?.source_url;
            // }

            if (post.post_more_details && post.post_more_details.post_thumbnail_url) {
                url = post.post_more_details.post_thumbnail_url;
            }

            return url;
        },

        async changeHotTypeByIndex(index) {
            //更改推荐后-选中是否处理
            this.setActiveHotType(index);
            await this.search(true);
            return;
        },
        setActiveHotType(index) {
            if (this.filters.hot_types && this.filters.hot_types[index]) {
                if (!this.filters.hot_types[index].is_active) {
                    this.filters.hot_types.forEach((item, i) => {
                        item.is_active = "";
                        if (i == index) {
                            item.is_active = "is_active";
                            let tags = item.children.length > 0 ? item.children : this.getListDataFromStore("tags");
                            this.filters.condition["tag"] = (tags || []).map((item) => {
                                return {
                                    ...item,
                                    is_checked: false,
                                };
                            });
                        }
                        this.filters.hot_types[i] = item;
                        return item;
                    });
                } else {
                    this.filters.hot_types.forEach((item, i) => {
                        if (i == index) {
                            item.is_active = "is_active";
                            let tags = item.children.length > 0 ? item.children : this.getListDataFromStore("tags");
                            this.filters.condition["tag"] = (tags || []).map((item) => {
                                return {
                                    ...item,
                                    is_checked: false,
                                };
                            });
                        }
                        this.filters.hot_types[i] = item;
                        return item;
                    });
                }
            }
        },
        getTagName(item) {
            let name = item.name;
            if (getLanguage() == "CN") {
                name = item.name;
            } else {
                name = item.meta && item.meta.tag_en ? item.meta.tag_en[0] : item.name;
            }
            return name || item.name;
        },
        get_hot_type(hot_type_index, field = "") {
            // console.error(this.filters.hot_types, hot_type_index);
            if (this.filters.hot_types[hot_type_index]) {
                if (field) {
                    if (field == "name") {
                        return getLanguage() == "CN"
                            ? this.filters.hot_types[hot_type_index]["cn"]
                            : this.filters.hot_types[hot_type_index]["en"];
                    } else if (field == "icon") {
                        const icon = this.filters.hot_types[hot_type_index][field];
                        return icon ? this.iconPref + icon : "";
                    } else {
                        return this.filters.hot_types[hot_type_index][field];
                    }
                } else {
                    return hot_type_index;
                }
            }
            return "";
        },
        getImage(post) {
            if (post._embedded && post._embedded["wp:featuredmedia"] && post._embedded["wp:featuredmedia"].length > 0) {
                return post._embedded["wp:featuredmedia"][0].source_url;
            }
            return "";
        },
        //显示过滤
        toggleCondition() {
            if (!this.filters.isSearchLoading) {
                this.filters.isShowCondition = !this.filters.isShowCondition;
            }
        },

        async handleback(newCondition, need_search) {
            this.$set(this.filters, "condition", newCondition);
            if (need_search) {
                let new_posts = await this.search(true);
            }
        },
        async handleReset() {
            let condition = {};
            this.filters.searchInputKey = "";
            for (let key in this.filters.condition) {
                let list = this.filters.condition[key];
                condition[key] = this.filters.condition[key].reduce((h, v) => {
                    v.is_checked = false;
                    h.push(v);
                    return h;
                }, []);
            }
            await this.handleback(condition, true);
        },
        async filedChange(field, parent, gparent) {
            var condition = cloneDeep(this.filters.condition);
            if (gparent) {
                condition[gparent] = condition[gparent] || {};
                condition[gparent][parent] = condition[gparent][parent] || [];
                condition[gparent][parent] = this.filters.condition[gparent][parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            } else {
                condition[parent] = condition[parent] || [];
                condition[parent] = this.filters.condition[parent].reduce((h, v) => {
                    if (v.id == field.id) {
                        v.is_checked = !v.is_checked;
                    }
                    h.push(v);
                    return h;
                }, []);
            }
            await this.handleback(condition, true);
        },
        markTitle(title) {
            if (this.filters.searchInputKey) {
                const highlight = this.filters.searchInputKey;
                let replaceString = '<span style="color:#00c59d;">' + highlight + "</span>"; // 高亮替换v-html值
                return title.split(highlight).join(replaceString);
            } else {
                return title;
            }
        },
    },
};
</script>

<style lang="scss" scoped>
@import '@/module/ultrasync_pc/style/education.scss';

.education-library {
    height: 100%;
    display: flex;
    flex-direction: column;
    background-color: #f7f9fc;
    overflow: auto;
    .search_container{
        padding: 0.5rem 1rem;
        display: flex;
        gap: .5rem;
    }
    .swipe_container{
        .el-carousel__item{
            background: #000;
            img{
                margin: 0 auto;
                height: 100%;
                display: block;
            }
        }
    }
    .hot_types_container{
        display: flex;
        flex-wrap: wrap;
        padding: 20px 20px 0;
        .hot_type_item{
            width: 8rem;
            display: flex;
            justify-content: center;
            flex-direction: column;
            align-items: center;
            font-size: .7rem;
            color: #7a7a7a;
            margin-bottom: 1rem;
            cursor: pointer;
            .hot_type_name{
                padding: .2rem
            }
        }

        :deep(.hot_type_item){
            .library_icon_img{
                svg{
                    width: 36px;
                    height: 36px;
                }
            }   
            &.is_active {
                color: #00c59dc7;
                svg {
                    color: #00c59dc7;
                    path {
                        fill: #00c59dc7;
                    }
                }
            }
        }
    }
    .filter_type_container {
        padding: 0.5rem;
        border-top: 0.01px solid #e3e3e3;
        display: flex;
        .filter_type_list {
            flex: 2;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            align-items: center;
            .filter_type_item {
                cursor: pointer;
                background-color: #e9e9e9;
                color: rgb(155, 168, 172);
                padding: 0.2rem .4rem;
                margin: 0.1rem;
                border-radius: 0.3rem;
                font-size: 0.65rem;
                height: 1.2rem;
                display: flex;
                justify-items: center;
                align-items: center;
            }
            .is-active {
                color: #00c59d;
            }
            .result_type_shuaxin{
                color: #00c59d;
                cursor: pointer;
                margin-left: .5rem;
            }
        }
    }
    .posts_container{
        .current_post_empty,.no_more_text{
            font-size: 1rem;
            padding:.5rem;
            text-align: center;
            width: 100%;
        }
        .click_load_more{
            cursor: pointer;
        }
        .result_num_tips{
            padding: 0 1rem;
            margin-bottom: .5rem;
        }
        .posts_list{
            position: relative;
            // padding: 0.25rem 1.5rem 0.4rem 1.5rem;
            padding: 0 1rem;
            display: flex;
            flex-direction: row;
            flex-wrap: wrap;
            justify-content: flex-start;
            .current_post_item {
                display: flex;
                flex-direction: column;
                width: 16rem;
                height: 14rem;
                margin-bottom: 0.8rem;
                margin-right: 1rem;
                align-items: center;
                background: #ececec;
                border-radius: 0.4rem;
                .default_image {
                    width: 4rem;
                    height: 4rem;
                }
                .post_image {
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background: black;
                    border-radius: 0.4rem;
                    height: 9rem;
                    width: 100%;
                    img {
                        border-radius: 0.4rem;
                        text-align: center;
                        max-width: 100%;
                        max-height: 9rem;
                    }
                }
                .post_title {
                    width: 100%;
                    text-align: left;
                    padding-left: 0.6rem;
                    padding-right: 0.8rem;
                    padding-top: 0.5rem;
                    font-size: 0.75rem;
                    height: 3.5rem;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                }
                .post_like {
                    width: calc(100% - 0.8rem);
                    height: 1.5rem;
                    text-align: left;
                    font-size: 0.7rem;
                    padding-left: 0rem;
                    .post_like_item {
                        margin-right: 0.4rem;
                        color: #757575;
                        padding-right: 0.1rem;
                    }
                }
            }
        }
    }
}
</style>
