import BaseEventHandler from './BaseEventHandler'
import { parseServerInfo, getBaseUrl } from '../common_base'
import Tool from '@/common/tool.js'
import permissionManager from '@/common/permission/index.js'
/**
 * 服务器信息事件处理器
 * 处理服务器配置相关的事件
 */
class ServerInfoEventHandler extends BaseEventHandler {
    /**
     * 构造函数
     * @param {Object} vueInstance Vue实例
     * @param {Object} autoLoginManager 自动登录管理器
     * @param {Object} mainScreenManager 主屏幕管理器
     * @param {Object} eventListenerManager 事件监听管理器
     */
    constructor(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager) {
        super(vueInstance, autoLoginManager, mainScreenManager, eventListenerManager)

        this.isFirstLoadServerInfo = false
    }
    /**
     * 初始化服务器信息相关事件
     * @param {Object} controller 控制器
     */
    initEvents(controller) {
        controller.on("server_info", (data) => {
            let json = parseServerInfo(data)
            this.vm.$store.commit('systemConfig/updateSystemConfig', {
                serverInfo: json
            })

            if (json.network_environment === 1 && json.storageReplaceInfo.replace) {
                this.observeImageLoad(json)
            }

            window.CWorkstationCommunicationMng.initServerConfig(json)
            this.initUserConfig2App()

            if (!this.isFirstLoadServerInfo) {
                this.isFirstLoadServerInfo = true
                this.vm.sendSyncAccountOrLiveToULinker()
                this.checkAutoEnterTvWall()

                if (Tool.checkAppClient('Cef')) {
                    this.queryIStationInfo_DR()
                    this.setWhiteBoardUrl()
                    Tool.initNativeAgoraSdk(json.agora_appid).then(async () => {
                        setTimeout(() => {
                            this.ifNeedAutoPushStream()
                        }, 1500)
                    })
                }
            }

            setTimeout(() => {
                this.checkAndSetPrivacyAgreement()
            }, 100)
        })
    }

    /**
     * 观察图片加载
     * @param {Object} json 服务器信息
     */
    observeImageLoad(json) {
        const fallbackImageUrl = 'static/resource_pc/images/slt_err.png';
        Tool.observeImageLoad(fallbackImageUrl)
    }

    /**
     * 初始化用户配置到应用
     */
    initUserConfig2App() {
        var user_config = {
            auto_upload: 0
        };
        if (this.vm.user.preferences && this.vm.user.preferences.auto_upload) {
            user_config.auto_upload = 1;
        }
        console.log('--')
        console.log('upload_config', user_config)
        console.log('--')
        window.CWorkstationCommunicationMng.initUserConfig(user_config);
    }

    /**
     * 查询IStation信息
     */
    queryIStationInfo_DR() {
        Tool.createCWorkstationCommunicationMng({
            name: 'queryIStationInfo_DR',
            emitName: 'IStationInfo_DR',
            params: {},
            timeout: 5000,
        }).then((data) => {
            window.enable_istation = data.Show;
            this.vm.$store.commit('globalParams/updateGlobalIstationInfo', data)
            this.vm.$store.commit('device/updateDeviceInfo', {
                isIStationInfoDR: true,
            })
        })
        window.CWorkstationCommunicationMng.RequestDrConnectStatus()
    }

    /**
     * 设置白板URL
     */
    setWhiteBoardUrl() {
        if (Tool.checkAppClient('Cef')) {
            let language = window.localStorage.getItem('lang')
            window.CWorkstationCommunicationMng.SetWhiteBoardUrl({
                url: window.location.href.includes('localhost') ?
                    `http://localhost:8888/whiteboard.html#/index?language=${language}`
                    : Tool.transferLocationToCe(`${getBaseUrl()}/whiteboard/whiteboard.html#/index?language=${language}`)
            })
        }
    }

    /**
     * 检查自动进入电视墙
     */
    checkAutoEnterTvWall() {
        if (this.vm.user.preferences.auto_enter_tv_wall) {
            if (permissionManager.checkPermission({regionPermissionKey:'tvwall'}) && this.vm.user.role > 1 && !this.vm.isWorkStation) {
                if (location.href.includes('localhost')) {
                    return
                }
                this.vm.$root.eventBus.$emit('enterTVmode')
            }
        }
    }

    /**
     * 检查是否需要自动推流
     */
    ifNeedAutoPushStream() {
        let odata = localStorage.getItem('auto_push_stream_' + this.vm.user.id);
        if (odata) {
            let data = JSON.parse(odata)
            let isAutoPushStream = data.enable
            let lastPushStreamCid = data.value
            if (isAutoPushStream && lastPushStreamCid) {
                this.requestConversationToStartUltrasoundDesktopByAutoPushStream(data)
            }
        }
        this.vm.$store.commit('liveConference/updateConferenceValue', {
            autoPushReady: true,
        })
    }

    /**
     * 请求会话开始超声桌面自动推流
     * @param {Object} data 数据
     */
    requestConversationToStartUltrasoundDesktopByAutoPushStream(data) {
        var auto_push_stream = this.vm.$store.state.globalParams.auto_push_stream;

        if (!permissionManager.checkPermission({regionPermissionKey:'live'})
             ||!auto_push_stream
             || !auto_push_stream.enable
             || !auto_push_stream.value_type
             || !auto_push_stream.value) {
            return;
        }

        if ("Conversation" == auto_push_stream.value_type && 0 < auto_push_stream.value) {
            var cid = auto_push_stream.value;
            this.requestGroupToStartUltrasoundDesktopByAutoPushStream({cid:cid, conversation:data});
        } else if ("FriendId" == auto_push_stream.value_type && 0 < auto_push_stream.value) {
            var fid = auto_push_stream.value;
            this.requestFriendToStartUltrasoundDesktopByAutoPushStream({fid:fid, conversation:data});
        }
    }

    /**
     * 请求群组开始超声桌面自动推流
     * @param {Object} data 数据
     */
    requestGroupToStartUltrasoundDesktopByAutoPushStream(data) {
        let cid = 0;
        let conversation = null;
        if (0 == cid) {
            this.vm.openConversation(data.cid,2,null,(is_suc)=>{
                var input_data={
                    gid:data.cid,
                    record_mode:data.conversation.record_mode?1:0
                }
                let newConversation = this.vm.$store.state.conversationList[data.cid]
                newConversation.socket.emit('edit_record_mode',input_data,function(is_succ,data){
                    if(is_succ){
                        //修改成功
                        this.vm.$store.commit('conversationList/updateIsLiveRecord',{
                            cid:input_data.gid,
                            record_mode:input_data.record_mode
                        });
                    }else{//修改失败
                    }
                    console.log('requestGroupToStartUltrasoundDesktopByAutoPushStream:', this.vm.$store.state.conversationList[input_data.gid])
                    this.startUltrasoundDesktopByAutoPushStream();
                })
                // is_suc&&this.startUltrasoundDesktopByAutoPushStream(conversation);
            });
        } else {
            this.vm.openConversation(cid,2,null,()=>{
                this.startUltrasoundDesktopByAutoPushStream();
            });
        }
    }

    /**
     * 请求好友开始超声桌面自动推流
     * @param {Object} data 数据
     */
    requestFriendToStartUltrasoundDesktopByAutoPushStream(data){
        console.log('requestFriendToStartUltrasoundDesktopByAutoPushStream',data)
        let cid = 0;
        if (0 == cid) {
            this.vm.openConversation(data.fid,3,null,(is_suc)=>{
                var input_data={
                    gid:data.cid,
                    record_mode:data.conversation.record_mode?1:0
                }
                this.vm.$root.socket.emit('edit_record_mode',input_data,function(is_succ,data){
                    if(is_succ){
                        //修改成功
                        this.vm.$store.commit('conversationList/updateIsLiveRecord',{
                            cid:input_data.gid,
                            record_mode:input_data.record_mode
                        });
                    }else{//修改失败
                    }
                    this.startUltrasoundDesktopByAutoPushStream();
                })
                // is_suc&&this.startUltrasoundDesktopByAutoPushStream();
            });
        } else {
            this.vm.openConversation(cid,2,null,()=>{
                this.startUltrasoundDesktopByAutoPushStream();
            });
        }

    }

    /**
     * 开始超声桌面自动推流
     */
    startUltrasoundDesktopByAutoPushStream(){
        if(!this.vm.$root.currentLiveCid){
            setTimeout(()=>{
                this.vm.$root.eventBus.$emit('chatWindowStartJoinRoom',{main:1,aux:1,videoSource:'doppler',isSender:1,autoPushStream:true});
            },1000)
        }
    }

    /**
     * 检查并自动设置隐私协议状态
     */
    checkAndSetPrivacyAgreement() {
        const serverType = localStorage.getItem('serverType') || '云++';
        const privacyStatus = JSON.parse(localStorage.getItem('isAgreePrivacyPolicy') || "{}");
        const privacy_version = this.vm.$store.state.systemConfig.envConfig &&
            this.vm.$store.state.systemConfig.envConfig.privacy_agreement_version;

        // 获取用户已同意的版本号
        const agreedVersion = privacyStatus[serverType];

        // 检查是否没有同意记录或记录为空（表示曾经撤销过）
        const hasNoAgreement = !agreedVersion || agreedVersion === '' || agreedVersion === 0;

        if (hasNoAgreement && privacy_version) {
            console.log('检测到用户没有隐私协议同意记录，自动设置为已同意状态');

            // 自动设置为已同意
            privacyStatus[serverType] = privacy_version;
            localStorage.setItem('isAgreePrivacyPolicy', JSON.stringify(privacyStatus));

            // 向客户端通知隐私协议状态
            if (window.CWorkstationCommunicationMng && window.CWorkstationCommunicationMng.setPrivacyPolicyStatus) {
                window.CWorkstationCommunicationMng.setPrivacyPolicyStatus({
                    status: 1,
                    version: privacy_version
                });
            }

            console.log('隐私协议状态已自动设置为已同意，版本号:', privacy_version);
        } else if (!privacy_version) {
            console.log('等待获取隐私协议版本号...');
        } else {
            console.log('用户已有隐私协议同意记录，版本号:', agreedVersion);
        }
    }
}

export default ServerInfoEventHandler
